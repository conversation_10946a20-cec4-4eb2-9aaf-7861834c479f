<!DOCTYPE html>
<html>
<head>
    <title>创建扩展图标</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas32" width="32" height="32" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="downloadIcons()">下载图标</button>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制AI图标 (简化的机器人头像)
            ctx.fillStyle = 'white';
            
            // 机器人眼睛
            const eyeSize = size * 0.08;
            const eyeY = size * 0.35;
            ctx.beginPath();
            ctx.arc(size * 0.35, eyeY, eyeSize, 0, 2 * Math.PI);
            ctx.arc(size * 0.65, eyeY, eyeSize, 0, 2 * Math.PI);
            ctx.fill();
            
            // 机器人嘴巴
            ctx.strokeStyle = 'white';
            ctx.lineWidth = size * 0.04;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.arc(size/2, size * 0.55, size * 0.15, 0, Math.PI);
            ctx.stroke();
            
            // 机器人天线
            ctx.beginPath();
            ctx.moveTo(size/2, size * 0.15);
            ctx.lineTo(size/2, size * 0.05);
            ctx.stroke();
            
            // 天线顶部
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(size/2, size * 0.05, size * 0.03, 0, 2 * Math.PI);
            ctx.fill();
        }

        // 创建所有尺寸的图标
        createIcon(document.getElementById('canvas16'), 16);
        createIcon(document.getElementById('canvas32'), 32);
        createIcon(document.getElementById('canvas48'), 48);
        createIcon(document.getElementById('canvas128'), 128);

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadIcons() {
            downloadCanvas(document.getElementById('canvas16'), 'icon16.png');
            setTimeout(() => downloadCanvas(document.getElementById('canvas32'), 'icon32.png'), 100);
            setTimeout(() => downloadCanvas(document.getElementById('canvas48'), 'icon48.png'), 200);
            setTimeout(() => downloadCanvas(document.getElementById('canvas128'), 'icon128.png'), 300);
        }
    </script>
</body>
</html>