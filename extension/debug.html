<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI工作台 - 功能调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .debug-section h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .result {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .warning { background: #fef3c7; color: #92400e; }
    </style>
</head>
<body>
    <h1>🔧 AI工作台功能调试</h1>
    <p>此页面用于测试和调试扩展的核心功能</p>
    
    <div class="debug-section">
        <h3>📋 标签页管理测试</h3>
        <button class="test-button" onclick="testGetCurrentTabs()">获取当前标签页</button>
        <button class="test-button" onclick="testTabPermissions()">检查标签页权限</button>
        <div id="tabsResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>🏠 工作空间管理测试</h3>
        <button class="test-button" onclick="testGetWorkspaces()">获取工作空间</button>
        <button class="test-button" onclick="testCreateWorkspace()">创建测试工作空间</button>
        <button class="test-button" onclick="testInitializeStorage()">初始化存储</button>
        <div id="workspacesResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>🤖 AI功能测试</h3>
        <button class="test-button" onclick="testAIConfig()">检查AI配置</button>
        <button class="test-button" onclick="testAIAssistant()">测试AI助手</button>
        <div id="aiResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>💾 存储功能测试</h3>
        <button class="test-button" onclick="testStoragePermissions()">检查存储权限</button>
        <button class="test-button" onclick="testDataExport()">测试数据导出</button>
        <button class="test-button" onclick="testStorageUsage()">检查存储使用情况</button>
        <div id="storageResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>🔄 同步功能测试</h3>
        <button class="test-button" onclick="testLocalSync()">测试本地同步</button>
        <button class="test-button" onclick="testSyncConfig()">检查同步配置</button>
        <div id="syncResult" class="result"></div>
    </div>

    <script>
        // 检查Chrome扩展环境
        function checkExtensionEnvironment() {
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                return false;
            }
            return true;
        }
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            element.textContent += logMessage;
            element.className = `result ${type}`;
            element.scrollTop = element.scrollHeight;
        }
        
        // 标签页测试
        async function testGetCurrentTabs() {
            try {
                if (!checkExtensionEnvironment()) {
                    log('tabsResult', '错误: 未在Chrome扩展环境中运行', 'error');
                    return;
                }
                
                log('tabsResult', '正在获取当前标签页...', 'info');
                const tabs = await chrome.tabs.query({ currentWindow: true });
                log('tabsResult', `成功获取 ${tabs.length} 个标签页`, 'success');
                log('tabsResult', JSON.stringify(tabs.map(t => ({ id: t.id, title: t.title, url: t.url })), null, 2), 'info');
            } catch (error) {
                log('tabsResult', `错误: ${error.message}`, 'error');
            }
        }
        
        async function testTabPermissions() {
            try {
                const permissions = await chrome.permissions.getAll();
                log('tabsResult', '当前权限: ' + JSON.stringify(permissions, null, 2), 'info');
            } catch (error) {
                log('tabsResult', `权限检查失败: ${error.message}`, 'error');
            }
        }
        
        // 工作空间测试
        async function testGetWorkspaces() {
            try {
                log('workspacesResult', '正在获取工作空间...', 'info');
                const result = await chrome.storage.sync.get(['workspaces']);
                const workspaces = result.workspaces || [];
                log('workspacesResult', `找到 ${workspaces.length} 个工作空间`, 'success');
                log('workspacesResult', JSON.stringify(workspaces, null, 2), 'info');
            } catch (error) {
                log('workspacesResult', `错误: ${error.message}`, 'error');
            }
        }
        
        async function testCreateWorkspace() {
            try {
                const testWorkspace = {
                    id: 'test_' + Date.now(),
                    name: '测试工作空间',
                    description: '调试用工作空间',
                    color: '#3B82F6',
                    icon: '🧪',
                    groups: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isDefault: false
                };
                
                const result = await chrome.storage.sync.get(['workspaces']);
                const workspaces = result.workspaces || [];
                workspaces.push(testWorkspace);
                
                await chrome.storage.sync.set({ workspaces });
                log('workspacesResult', '测试工作空间创建成功', 'success');
                log('workspacesResult', JSON.stringify(testWorkspace, null, 2), 'info');
            } catch (error) {
                log('workspacesResult', `创建工作空间失败: ${error.message}`, 'error');
            }
        }
        
        async function testInitializeStorage() {
            try {
                log('workspacesResult', '正在初始化存储...', 'info');
                
                // 检查是否有默认工作空间
                const result = await chrome.storage.sync.get(['workspaces']);
                const workspaces = result.workspaces || [];
                
                if (workspaces.length === 0) {
                    const defaultWorkspace = {
                        id: 'default_' + Date.now(),
                        name: '默认工作空间',
                        description: '系统默认工作空间',
                        color: '#3B82F6',
                        icon: '🏠',
                        groups: [],
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                        isDefault: true
                    };
                    
                    await chrome.storage.sync.set({ workspaces: [defaultWorkspace] });
                    log('workspacesResult', '默认工作空间已创建', 'success');
                } else {
                    log('workspacesResult', '工作空间已存在，无需初始化', 'warning');
                }
            } catch (error) {
                log('workspacesResult', `初始化失败: ${error.message}`, 'error');
            }
        }
        
        // AI功能测试
        async function testAIConfig() {
            try {
                log('aiResult', '正在检查AI配置...', 'info');
                const result = await chrome.storage.sync.get(['aiConfig']);
                const config = result.aiConfig;
                
                if (config) {
                    log('aiResult', 'AI配置已存在', 'success');
                    log('aiResult', JSON.stringify(config, null, 2), 'info');
                } else {
                    log('aiResult', 'AI配置不存在，需要配置', 'warning');
                }
            } catch (error) {
                log('aiResult', `AI配置检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testAIAssistant() {
            log('aiResult', 'AI助手功能需要在扩展popup中测试', 'warning');
        }
        
        // 存储功能测试
        async function testStoragePermissions() {
            try {
                log('storageResult', '正在测试存储权限...', 'info');
                
                // 测试sync存储
                await chrome.storage.sync.set({ test: 'sync_test' });
                const syncResult = await chrome.storage.sync.get(['test']);
                log('storageResult', 'Sync存储测试: ' + (syncResult.test === 'sync_test' ? '成功' : '失败'), 
                    syncResult.test === 'sync_test' ? 'success' : 'error');
                
                // 测试local存储
                await chrome.storage.local.set({ test: 'local_test' });
                const localResult = await chrome.storage.local.get(['test']);
                log('storageResult', 'Local存储测试: ' + (localResult.test === 'local_test' ? '成功' : '失败'),
                    localResult.test === 'local_test' ? 'success' : 'error');
                
                // 清理测试数据
                await chrome.storage.sync.remove(['test']);
                await chrome.storage.local.remove(['test']);
                
            } catch (error) {
                log('storageResult', `存储权限测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testDataExport() {
            try {
                log('storageResult', '正在测试数据导出...', 'info');
                const allData = await chrome.storage.sync.get(null);
                log('storageResult', '数据导出成功', 'success');
                log('storageResult', `导出数据大小: ${JSON.stringify(allData).length} 字符`, 'info');
            } catch (error) {
                log('storageResult', `数据导出失败: ${error.message}`, 'error');
            }
        }
        
        async function testStorageUsage() {
            try {
                log('storageResult', '正在检查存储使用情况...', 'info');
                const usage = await chrome.storage.sync.getBytesInUse();
                log('storageResult', `Sync存储使用: ${usage} 字节`, 'info');
                
                const localUsage = await chrome.storage.local.getBytesInUse();
                log('storageResult', `Local存储使用: ${localUsage} 字节`, 'info');
            } catch (error) {
                log('storageResult', `存储使用情况检查失败: ${error.message}`, 'error');
            }
        }
        
        // 同步功能测试
        async function testLocalSync() {
            try {
                log('syncResult', '正在测试本地同步...', 'info');
                const result = await chrome.storage.sync.get(['localSyncConfig']);
                const config = result.localSyncConfig;
                
                if (config) {
                    log('syncResult', '本地同步配置已存在', 'success');
                    log('syncResult', JSON.stringify(config, null, 2), 'info');
                } else {
                    log('syncResult', '本地同步配置不存在', 'warning');
                }
            } catch (error) {
                log('syncResult', `本地同步测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testSyncConfig() {
            try {
                log('syncResult', '正在检查同步配置...', 'info');
                const allSyncData = await chrome.storage.sync.get(null);
                const syncKeys = Object.keys(allSyncData);
                log('syncResult', `同步存储中的键: ${syncKeys.join(', ')}`, 'info');
            } catch (error) {
                log('syncResult', `同步配置检查失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时检查环境
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkExtensionEnvironment()) {
                document.body.innerHTML = `
                    <div style="text-align: center; padding: 50px;">
                        <h2>⚠️ 环境错误</h2>
                        <p>此页面需要在Chrome扩展环境中运行</p>
                        <p>请将此文件添加到扩展的dist目录，然后在扩展中访问</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
