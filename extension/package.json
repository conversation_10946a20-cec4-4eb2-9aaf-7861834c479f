{"name": "ai-workspace-extension", "version": "1.0.0", "description": "AI工作台Chrome扩展前端", "private": true, "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "test": "jest", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@types/chrome": "^0.0.260", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.8.0", "typescript": "^5.3.3", "webpack": "^5.90.1", "webpack-cli": "^5.1.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "testMatch": ["<rootDir>/tests/**/*.test.ts", "<rootDir>/tests/**/*.test.tsx"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/types/**/*"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}