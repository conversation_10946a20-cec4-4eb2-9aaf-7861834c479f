<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>工作区管理</title>
  <style>
    /* Material Design 3.0 基础样式 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    :root {
      /* Material Design 3.0 色彩系统 */
      --md-sys-color-primary: #6750A4;
      --md-sys-color-on-primary: #FFFFFF;
      --md-sys-color-primary-container: #EADDFF;
      --md-sys-color-on-primary-container: #21005D;

      --md-sys-color-secondary: #625B71;
      --md-sys-color-on-secondary: #FFFFFF;
      --md-sys-color-secondary-container: #E8DEF8;
      --md-sys-color-on-secondary-container: #1D192B;

      --md-sys-color-surface: #FEF7FF;
      --md-sys-color-on-surface: #1D1B20;
      --md-sys-color-surface-variant: #E7E0EC;
      --md-sys-color-on-surface-variant: #49454F;

      --md-sys-color-outline: #79747E;
      --md-sys-color-outline-variant: #CAC4D0;

      --md-sys-color-error: #BA1A1A;
      --md-sys-color-on-error: #FFFFFF;
      --md-sys-color-error-container: #FFDAD6;
      --md-sys-color-on-error-container: #410002;

      /* 字体系统 */
      --md-sys-typescale-display-large-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-display-large-size: 57px;
      --md-sys-typescale-display-large-weight: 400;
      --md-sys-typescale-display-large-line-height: 64px;

      --md-sys-typescale-headline-medium-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-headline-medium-size: 28px;
      --md-sys-typescale-headline-medium-weight: 400;
      --md-sys-typescale-headline-medium-line-height: 36px;

      --md-sys-typescale-title-large-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-title-large-size: 22px;
      --md-sys-typescale-title-large-weight: 400;
      --md-sys-typescale-title-large-line-height: 28px;

      --md-sys-typescale-body-large-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-body-large-size: 16px;
      --md-sys-typescale-body-large-weight: 400;
      --md-sys-typescale-body-large-line-height: 24px;

      --md-sys-typescale-body-medium-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-body-medium-size: 14px;
      --md-sys-typescale-body-medium-weight: 400;
      --md-sys-typescale-body-medium-line-height: 20px;

      --md-sys-typescale-label-large-font: 'Roboto', 'Noto Sans SC', sans-serif;
      --md-sys-typescale-label-large-size: 14px;
      --md-sys-typescale-label-large-weight: 500;
      --md-sys-typescale-label-large-line-height: 20px;

      /* 形状系统 */
      --md-sys-shape-corner-none: 0px;
      --md-sys-shape-corner-extra-small: 4px;
      --md-sys-shape-corner-small: 8px;
      --md-sys-shape-corner-medium: 12px;
      --md-sys-shape-corner-large: 16px;
      --md-sys-shape-corner-extra-large: 28px;

      /* 阴影系统 */
      --md-sys-elevation-level0: none;
      --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
      --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
      --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
      --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
      --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
    }

    body {
      font-family: var(--md-sys-typescale-body-medium-font);
      font-size: var(--md-sys-typescale-body-medium-size);
      font-weight: var(--md-sys-typescale-body-medium-weight);
      line-height: var(--md-sys-typescale-body-medium-line-height);
      background: var(--md-sys-color-surface);
      color: var(--md-sys-color-on-surface);
      height: 100vh;
      overflow: hidden;
      width: 100%;
      min-width: 320px;
      max-width: 400px;
    }

    .workspace-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      width: 100%;
      overflow: hidden;
      background: var(--md-sys-color-surface);
    }

    /* Material Design 3.0 顶部应用栏 */
    .workspace-header {
      background: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
      padding: 16px;
      box-shadow: var(--md-sys-elevation-level2);
      border-radius: 0 0 var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large);
      position: relative;
      z-index: 2;
    }

    .current-workspace {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .workspace-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .workspace-icon {
      font-size: 24px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);
      border-radius: var(--md-sys-shape-corner-medium);
      box-shadow: var(--md-sys-elevation-level1);
    }

    .workspace-name {
      font-family: var(--md-sys-typescale-title-large-font);
      font-size: var(--md-sys-typescale-title-large-size);
      font-weight: var(--md-sys-typescale-title-large-weight);
      line-height: var(--md-sys-typescale-title-large-line-height);
      color: var(--md-sys-color-on-primary-container);
      margin-bottom: 2px;
    }

    .workspace-stats {
      font-family: var(--md-sys-typescale-body-medium-font);
      font-size: 12px;
      color: var(--md-sys-color-on-surface-variant);
      opacity: 0.8;
    }

    .workspace-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    /* Material Design 3.0 按钮系统 */
    .btn {
      padding: 10px 24px;
      border: none;
      border-radius: var(--md-sys-shape-corner-large);
      font-family: var(--md-sys-typescale-label-large-font);
      font-size: var(--md-sys-typescale-label-large-size);
      font-weight: var(--md-sys-typescale-label-large-weight);
      line-height: var(--md-sys-typescale-label-large-line-height);
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.2, 0.0, 0, 1.0);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      min-height: 40px;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: currentColor;
      opacity: 0;
      transition: opacity 0.2s cubic-bezier(0.2, 0.0, 0, 1.0);
    }

    .btn:hover::before {
      opacity: 0.08;
    }

    .btn:active::before {
      opacity: 0.12;
    }

    /* Filled Button (Primary) */
    .btn-primary {
      background: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);
      box-shadow: var(--md-sys-elevation-level0);
    }

    .btn-primary:hover {
      box-shadow: var(--md-sys-elevation-level1);
    }

    /* Filled Tonal Button (Secondary) */
    .btn-secondary {
      background: var(--md-sys-color-secondary-container);
      color: var(--md-sys-color-on-secondary-container);
      box-shadow: var(--md-sys-elevation-level0);
    }

    .btn-secondary:hover {
      box-shadow: var(--md-sys-elevation-level1);
    }

    /* Error Button */
    .btn-danger {
      background: var(--md-sys-color-error);
      color: var(--md-sys-color-on-error);
      box-shadow: var(--md-sys-elevation-level0);
    }

    .btn-danger:hover {
      box-shadow: var(--md-sys-elevation-level1);
    }

    /* Small Button Variant */
    .btn-small {
      padding: 4px 8px;
      min-height: 28px;
      font-size: 11px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 60px;
    }

    /* Material Design 3.0 状态指示器 */
    #connection-status {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      transition: all 0.3s cubic-bezier(0.2, 0.0, 0, 1.0);
      position: relative;
    }

    .connection-healthy {
      background-color: #4CAF50;
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
    }

    .connection-healthy::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid rgba(76, 175, 80, 0.3);
      border-radius: 50%;
      animation: ripple 2s infinite;
    }

    .connection-unhealthy {
      background-color: var(--md-sys-color-error);
      box-shadow: 0 0 8px rgba(186, 26, 26, 0.4);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
        transform: scale(1);
      }

      50% {
        opacity: 0.7;
        transform: scale(1.1);
      }
    }

    @keyframes ripple {
      0% {
        transform: scale(1);
        opacity: 1;
      }

      100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }

    /* Material Design 3.0 主要内容区域 */
    .workspace-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      background: var(--md-sys-color-surface);
    }

    .section {
      margin-bottom: 24px;
    }

    .section-title {
      font-family: var(--md-sys-typescale-title-large-font);
      font-size: 18px;
      font-weight: var(--md-sys-typescale-title-large-weight);
      color: var(--md-sys-color-on-surface);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    /* Material Design 3.0 工作区列表 */
    .workspace-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .workspace-item {
      background: var(--md-sys-color-surface-variant);
      border: 1px solid var(--md-sys-color-outline-variant);
      border-radius: var(--md-sys-shape-corner-medium);
      padding: 12px;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.2, 0.0, 0, 1.0);
      display: flex;
      flex-direction: column;
      gap: 8px;
      position: relative;
      overflow: hidden;
      min-height: 80px;
    }

    .workspace-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--md-sys-color-primary);
      opacity: 0;
      transition: opacity 0.2s cubic-bezier(0.2, 0.0, 0, 1.0);
    }

    .workspace-item-main {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      position: relative;
      z-index: 1;
    }

    .workspace-item:hover::before {
      opacity: 0.08;
    }

    .workspace-item:hover {
      border-color: var(--md-sys-color-primary);
      box-shadow: var(--md-sys-elevation-level1);
    }

    .workspace-item.active {
      border-color: var(--md-sys-color-primary);
      background: var(--md-sys-color-primary-container);
      color: var(--md-sys-color-on-primary-container);
      box-shadow: var(--md-sys-elevation-level2);
    }

    .workspace-item-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .workspace-item-icon {
      font-size: 20px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--md-sys-shape-corner-small);
      background: var(--md-sys-color-secondary-container);
      color: var(--md-sys-color-on-secondary-container);
    }

    .workspace-item.active .workspace-item-icon {
      background: var(--md-sys-color-primary);
      color: var(--md-sys-color-on-primary);
    }

    .workspace-item-details {
      flex: 1;
    }

    .workspace-item-name {
      font-family: var(--md-sys-typescale-body-large-font);
      font-size: 14px;
      font-weight: 500;
      color: var(--md-sys-color-on-surface-variant);
      margin-bottom: 4px;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 180px;
    }

    .workspace-item.active .workspace-item-name {
      color: var(--md-sys-color-on-primary-container);
    }

    .workspace-item-count {
      font-size: 12px;
      color: var(--md-sys-color-on-surface-variant);
      opacity: 0.7;
    }

    .workspace-item.active .workspace-item-count {
      color: var(--md-sys-color-on-primary-container);
    }

    .workspace-item-actions {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
      min-width: 120px;
    }

    /* 标签页管理 */
    .tab-groups {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .tab-section {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      overflow: hidden;
    }

    .tab-section-header {
      background: #f8fafc;
      padding: 10px 12px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tab-section-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab-section-icon {
      font-size: 14px;
    }

    .tab-section-name {
      font-size: 13px;
      font-weight: 500;
      color: #374151;
    }

    .tab-section-count {
      font-size: 11px;
      color: #6b7280;
      background: #e5e7eb;
      padding: 2px 6px;
      border-radius: 10px;
    }

    /* 分组相关样式已移除，改用固定状态管理 */

    .tab-list {
      max-height: 200px;
      overflow-y: auto;
      transition: max-height 0.3s ease, opacity 0.3s ease;
    }

    .tab-list.collapsed {
      max-height: 0;
      opacity: 0;
      overflow: hidden;
      overflow-y: auto;
    }

    .tab-item {
      padding: 8px 12px;
      border-bottom: 1px solid #f1f5f9;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .tab-item:hover {
      background: #f8fafc;
    }

    .tab-pinned-indicator {
      position: absolute;
      top: 4px;
      right: 4px;
      font-size: 10px;
      opacity: 0.7;
    }

    .tab-actions {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-left: auto;
    }

    .tab-pin-btn {
      background: none;
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      padding: 4px 6px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 24px;
      height: 24px;
    }

    .tab-pin-btn:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }

    .tab-pin-btn.pinned {
      background: #fef3c7;
      border-color: #fbbf24;
      color: #d97706;
    }

    .tab-pin-btn.pinned:hover {
      background: #fde68a;
      border-color: #f59e0b;
    }

    .pin-icon {
      transition: transform 0.2s ease;
    }

    .tab-pin-btn:active .pin-icon {
      transform: scale(0.9);
    }

    /* 工作区添加面板样式 */
    .workspace-add-panel {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      margin-top: 12px;
      overflow: hidden;
      animation: slideDown 0.3s ease;
      width: 100%;
      box-sizing: border-box;
      position: relative;
    }

    @keyframes slideDown {
      from {
        max-height: 0;
        opacity: 0;
      }

      to {
        max-height: 300px;
        opacity: 1;
      }
    }

    .add-panel-header {
      background: #f1f5f9;
      padding: 12px 16px;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .add-panel-header h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }

    .add-panel-close {
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      color: #6b7280;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .add-panel-close:hover {
      color: #374151;
    }

    .add-panel-content {
      padding: 16px;
    }

    .add-method-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .add-method-btn {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
      width: 100%;
      box-sizing: border-box;
      font-size: 14px;
    }

    .add-method-btn:hover {
      background: #f0f9ff;
      border-color: #3b82f6;
    }

    .method-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .method-text {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }

    .tab-item:last-child {
      border-bottom: none;
    }

    .tab-favicon {
      width: 16px;
      height: 16px;
      border-radius: 2px;
      flex-shrink: 0;
    }

    .tab-info {
      flex: 1;
      min-width: 0;
    }

    .tab-title {
      font-size: 12px;
      color: #1e293b;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 2px;
    }

    .tab-url {
      font-size: 10px;
      color: #64748b;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 添加标签页区域 */
    .add-tab-section {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
    }

    .add-tab-methods {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .add-method {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .add-method:hover {
      border-color: #3b82f6;
      background: #f8fafc;
    }

    .add-method-icon {
      font-size: 16px;
      width: 24px;
      text-align: center;
    }

    .add-method-text {
      flex: 1;
      font-size: 13px;
      color: #374151;
    }

    /* AI自动分组 */
    .ai-grouping {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #0ea5e9;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
    }

    .ai-grouping-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .ai-grouping-title {
      font-size: 14px;
      font-weight: 600;
      color: #0c4a6e;
      margin-bottom: 4px;
    }

    .ai-grouping-desc {
      font-size: 12px;
      color: #0369a1;
      margin-bottom: 12px;
    }

    .btn-ai {
      background: #0ea5e9;
      color: white;
      border: none;
    }

    .btn-ai:hover {
      background: #0284c7;
    }

    /* 加载状态 */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #6b7280;
    }

    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #e5e7eb;
      border-top: 2px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 响应式设计 */
    @media (max-width: 350px) {
      .workspace-header {
        padding: 10px;
      }

      .workspace-content {
        padding: 10px;
      }

      .workspace-item {
        padding: 8px;
        min-height: 70px;
      }

      .workspace-item-name {
        font-size: 13px;
        max-width: 140px;
      }

      .workspace-item-actions {
        min-width: 100px;
        gap: 4px;
      }

      .btn-small {
        padding: 3px 6px;
        font-size: 10px;
        max-width: 50px;
      }

      .section-title {
        font-size: 16px;
      }

      .current-workspace {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .workspace-actions {
        align-self: flex-end;
      }
    }
  </style>
</head>

<body>
  <div class="workspace-container">
    <!-- 顶部工作区选择器 -->
    <div class="workspace-header">
      <div class="current-workspace">
        <div class="workspace-info">
          <span class="workspace-icon" id="current-workspace-icon">🏠</span>
          <div>
            <div class="workspace-name" id="current-workspace-name">默认工作区</div>
            <div class="workspace-stats" id="current-workspace-stats">0 个标签页</div>
          </div>
        </div>
        <div class="workspace-actions">
          <div id="connection-status" class="connection-healthy" title="连接状态"></div>
          <button class="btn btn-secondary btn-small" id="cloud-sync-btn" title="云同步">
            ☁️ 同步
          </button>
          <button class="btn btn-primary btn-small" id="new-workspace-btn">
            ➕ 新建
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="workspace-content">
      <!-- 工作区列表 -->
      <div class="section">
        <div class="section-title">
          工作区列表
          <div style="display: flex; gap: 4px;">
            <button class="btn btn-secondary btn-small" id="sync-status-btn" title="同步状态">
              📊 状态
            </button>
            <button class="btn btn-secondary btn-small" id="refresh-workspaces-btn">
              🔄 刷新
            </button>
          </div>
        </div>
        <div class="workspace-list" id="workspace-list">
          <div class="loading">
            <div class="loading-spinner"></div>
            加载中...
          </div>
        </div>
      </div>

      <!-- 当前工作区标签页 -->
      <div class="section">
        <div class="section-title">
          当前标签页
          <div style="display: flex; gap: 4px;">
            <button class="btn btn-secondary btn-small" id="verify-pinning-btn" title="验证固定状态">
              📌 验证
            </button>
            <button class="btn btn-secondary btn-small" id="force-apply-pinning-btn" title="强制应用固定设置">
              🔧 修复
            </button>
            <button class="btn btn-secondary btn-small" id="refresh-tabs-btn">
              🔄 刷新
            </button>
          </div>
        </div>
        <div class="tab-groups" id="tab-groups">
          <div class="loading">
            <div class="loading-spinner"></div>
            加载中...
          </div>
        </div>
      </div>

      <!-- AI自动分组 -->
      <div class="section">
        <div class="ai-grouping">
          <div class="ai-grouping-icon">🤖</div>
          <div class="ai-grouping-title">AI智能分组</div>
          <div class="ai-grouping-desc">让AI帮您自动整理标签页</div>
          <button class="btn btn-ai" id="ai-group-btn">
            ✨ 开始分组
          </button>
        </div>
      </div>

      <!-- 添加标签页 -->
      <div class="section">
        <div class="section-title">添加标签页</div>
        <div class="add-tab-section">
          <div class="add-tab-methods">
            <div class="add-method" id="add-url-method">
              <span class="add-method-icon">🌐</span>
              <span class="add-method-text">输入网址</span>
            </div>
            <div class="add-method" id="add-bookmark-method">
              <span class="add-method-icon">⭐</span>
              <span class="add-method-text">从书签选择</span>
            </div>
            <div class="add-method" id="add-current-method">
              <span class="add-method-icon">📋</span>
              <span class="add-method-text">当前标签页</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="workspace-manager.js"></script>
</body>

</html>