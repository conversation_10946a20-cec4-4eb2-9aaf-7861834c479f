import { PresetGroup, PresetWebsite } from '../types';

// AI工作主力网站
const aiPrimaryWebsites: PresetWebsite[] = [
  {
    name: 'ChatGPT',
    url: 'https://chat.openai.com/',
    description: 'OpenAI的ChatGPT对话AI，最强大的通用AI助手',
    tags: ['ai', 'chat', 'openai', 'gpt'],
    favicon: 'https://chat.openai.com/favicon.ico'
  },
  {
    name: 'Gemini',
    url: 'https://gemini.google.com/',
    description: 'Google的Gemini AI助手，支持多模态交互',
    tags: ['ai', 'google', 'gemini', 'multimodal'],
    favicon: 'https://www.google.com/favicon.ico'
  },
  {
    name: 'LobeHub',
    url: 'https://chat-preview.lobehub.com/discover',
    description: 'LobeHub AI聊天平台，开源AI助手社区',
    tags: ['ai', 'chat', 'lobehub', 'opensource'],
    favicon: 'https://chat-preview.lobehub.com/favicon.ico'
  },
  {
    name: 'Perplexity',
    url: 'https://www.perplexity.ai/',
    description: 'Perplexity AI搜索引擎，AI驱动的搜索体验',
    tags: ['ai', 'search', 'perplexity', 'research'],
    favicon: 'https://www.perplexity.ai/favicon.ico'
  },
  {
    name: 'Grok',
    url: 'https://grok.x.ai/',
    description: 'xAI的Grok AI助手，马斯克团队开发',
    tags: ['ai', 'grok', 'xai', 'elon'],
    favicon: 'https://grok.x.ai/favicon.ico'
  },
  {
    name: 'AI Studio',
    url: 'https://aistudio.google.com/',
    description: 'Google AI Studio开发平台，AI应用开发工具',
    tags: ['ai', 'google', 'development', 'studio'],
    favicon: 'https://aistudio.google.com/favicon.ico'
  }
];

// AI次选网站
const aiSecondaryWebsites: PresetWebsite[] = [
  {
    name: 'DeepAsk',
    url: 'https://deepask.cc/',
    description: 'DeepAsk AI问答平台，深度AI对话体验',
    tags: ['ai', 'qa', 'deepask', 'chinese'],
    favicon: 'https://deepask.cc/favicon.ico'
  },
  {
    name: 'GPTFun',
    url: 'https://fun4ai.khthink.cn/login',
    description: 'GPTFun AI娱乐平台，有趣的AI互动体验',
    tags: ['ai', 'fun', 'gpt', 'entertainment'],
    favicon: 'https://fun4ai.khthink.cn/favicon.ico'
  },
  {
    name: 'C佬',
    url: 'https://new.clivia.fun/',
    description: 'C佬AI助手，专业的AI对话工具',
    tags: ['ai', 'assistant', 'chinese'],
    favicon: 'https://new.clivia.fun/favicon.ico'
  },
  {
    name: 'A佬',
    url: 'https://aabao.eu.cc/',
    description: 'A佬AI工具，实用的AI助手平台',
    tags: ['ai', 'tools', 'chinese'],
    favicon: 'https://aabao.eu.cc/favicon.ico'
  },
  {
    name: 'H佬',
    url: 'https://work.haomo.de/',
    description: 'H佬工作助手，专注工作效率的AI工具',
    tags: ['ai', 'work', 'productivity'],
    favicon: 'https://work.haomo.de/favicon.ico'
  },
  {
    name: 'Claude',
    url: 'https://demo.fuclaude.com/',
    description: 'Anthropic Claude AI，安全可靠的AI助手',
    tags: ['ai', 'claude', 'anthropic', 'safety'],
    favicon: 'https://demo.fuclaude.com/favicon.ico'
  }
];

// AI其他工具网站
const aiToolsWebsites: PresetWebsite[] = [
  {
    name: 'Dify',
    url: 'https://dify.ai/',
    description: 'Dify AI应用开发平台，快速构建AI应用',
    tags: ['ai', 'development', 'dify', 'platform'],
    favicon: 'https://dify.ai/favicon.ico'
  },
  {
    name: '提示词优化',
    url: 'https://promptpilot.volcengine.com/home',
    description: '火山引擎提示词优化工具，提升AI对话效果',
    tags: ['ai', 'prompt', 'optimization', 'volcengine'],
    favicon: 'https://promptpilot.volcengine.com/favicon.ico'
  }
];

// 技术论坛网站
const techForumsWebsites: PresetWebsite[] = [
  {
    name: 'Linux.do',
    url: 'https://linux.do/',
    description: 'Linux技术社区，开源技术讨论平台',
    tags: ['linux', 'community', 'tech', 'opensource'],
    favicon: 'https://linux.do/favicon.ico'
  },
  {
    name: 'NodeLoc',
    url: 'https://nodeloc.cc/',
    description: 'NodeLoc技术论坛，服务器和网络技术交流',
    tags: ['tech', 'forum', 'node', 'server'],
    favicon: 'https://nodeloc.cc/favicon.ico'
  },
  {
    name: 'NodeSeek',
    url: 'https://www.nodeseek.com/',
    description: 'NodeSeek技术分享，专业的技术讨论社区',
    tags: ['tech', 'sharing', 'node', 'community'],
    favicon: 'https://www.nodeseek.com/favicon.ico'
  },
  {
    name: '小众软件',
    url: 'https://meta.appinn.net/latest',
    description: '小众软件分享社区，发现有趣的软件工具',
    tags: ['software', 'tools', 'community', 'apps'],
    favicon: 'https://meta.appinn.net/favicon.ico'
  },
  {
    name: 'Follow',
    url: 'https://app.follow.is/',
    description: 'Follow信息聚合工具，RSS和信息流管理',
    tags: ['rss', 'news', 'aggregation', 'follow'],
    favicon: 'https://app.follow.is/favicon.ico'
  }
];

// 协作工具网站
const collaborationWebsites: PresetWebsite[] = [
  {
    name: '语雀',
    url: 'https://www.yuque.com/',
    description: '阿里巴巴语雀知识库，专业的文档协作平台',
    tags: ['docs', 'knowledge', 'collaboration', 'alibaba'],
    favicon: 'https://www.yuque.com/favicon.ico'
  },
  {
    name: '飞书',
    url: 'https://p1b9rnchwd.feishu.cn/drive/home/',
    description: '字节跳动飞书协作平台，一站式办公解决方案',
    tags: ['collaboration', 'office', 'feishu', 'bytedance'],
    favicon: 'https://www.feishu.cn/favicon.ico'
  }
];

// 预设分组配置
export const PRESET_GROUPS: PresetGroup[] = [
  {
    id: 'ai_primary',
    name: 'AI工作主力',
    description: 'ChatGPT、Gemini、LobeHub等主力AI工具',
    color: '#EF4444',
    icon: '🤖',
    type: 'ai_primary',
    websites: aiPrimaryWebsites
  },
  {
    id: 'ai_secondary',
    name: 'AI次选',
    description: 'DeepAsk、GPTFun、Claude等次选AI工具',
    color: '#F97316',
    icon: '🔧',
    type: 'ai_secondary',
    websites: aiSecondaryWebsites
  },
  {
    id: 'ai_tools',
    name: 'AI其他工具',
    description: 'Dify、提示词优化等辅助工具',
    color: '#8B5CF6',
    icon: '🛠️',
    type: 'ai_tools',
    websites: aiToolsWebsites
  },
  {
    id: 'tech_forums',
    name: '技术论坛',
    description: 'Linux.do、NodeLoc等技术社区',
    color: '#06B6D4',
    icon: '💬',
    type: 'tech_forums',
    websites: techForumsWebsites
  },
  {
    id: 'collaboration',
    name: '协作工具',
    description: '语雀、飞书等协作平台',
    color: '#EC4899',
    icon: '👥',
    type: 'collaboration',
    websites: collaborationWebsites
  }
];

// 获取所有预设网站
export const getAllPresetWebsites = (): PresetWebsite[] => {
  return PRESET_GROUPS.flatMap(group => group.websites);
};

// 根据类型获取预设分组
export const getPresetGroupByType = (type: string): PresetGroup | undefined => {
  return PRESET_GROUPS.find(group => group.type === type);
};

// 根据URL匹配预设网站
export const matchPresetWebsite = (url: string): PresetWebsite | undefined => {
  const allWebsites = getAllPresetWebsites();
  return allWebsites.find(website => {
    try {
      const websiteHost = new URL(website.url).hostname;
      const urlHost = new URL(url).hostname;
      return urlHost.includes(websiteHost) || websiteHost.includes(urlHost);
    } catch {
      return false;
    }
  });
};

// 获取预设分组统计
export const getPresetGroupStats = () => {
  return PRESET_GROUPS.map(group => ({
    ...group,
    websiteCount: group.websites.length
  }));
};
