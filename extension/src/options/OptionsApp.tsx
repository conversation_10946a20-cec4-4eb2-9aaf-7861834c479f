import React, { useState, useEffect } from 'react';

interface Workspace {
  id: string;
  name: string;
  icon: string;
  color: string;
  groups: any[];
  createdAt: string;
  updatedAt: string;
}

const OptionsApp: React.FC = () => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [activeTab, setActiveTab] = useState<'workspaces' | 'about'>('workspaces');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await chrome.storage.local.get(['workspaces']);
      const workspacesData = Object.values(result.workspaces || {}) as Workspace[];
      setWorkspaces(workspacesData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createNewWorkspace = async () => {
    const name = prompt('请输入工作空间名称:');
    if (!name) return;

    const newWorkspace: Workspace = {
      id: `workspace_${Date.now()}`,
      name,
      color: '#3B82F6',
      icon: '📁',
      groups: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    try {
      const result = await chrome.storage.local.get(['workspaces']);
      const workspaces = result.workspaces || {};
      workspaces[newWorkspace.id] = newWorkspace;
      await chrome.storage.local.set({ workspaces });
      await loadData();
    } catch (error) {
      console.error('Failed to create workspace:', error);
      alert('创建工作空间失败');
    }
  };

  const deleteWorkspace = async (workspaceId: string) => {
    if (!confirm('确定要删除这个工作空间吗？')) return;
    
    try {
      const result = await chrome.storage.local.get(['workspaces']);
      const workspaces = result.workspaces || {};
      delete workspaces[workspaceId];
      await chrome.storage.local.set({ workspaces });
      await loadData();
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      alert('删除工作空间失败');
    }
  };

  const openTestPage = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('test-workspace.html') });
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px'
    }}>
      <div style={{
        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        color: 'white',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '30px',
        textAlign: 'center'
      }}>
        <h1 style={{ margin: '0 0 10px 0', fontSize: '24px' }}>🏠 工作区管理器</h1>
        <p style={{ margin: 0, opacity: 0.9 }}>专业的Chrome工作区管理扩展</p>
      </div>

      <div style={{
        display: 'flex',
        borderBottom: '1px solid #e2e8f0',
        marginBottom: '20px'
      }}>
        <button
          onClick={() => setActiveTab('workspaces')}
          style={{
            padding: '12px 24px',
            border: 'none',
            background: activeTab === 'workspaces' ? '#3b82f6' : 'transparent',
            color: activeTab === 'workspaces' ? 'white' : '#64748b',
            borderRadius: '6px 6px 0 0',
            cursor: 'pointer',
            fontWeight: '500'
          }}
        >
          工作区管理
        </button>
        <button
          onClick={() => setActiveTab('about')}
          style={{
            padding: '12px 24px',
            border: 'none',
            background: activeTab === 'about' ? '#3b82f6' : 'transparent',
            color: activeTab === 'about' ? 'white' : '#64748b',
            borderRadius: '6px 6px 0 0',
            cursor: 'pointer',
            fontWeight: '500'
          }}
        >
          关于
        </button>
      </div>

      {activeTab === 'workspaces' && (
        <div>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ margin: 0, fontSize: '18px', color: '#1e293b' }}>工作区列表</h2>
            <button
              onClick={createNewWorkspace}
              style={{
                padding: '8px 16px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontWeight: '500'
              }}
            >
              ➕ 新建工作区
            </button>
          </div>

          <div style={{ display: 'grid', gap: '12px' }}>
            {workspaces.map(workspace => (
              <div
                key={workspace.id}
                style={{
                  background: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  padding: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '20px' }}>{workspace.icon}</span>
                  <div>
                    <div style={{ fontWeight: '500', marginBottom: '4px' }}>
                      {workspace.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#64748b' }}>
                      {workspace.groups?.length || 0} 个分组
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => deleteWorkspace(workspace.id)}
                  style={{
                    padding: '6px 12px',
                    background: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  删除
                </button>
              </div>
            ))}
          </div>

          {workspaces.length === 0 && (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#64748b',
              background: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📁</div>
              <div>暂无工作区</div>
              <div style={{ fontSize: '14px', marginTop: '8px' }}>
                点击"新建工作区"开始使用
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'about' && (
        <div style={{
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '8px',
          padding: '24px'
        }}>
          <h2 style={{ margin: '0 0 16px 0', fontSize: '18px', color: '#1e293b' }}>关于工作区管理器</h2>
          <div style={{ lineHeight: '1.6', color: '#374151' }}>
            <p>工作区管理器是一个专业的Chrome扩展，帮助您更好地组织和管理浏览器标签页。</p>
            
            <h3 style={{ margin: '20px 0 12px 0', fontSize: '16px' }}>主要功能：</h3>
            <ul style={{ paddingLeft: '20px' }}>
              <li>工作区管理：创建和切换不同的工作区</li>
              <li>智能分组：AI自动分类标签页</li>
              <li>书签集成：从书签快速添加标签页</li>
              <li>标签页可见性控制：类似Workona的核心功能</li>
            </ul>

            <h3 style={{ margin: '20px 0 12px 0', fontSize: '16px' }}>使用方法：</h3>
            <ol style={{ paddingLeft: '20px' }}>
              <li>点击扩展图标打开侧边栏</li>
              <li>创建新的工作区或切换现有工作区</li>
              <li>使用AI自动分组功能整理标签页</li>
              <li>从书签或当前标签页添加新内容</li>
            </ol>

            <div style={{
              background: '#f0f9ff',
              border: '1px solid #0ea5e9',
              borderRadius: '6px',
              padding: '16px',
              marginTop: '20px'
            }}>
              <h4 style={{ margin: '0 0 8px 0', color: '#0c4a6e' }}>🧪 测试功能</h4>
              <p style={{ margin: '0 0 12px 0', color: '#0369a1' }}>
                我们提供了一个测试页面来帮助您体验工作区管理功能。
              </p>
              <button
                onClick={openTestPage}
                style={{
                  padding: '8px 16px',
                  background: '#0ea5e9',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontWeight: '500'
                }}
              >
                🚀 打开测试页面
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptionsApp;
