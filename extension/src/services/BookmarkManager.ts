import { BookmarkNode, BookmarkGroupMapping, ImportPreview, BookmarkConflict } from '../types';
import { StorageManager } from './StorageManager';
import { FavoriteManager } from './FavoriteManager';

export class BookmarkManager {
  private static readonly STORAGE_KEY = 'bookmarkImportHistory';

  // 导入Chrome书签
  static async importBookmarks(): Promise<BookmarkNode[]> {
    try {
      const bookmarkTree = await chrome.bookmarks.getTree();
      const flatBookmarks: BookmarkNode[] = [];
      
      // 递归遍历书签树
      this.traverseBookmarkTree(bookmarkTree, flatBookmarks, '');
      
      // 过滤掉文件夹，只保留实际的书签
      const bookmarks = flatBookmarks.filter(bookmark => bookmark.url);
      
      console.log(`Found ${bookmarks.length} bookmarks to import`);
      return bookmarks;
    } catch (error) {
      console.error('Failed to import bookmarks:', error);
      throw error;
    }
  }

  // 递归遍历书签树
  private static traverseBookmarkTree(
    nodes: chrome.bookmarks.BookmarkTreeNode[], 
    result: BookmarkNode[], 
    path: string
  ): void {
    for (const node of nodes) {
      const currentPath = path ? `${path}/${node.title}` : node.title;
      
      const bookmarkNode: BookmarkNode = {
        ...node,
        originalPath: currentPath,
        importStatus: 'pending'
      };
      
      if (node.url) {
        // 这是一个实际的书签
        result.push(bookmarkNode);
      }
      
      // 递归处理子节点
      if (node.children) {
        this.traverseBookmarkTree(node.children, result, currentPath);
      }
    }
  }

  // 预览导入
  static async previewImport(bookmarks: BookmarkNode[]): Promise<ImportPreview> {
    try {
      const mappings: BookmarkGroupMapping[] = [];
      const conflicts: BookmarkConflict[] = [];
      const existingFavorites = await FavoriteManager.getAllFavorites();
      const workspaces = await StorageManager.getWorkspaces();
      
      let mappedCount = 0;
      let conflictCount = 0;
      const newGroupsToCreate = new Set<string>();

      for (const bookmark of bookmarks) {
        if (!bookmark.url) continue;

        // 检查冲突
        const conflict = this.checkForConflicts(bookmark, existingFavorites);
        if (conflict) {
          conflicts.push(conflict);
          conflictCount++;
          continue;
        }

        // 智能映射到分组
        const mapping = await this.suggestGroupMapping(bookmark, workspaces);
        if (mapping) {
          mappings.push(mapping);
          mappedCount++;
          
          if (mapping.mappingType === 'auto' && mapping.targetGroupName.startsWith('New: ')) {
            newGroupsToCreate.add(mapping.targetGroupName);
          }
        }
      }

      return {
        totalBookmarks: bookmarks.length,
        mappedBookmarks: mappedCount,
        unmappedBookmarks: bookmarks.length - mappedCount - conflictCount,
        conflictBookmarks: conflictCount,
        newGroupsToCreate: newGroupsToCreate.size,
        mappings,
        conflicts
      };
    } catch (error) {
      console.error('Failed to preview import:', error);
      throw error;
    }
  }

  // 检查冲突
  private static checkForConflicts(bookmark: BookmarkNode, existingFavorites: any[]): BookmarkConflict | null {
    // 检查URL重复
    const duplicateUrl = existingFavorites.find(fav => fav.url === bookmark.url);
    if (duplicateUrl) {
      return {
        bookmarkId: bookmark.id,
        bookmarkTitle: bookmark.title,
        bookmarkUrl: bookmark.url!,
        conflictType: 'duplicate_url',
        existingItem: {
          id: duplicateUrl.id,
          title: duplicateUrl.title,
          type: 'favorite'
        },
        suggestedAction: 'skip'
      };
    }

    // 检查标题重复
    const duplicateTitle = existingFavorites.find(fav => 
      fav.title.toLowerCase() === bookmark.title.toLowerCase()
    );
    if (duplicateTitle) {
      return {
        bookmarkId: bookmark.id,
        bookmarkTitle: bookmark.title,
        bookmarkUrl: bookmark.url!,
        conflictType: 'duplicate_title',
        existingItem: {
          id: duplicateTitle.id,
          title: duplicateTitle.title,
          type: 'favorite'
        },
        suggestedAction: 'rename'
      };
    }

    return null;
  }

  // 智能映射到分组
  private static async suggestGroupMapping(bookmark: BookmarkNode, workspaces: any[]): Promise<BookmarkGroupMapping | null> {
    try {
      // 基于URL域名进行智能匹配
      const domain = this.extractDomain(bookmark.url!);
      const pathSegments = bookmark.originalPath?.split('/') || [];
      
      // 尝试匹配现有分组
      for (const workspace of workspaces) {
        for (const group of workspace.groups) {
          const confidence = this.calculateMappingConfidence(bookmark, group, domain);
          if (confidence > 0.7) {
            return {
              bookmarkId: bookmark.id,
              bookmarkTitle: bookmark.title,
              bookmarkUrl: bookmark.url,
              targetGroupId: group.id,
              targetGroupName: group.name,
              mappingType: 'auto',
              confidence
            };
          }
        }
      }

      // 基于文件夹路径建议新分组
      if (pathSegments.length > 1) {
        const suggestedGroupName = pathSegments[pathSegments.length - 2]; // 父文件夹名
        return {
          bookmarkId: bookmark.id,
          bookmarkTitle: bookmark.title,
          bookmarkUrl: bookmark.url,
          targetGroupId: `new_${Date.now()}`,
          targetGroupName: `New: ${suggestedGroupName}`,
          mappingType: 'suggested',
          confidence: 0.6
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to suggest group mapping:', error);
      return null;
    }
  }

  // 计算映射置信度
  private static calculateMappingConfidence(bookmark: BookmarkNode, group: any, domain: string): number {
    let confidence = 0;

    // 检查分组名称匹配
    const groupNameLower = group.name.toLowerCase();
    const bookmarkTitleLower = bookmark.title.toLowerCase();
    
    if (bookmarkTitleLower.includes(groupNameLower) || groupNameLower.includes(bookmarkTitleLower)) {
      confidence += 0.3;
    }

    // 检查域名匹配（如果分组有相关标签页）
    if (group.tabs && group.tabs.length > 0) {
      const groupDomains = group.tabs.map((tab: any) => this.extractDomain(tab.url)).filter(Boolean);
      if (groupDomains.includes(domain)) {
        confidence += 0.5;
      }
    }

    // 检查路径匹配
    if (bookmark.originalPath) {
      const pathLower = bookmark.originalPath.toLowerCase();
      if (pathLower.includes(groupNameLower)) {
        confidence += 0.2;
      }
    }

    return Math.min(confidence, 1.0);
  }

  // 提取域名
  private static extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return '';
    }
  }

  // 批量导入书签
  static async batchImportBookmarks(mappings: BookmarkGroupMapping[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    try {
      let successCount = 0;
      let failedCount = 0;
      const errors: string[] = [];

      for (const mapping of mappings) {
        try {
          // 创建收藏项
          const tab = {
            id: undefined,
            title: mapping.bookmarkTitle,
            url: mapping.bookmarkUrl!,
            favIconUrl: undefined,
            active: false,
            pinned: false
          };

          await FavoriteManager.addFavorite(tab, mapping.targetGroupName);
          successCount++;
        } catch (error) {
          failedCount++;
          errors.push(`Failed to import "${mapping.bookmarkTitle}": ${error}`);
        }
      }

      // 保存导入历史
      await this.saveImportHistory({
        timestamp: new Date().toISOString(),
        totalMappings: mappings.length,
        successCount,
        failedCount,
        mappings
      });

      console.log(`Import completed: ${successCount} success, ${failedCount} failed`);
      return { success: successCount, failed: failedCount, errors };
    } catch (error) {
      console.error('Failed to batch import bookmarks:', error);
      throw error;
    }
  }

  // 保存导入历史
  private static async saveImportHistory(history: any): Promise<void> {
    try {
      const existingHistory = await StorageManager.getItem(this.STORAGE_KEY) || [];
      existingHistory.push(history);
      
      // 只保留最近10次导入记录
      if (existingHistory.length > 10) {
        existingHistory.splice(0, existingHistory.length - 10);
      }
      
      await StorageManager.setItem(this.STORAGE_KEY, existingHistory);
    } catch (error) {
      console.error('Failed to save import history:', error);
    }
  }

  // 获取导入历史
  static async getImportHistory(): Promise<any[]> {
    try {
      return await StorageManager.getItem(this.STORAGE_KEY) || [];
    } catch (error) {
      console.error('Failed to get import history:', error);
      return [];
    }
  }
}
