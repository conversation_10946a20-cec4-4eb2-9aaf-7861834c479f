import { StorageManager } from './StorageManager';
import { TabSuspensionManager } from './TabSuspensionManager';
import { Workspace, Tab } from '../types';

export interface WorkspaceWindow {
  id: number;
  workspaceId: string;
  isHidden: boolean;
  tabs: chrome.tabs.Tab[];
  createdAt: string;
  lastActiveAt: string;
}

export interface WorkspaceSwitchOptions {
  suspendPrevious?: boolean;
  restoreState?: boolean;
  preserveActiveTab?: boolean;
}

export class EnhancedWorkspaceManager {
  private static workspaceWindows: Map<string, WorkspaceWindow> = new Map();
  private static hiddenWindows: Map<string, number> = new Map(); // workspaceId -> windowId
  private static currentWorkspaceId: string | null = null;
  private static isInitialized: boolean = false;

  /**
   * 初始化增强工作空间管理器
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 恢复工作空间窗口信息
      await this.restoreWorkspaceWindows();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 检测当前活跃工作空间
      await this.detectCurrentWorkspace();
      
      this.isInitialized = true;
      console.log('EnhancedWorkspaceManager initialized');
    } catch (error) {
      console.error('Failed to initialize EnhancedWorkspaceManager:', error);
    }
  }

  /**
   * 切换工作空间（核心功能）
   */
  static async switchWorkspace(
    targetWorkspaceId: string, 
    options: WorkspaceSwitchOptions = {}
  ): Promise<boolean> {
    try {
      const {
        suspendPrevious = true,
        restoreState = true,
        preserveActiveTab = true
      } = options;

      console.log(`Switching from ${this.currentWorkspaceId} to ${targetWorkspaceId}`);

      // 1. 保存当前工作空间状态
      if (this.currentWorkspaceId && this.currentWorkspaceId !== targetWorkspaceId) {
        await this.saveCurrentWorkspaceState();
        
        if (suspendPrevious) {
          await this.hideWorkspace(this.currentWorkspaceId);
        }
      }

      // 2. 显示目标工作空间
      await this.showWorkspace(targetWorkspaceId, restoreState);
      
      // 3. 更新当前工作空间
      this.currentWorkspaceId = targetWorkspaceId;
      
      // 4. 保存切换记录
      await this.recordWorkspaceSwitch(targetWorkspaceId);
      
      console.log(`Successfully switched to workspace: ${targetWorkspaceId}`);
      return true;
    } catch (error) {
      console.error(`Failed to switch workspace to ${targetWorkspaceId}:`, error);
      return false;
    }
  }

  /**
   * 隐藏工作空间（移动到隐藏窗口）
   */
  private static async hideWorkspace(workspaceId: string): Promise<void> {
    try {
      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent({ populate: true });
      const workspaceTabs = currentWindow.tabs?.filter(tab => 
        this.isTabInWorkspace(tab, workspaceId)
      ) || [];

      if (workspaceTabs.length === 0) return;

      // 创建隐藏窗口
      const hiddenWindow = await chrome.windows.create({
        url: 'about:blank',
        focused: false,
        state: 'minimized',
        type: 'normal'
      });

      if (!hiddenWindow.id) {
        throw new Error('Failed to create hidden window');
      }

      // 移动标签页到隐藏窗口
      const tabIds = workspaceTabs.map(tab => tab.id!).filter(id => id !== undefined);
      if (tabIds.length > 0) {
        await chrome.tabs.move(tabIds, {
          windowId: hiddenWindow.id,
          index: -1
        });
      }

      // 关闭隐藏窗口中的空白标签页
      const blankTabs = await chrome.tabs.query({ 
        windowId: hiddenWindow.id, 
        url: 'about:blank' 
      });
      for (const tab of blankTabs) {
        if (tab.id && tabIds.indexOf(tab.id) === -1) {
          await chrome.tabs.remove(tab.id);
        }
      }

      // 记录隐藏窗口
      this.hiddenWindows.set(workspaceId, hiddenWindow.id);
      
      // 保存工作空间窗口信息
      const workspaceWindow: WorkspaceWindow = {
        id: hiddenWindow.id,
        workspaceId,
        isHidden: true,
        tabs: workspaceTabs,
        createdAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString()
      };
      
      this.workspaceWindows.set(workspaceId, workspaceWindow);
      await this.saveWorkspaceWindows();

      console.log(`Hidden workspace ${workspaceId} in window ${hiddenWindow.id}`);
    } catch (error) {
      console.error(`Failed to hide workspace ${workspaceId}:`, error);
    }
  }

  /**
   * 显示工作空间（从隐藏窗口恢复）
   */
  private static async showWorkspace(workspaceId: string, restoreState: boolean = true): Promise<void> {
    try {
      const hiddenWindowId = this.hiddenWindows.get(workspaceId);
      
      if (hiddenWindowId) {
        // 从隐藏窗口恢复
        await this.restoreFromHiddenWindow(workspaceId, hiddenWindowId);
      } else if (restoreState) {
        // 从保存的状态恢复
        await this.restoreWorkspaceFromStorage(workspaceId);
      } else {
        // 创建新的空工作空间
        await this.createEmptyWorkspace(workspaceId);
      }

      console.log(`Showed workspace ${workspaceId}`);
    } catch (error) {
      console.error(`Failed to show workspace ${workspaceId}:`, error);
    }
  }

  /**
   * 从隐藏窗口恢复工作空间
   */
  private static async restoreFromHiddenWindow(workspaceId: string, hiddenWindowId: number): Promise<void> {
    try {
      // 获取当前活跃窗口
      const currentWindow = await chrome.windows.getCurrent();
      
      // 获取隐藏窗口中的标签页
      const hiddenTabs = await chrome.tabs.query({ windowId: hiddenWindowId });
      
      if (hiddenTabs.length > 0) {
        // 移动标签页到当前窗口
        const tabIds = hiddenTabs.map(tab => tab.id!);
        await chrome.tabs.move(tabIds, {
          windowId: currentWindow.id!,
          index: -1
        });
        
        // 激活第一个标签页
        if (tabIds[0]) {
          await chrome.tabs.update(tabIds[0], { active: true });
        }
      }
      
      // 关闭隐藏窗口
      await chrome.windows.remove(hiddenWindowId);
      
      // 清理记录
      this.hiddenWindows.delete(workspaceId);
      this.workspaceWindows.delete(workspaceId);
      
      console.log(`Restored workspace ${workspaceId} from hidden window`);
    } catch (error) {
      console.error(`Failed to restore from hidden window:`, error);
    }
  }

  /**
   * 从存储恢复工作空间
   */
  private static async restoreWorkspaceFromStorage(workspaceId: string): Promise<void> {
    try {
      // 获取保存的工作空间数据
      const workspace = await StorageManager.getWorkspace(workspaceId);
      if (!workspace || !workspace.tabs || workspace.tabs.length === 0) {
        return;
      }

      // 批量打开标签页
      const createdTabs: chrome.tabs.Tab[] = [];
      for (const tab of workspace.tabs) {
        try {
          const createdTab = await chrome.tabs.create({
            url: tab.url,
            active: false,
            pinned: tab.pinned || false
          });
          createdTabs.push(createdTab);
        } catch (error) {
          console.error(`Failed to restore tab ${tab.url}:`, error);
        }
      }

      // 激活第一个标签页
      if (createdTabs.length > 0 && createdTabs[0].id) {
        await chrome.tabs.update(createdTabs[0].id, { active: true });
      }

      console.log(`Restored workspace ${workspaceId} from storage with ${createdTabs.length} tabs`);
    } catch (error) {
      console.error(`Failed to restore workspace from storage:`, error);
    }
  }

  /**
   * 创建空工作空间
   */
  private static async createEmptyWorkspace(workspaceId: string): Promise<void> {
    try {
      // 创建一个新标签页作为工作空间起点
      await chrome.tabs.create({
        url: 'chrome://newtab/',
        active: true
      });
      
      console.log(`Created empty workspace ${workspaceId}`);
    } catch (error) {
      console.error(`Failed to create empty workspace:`, error);
    }
  }

  /**
   * 保存当前工作空间状态
   */
  private static async saveCurrentWorkspaceState(): Promise<void> {
    if (!this.currentWorkspaceId) return;

    try {
      const currentTabs = await chrome.tabs.query({ 
        windowId: chrome.windows.WINDOW_ID_CURRENT 
      });
      
      const workspaceTabs = currentTabs
        .filter(tab => this.isTabInWorkspace(tab, this.currentWorkspaceId!))
        .map(tab => ({
          id: tab.id!,
          url: tab.url!,
          title: tab.title!,
          pinned: tab.pinned,
          active: tab.active,
          favIconUrl: tab.favIconUrl
        }));

      // 更新工作空间数据
      const workspace = await StorageManager.getWorkspace(this.currentWorkspaceId) || {
        id: this.currentWorkspaceId,
        name: this.currentWorkspaceId,
        tabs: [],
        groups: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      workspace.tabs = workspaceTabs;
      workspace.updatedAt = new Date().toISOString();

      await StorageManager.saveWorkspace(workspace);
      console.log(`Saved state for workspace ${this.currentWorkspaceId}`);
    } catch (error) {
      console.error('Failed to save current workspace state:', error);
    }
  }

  /**
   * 检查标签页是否属于指定工作空间
   */
  private static isTabInWorkspace(tab: chrome.tabs.Tab, workspaceId: string): boolean {
    // 这里可以实现更复杂的逻辑来判断标签页归属
    // 暂时简单实现：所有标签页都属于当前工作空间
    return true;
  }

  /**
   * 检测当前活跃工作空间
   */
  private static async detectCurrentWorkspace(): Promise<void> {
    try {
      // 从存储中获取最后活跃的工作空间
      const lastActiveWorkspace = await StorageManager.getItem('lastActiveWorkspace');
      this.currentWorkspaceId = lastActiveWorkspace || 'default';
      
      console.log(`Detected current workspace: ${this.currentWorkspaceId}`);
    } catch (error) {
      console.error('Failed to detect current workspace:', error);
      this.currentWorkspaceId = 'default';
    }
  }

  /**
   * 记录工作空间切换
   */
  private static async recordWorkspaceSwitch(workspaceId: string): Promise<void> {
    try {
      await StorageManager.setItem('lastActiveWorkspace', workspaceId);
      
      // 记录切换历史
      const switchHistory = await StorageManager.getItem('workspaceSwitchHistory') || [];
      switchHistory.push({
        workspaceId,
        timestamp: new Date().toISOString()
      });
      
      // 只保留最近100次切换记录
      if (switchHistory.length > 100) {
        switchHistory.splice(0, switchHistory.length - 100);
      }
      
      await StorageManager.setItem('workspaceSwitchHistory', switchHistory);
    } catch (error) {
      console.error('Failed to record workspace switch:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  private static setupEventListeners(): void {
    // 监听窗口关闭
    chrome.windows.onRemoved.addListener((windowId) => {
      // 清理隐藏窗口记录
      for (const [workspaceId, hiddenWindowId] of this.hiddenWindows.entries()) {
        if (hiddenWindowId === windowId) {
          this.hiddenWindows.delete(workspaceId);
          this.workspaceWindows.delete(workspaceId);
          break;
        }
      }
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && this.currentWorkspaceId) {
        // 自动保存工作空间状态
        await this.saveCurrentWorkspaceState();
      }
    });
  }

  /**
   * 保存工作空间窗口信息
   */
  private static async saveWorkspaceWindows(): Promise<void> {
    try {
      const windowsArray = Array.from(this.workspaceWindows.entries());
      await StorageManager.setItem('workspaceWindows', windowsArray);
      
      const hiddenWindowsArray = Array.from(this.hiddenWindows.entries());
      await StorageManager.setItem('hiddenWindows', hiddenWindowsArray);
    } catch (error) {
      console.error('Failed to save workspace windows:', error);
    }
  }

  /**
   * 恢复工作空间窗口信息
   */
  private static async restoreWorkspaceWindows(): Promise<void> {
    try {
      const windowsArray = await StorageManager.getItem('workspaceWindows');
      if (windowsArray && Array.isArray(windowsArray)) {
        this.workspaceWindows = new Map(windowsArray);
      }
      
      const hiddenWindowsArray = await StorageManager.getItem('hiddenWindows');
      if (hiddenWindowsArray && Array.isArray(hiddenWindowsArray)) {
        this.hiddenWindows = new Map(hiddenWindowsArray);
      }
    } catch (error) {
      console.error('Failed to restore workspace windows:', error);
    }
  }

  /**
   * 获取当前工作空间ID
   */
  static getCurrentWorkspaceId(): string | null {
    return this.currentWorkspaceId;
  }

  /**
   * 获取所有隐藏的工作空间
   */
  static getHiddenWorkspaces(): string[] {
    return Array.from(this.hiddenWindows.keys());
  }

  /**
   * 强制显示所有隐藏的工作空间
   */
  static async showAllHiddenWorkspaces(): Promise<void> {
    const hiddenWorkspaceIds = Array.from(this.hiddenWindows.keys());
    
    for (const workspaceId of hiddenWorkspaceIds) {
      try {
        await this.showWorkspace(workspaceId, false);
      } catch (error) {
        console.error(`Failed to show hidden workspace ${workspaceId}:`, error);
      }
    }
  }

  /**
   * 清理所有隐藏窗口
   */
  static async cleanupHiddenWindows(): Promise<void> {
    for (const [workspaceId, windowId] of this.hiddenWindows.entries()) {
      try {
        await chrome.windows.remove(windowId);
      } catch (error) {
        console.error(`Failed to cleanup hidden window ${windowId}:`, error);
      }
    }
    
    this.hiddenWindows.clear();
    this.workspaceWindows.clear();
    await this.saveWorkspaceWindows();
  }
}
