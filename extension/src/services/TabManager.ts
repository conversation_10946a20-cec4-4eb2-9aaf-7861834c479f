import { Tab } from '../types';

export class TabManager {
  // 获取当前窗口的所有标签页
  static async getCurrentTabs(): Promise<Tab[]> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      return tabs.map(tab => ({
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      }));
    } catch (error) {
      console.error('Failed to get current tabs:', error);
      return [];
    }
  }

  // 获取当前活动标签页
  static async getCurrentActiveTab(): Promise<Tab | null> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) return null;

      const tab = tabs[0];
      return {
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      };
    } catch (error) {
      console.error('Failed to get current active tab:', error);
      return null;
    }
  }

  // 获取所有标签页
  static async getAllTabs(): Promise<Tab[]> {
    try {
      const tabs = await chrome.tabs.query({});
      return tabs.map(tab => ({
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      }));
    } catch (error) {
      console.error('Failed to get all tabs:', error);
      return [];
    }
  }

  // 创建新标签页
  static async createTab(url: string, active: boolean = false): Promise<Tab | null> {
    try {
      const tab = await chrome.tabs.create({ url, active });
      return {
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      };
    } catch (error) {
      console.error('Failed to create tab:', error);
      return null;
    }
  }

  // 关闭标签页
  static async closeTab(tabId: number): Promise<boolean> {
    try {
      await chrome.tabs.remove(tabId);
      return true;
    } catch (error) {
      console.error('Failed to close tab:', error);
      return false;
    }
  }

  // 更新标签页
  static async updateTab(tabId: number, updateProperties: chrome.tabs.UpdateProperties): Promise<Tab> {
    try {
      const tab = await chrome.tabs.update(tabId, updateProperties);
      return {
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      };
    } catch (error) {
      console.error('Failed to update tab:', error);
      throw error;
    }
  }

  // 激活标签页
  static async activateTab(tabId: number): Promise<boolean> {
    try {
      await chrome.tabs.update(tabId, { active: true });
      return true;
    } catch (error) {
      console.error('Failed to activate tab:', error);
      return false;
    }
  }

  // 切换到标签页（别名）
  static async switchToTab(tabId: number): Promise<boolean> {
    return this.activateTab(tabId);
  }

  // 刷新标签页
  static async reloadTab(tabId: number): Promise<boolean> {
    try {
      await chrome.tabs.reload(tabId);
      return true;
    } catch (error) {
      console.error('Failed to reload tab:', error);
      return false;
    }
  }

  // 复制标签页
  static async duplicateTab(tabId: number): Promise<Tab> {
    try {
      const tabs = await chrome.tabs.query({ id: tabId });
      if (tabs.length === 0) {
        throw new Error('Tab not found');
      }

      const originalTab = tabs[0];
      const duplicatedTab = await chrome.tabs.create({
        url: originalTab.url,
        index: originalTab.index + 1,
        active: false
      });

      return {
        id: duplicatedTab.id,
        title: duplicatedTab.title || '',
        url: duplicatedTab.url || '',
        favIconUrl: duplicatedTab.favIconUrl,
        active: duplicatedTab.active,
        pinned: duplicatedTab.pinned,
        groupId: duplicatedTab.groupId,
        windowId: duplicatedTab.windowId,
        index: duplicatedTab.index
      };
    } catch (error) {
      console.error('Failed to duplicate tab:', error);
      throw error;
    }
  }

  // 固定标签页
  static async pinTab(tabId: number): Promise<boolean> {
    try {
      await chrome.tabs.update(tabId, { pinned: true });
      return true;
    } catch (error) {
      console.error('Failed to pin tab:', error);
      return false;
    }
  }

  // 取消固定标签页
  static async unpinTab(tabId: number): Promise<boolean> {
    try {
      await chrome.tabs.update(tabId, { pinned: false });
      return true;
    } catch (error) {
      console.error('Failed to unpin tab:', error);
      return false;
    }
  }

  // 组级固定控制功能

  // 固定分组中的所有标签页
  static async pinTabsInGroup(groupId: number): Promise<boolean> {
    try {
      const tabs = await chrome.tabs.query({ groupId });
      const pinPromises = tabs.map(tab => {
        if (tab.id && !tab.pinned) {
          return this.pinTab(tab.id);
        }
        return Promise.resolve(true);
      });

      const results = await Promise.all(pinPromises);
      const success = results.every(result => result);

      if (success) {
        console.log(`Pinned ${tabs.length} tabs in group ${groupId}`);
      }

      return success;
    } catch (error) {
      console.error('Failed to pin tabs in group:', error);
      return false;
    }
  }

  // 取消固定分组中的所有标签页
  static async unpinTabsInGroup(groupId: number): Promise<boolean> {
    try {
      const tabs = await chrome.tabs.query({ groupId });
      const unpinPromises = tabs.map(tab => {
        if (tab.id && tab.pinned) {
          return this.unpinTab(tab.id);
        }
        return Promise.resolve(true);
      });

      const results = await Promise.all(unpinPromises);
      const success = results.every(result => result);

      if (success) {
        console.log(`Unpinned ${tabs.length} tabs in group ${groupId}`);
      }

      return success;
    } catch (error) {
      console.error('Failed to unpin tabs in group:', error);
      return false;
    }
  }

  // 设置分组固定策略
  static async setGroupPinningPolicy(groupId: number, policy: 'auto' | 'manual' | 'disabled'): Promise<void> {
    try {
      const { StorageManager } = await import('./StorageManager');
      const policies = await StorageManager.getItem('groupPinningPolicies') || {};

      policies[groupId] = {
        groupId,
        policy,
        pinNewTabs: policy === 'auto',
        unpinOnRemove: policy === 'auto',
        exceptions: [],
        createdAt: policies[groupId]?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await StorageManager.setItem('groupPinningPolicies', policies);

      // 如果是自动策略，立即应用到现有标签页
      if (policy === 'auto') {
        await this.pinTabsInGroup(groupId);
      }

      console.log(`Set pinning policy for group ${groupId}: ${policy}`);
    } catch (error) {
      console.error('Failed to set group pinning policy:', error);
      throw error;
    }
  }

  // 获取分组固定策略
  static async getGroupPinningPolicy(groupId: number): Promise<any> {
    try {
      const { StorageManager } = await import('./StorageManager');
      const policies = await StorageManager.getItem('groupPinningPolicies') || {};
      return policies[groupId] || {
        groupId,
        policy: 'manual',
        pinNewTabs: false,
        unpinOnRemove: false,
        exceptions: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get group pinning policy:', error);
      return null;
    }
  }

  // 处理分组变化时的固定策略
  static async handleGroupPinningPolicy(groupId: number, tabs: any[]): Promise<void> {
    try {
      const policy = await this.getGroupPinningPolicy(groupId);

      if (policy && policy.policy === 'auto') {
        // 自动固定新加入分组的标签页
        for (const tab of tabs) {
          if (tab.id && !tab.pinned && !policy.exceptions.includes(tab.id)) {
            await this.pinTab(tab.id);
          }
        }
      }
    } catch (error) {
      console.error('Failed to handle group pinning policy:', error);
    }
  }

  // 批量打开标签页
  static async openTabs(urls: string[]): Promise<Tab[]> {
    const createdTabs: Tab[] = [];
    
    for (const url of urls) {
      const tab = await this.createTab(url, false);
      if (tab) {
        createdTabs.push(tab);
      }
    }
    
    return createdTabs;
  }  // 保存标签页到工作空间
  static async saveTabsToWorkspace(tabs: Tab[], workspaceId: string): Promise<boolean> {
    try {
      // 这里会调用StorageManager来保存
      const { StorageManager } = await import('./StorageManager');
      return await StorageManager.saveTabsToWorkspace(tabs, workspaceId);
    } catch (error) {
      console.error('Failed to save tabs to workspace:', error);
      return false;
    }
  }

  // 打开工作空间的所有标签页
  static async openWorkspace(workspaceId: string): Promise<boolean> {
    try {
      const { StorageManager } = await import('./StorageManager');
      const workspace = await StorageManager.getWorkspace(workspaceId);
      
      if (!workspace) {
        throw new Error('Workspace not found');
      }

      // 收集所有标签页URL
      const urls: string[] = [];
      workspace.groups.forEach(group => {
        group.tabs.forEach(tab => {
          if (tab.url) {
            urls.push(tab.url);
          }
        });
      });

      // 批量打开标签页
      await this.openTabs(urls);
      return true;
    } catch (error) {
      console.error('Failed to open workspace:', error);
      return false;
    }
  }

  // 创建标签页分组
  static async createTabGroup(tabIds: number[], groupName: string): Promise<number | null> {
    try {
      const groupId = await chrome.tabs.group({ tabIds });
      await chrome.tabGroups.update(groupId, { title: groupName });
      return groupId;
    } catch (error) {
      console.error('Failed to create tab group:', error);
      return null;
    }
  }

  // 移动标签页到分组
  static async moveTabsToGroup(tabIds: number[], groupId: number): Promise<boolean> {
    try {
      await chrome.tabs.group({ tabIds, groupId });
      return true;
    } catch (error) {
      console.error('Failed to move tabs to group:', error);
      return false;
    }
  }

  // 搜索标签页
  static async searchTabs(query: string): Promise<Tab[]> {
    try {
      const allTabs = await this.getAllTabs();
      const lowerQuery = query.toLowerCase();
      
      return allTabs.filter(tab => 
        tab.title.toLowerCase().includes(lowerQuery) ||
        tab.url.toLowerCase().includes(lowerQuery)
      );
    } catch (error) {
      console.error('Failed to search tabs:', error);
      return [];
    }
  }

  // 获取重复的标签页
  static async getDuplicateTabs(): Promise<Tab[][]> {
    try {
      const allTabs = await this.getAllTabs();
      const urlMap = new Map<string, Tab[]>();
      
      // 按URL分组
      allTabs.forEach(tab => {
        const url = tab.url;
        if (!urlMap.has(url)) {
          urlMap.set(url, []);
        }
        urlMap.get(url)!.push(tab);
      });
      
      // 返回重复的标签页组
      return Array.from(urlMap.values()).filter(tabs => tabs.length > 1);
    } catch (error) {
      console.error('Failed to get duplicate tabs:', error);
      return [];
    }
  }
}