import { Tab, TabGroup } from '../types';
import { StorageManager } from './StorageManager';

export class TabGroupManager {
  // 创建标签页分组
  static async createTabGroup(
    name: string, 
    color: string = '#3B82F6', 
    icon: string = '📁'
  ): Promise<number | null> {
    try {
      // 获取当前窗口的所有标签页
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabIds = tabs.map(tab => tab.id!).filter(id => id !== undefined);
      
      if (tabIds.length === 0) {
        throw new Error('No tabs to group');
      }
      
      // 创建Chrome标签页分组
      const groupId = await chrome.tabs.group({ tabIds });
      
      // 更新分组属性
      await chrome.tabGroups.update(groupId, {
        title: name,
        color: color as chrome.tabGroups.ColorEnum
      });
      
      console.log(`Created tab group: ${name} with ${tabIds.length} tabs`);
      return groupId;
    } catch (error) {
      console.error('Failed to create tab group:', error);
      return null;
    }
  }

  // 获取所有标签页分组
  static async getAllTabGroups(): Promise<chrome.tabGroups.TabGroup[]> {
    try {
      return await chrome.tabGroups.query({});
    } catch (error) {
      console.error('Failed to get tab groups:', error);
      return [];
    }
  }

  // 获取分组中的标签页
  static async getTabsInGroup(groupId: number): Promise<Tab[]> {
    try {
      const tabs = await chrome.tabs.query({ groupId });
      return tabs.map(tab => ({
        id: tab.id,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active,
        pinned: tab.pinned,
        groupId: tab.groupId,
        windowId: tab.windowId,
        index: tab.index
      }));
    } catch (error) {
      console.error('Failed to get tabs in group:', error);
      return [];
    }
  }

  // 将标签页添加到分组
  static async addTabsToGroup(tabIds: number[], groupId: number): Promise<boolean> {
    try {
      await chrome.tabs.group({ tabIds, groupId });
      console.log(`Added ${tabIds.length} tabs to group ${groupId}`);
      return true;
    } catch (error) {
      console.error('Failed to add tabs to group:', error);
      return false;
    }
  }

  // 从分组中移除标签页
  static async removeTabsFromGroup(tabIds: number[]): Promise<boolean> {
    try {
      await chrome.tabs.ungroup(tabIds);
      console.log(`Removed ${tabIds.length} tabs from group`);
      return true;
    } catch (error) {
      console.error('Failed to remove tabs from group:', error);
      return false;
    }
  }

  // 更新分组属性
  static async updateTabGroup(
    groupId: number, 
    updates: { title?: string; color?: string; collapsed?: boolean }
  ): Promise<boolean> {
    try {
      const updateData: chrome.tabGroups.UpdateProperties = {};
      
      if (updates.title) updateData.title = updates.title;
      if (updates.color) updateData.color = updates.color as chrome.tabGroups.ColorEnum;
      if (updates.collapsed !== undefined) updateData.collapsed = updates.collapsed;
      
      await chrome.tabGroups.update(groupId, updateData);
      console.log(`Updated tab group ${groupId}:`, updates);
      return true;
    } catch (error) {
      console.error('Failed to update tab group:', error);
      return false;
    }
  }

  // 删除标签页分组
  static async deleteTabGroup(groupId: number, closeTabsToo: boolean = false): Promise<boolean> {
    try {
      if (closeTabsToo) {
        // 获取分组中的标签页并关闭它们
        const tabs = await this.getTabsInGroup(groupId);
        const tabIds = tabs.map(tab => tab.id!).filter(id => id !== undefined);
        if (tabIds.length > 0) {
          await chrome.tabs.remove(tabIds);
        }
      } else {
        // 只是解散分组，保留标签页
        const tabs = await this.getTabsInGroup(groupId);
        const tabIds = tabs.map(tab => tab.id!).filter(id => id !== undefined);
        if (tabIds.length > 0) {
          await chrome.tabs.ungroup(tabIds);
        }
      }
      
      console.log(`Deleted tab group ${groupId}, closeTabsToo: ${closeTabsToo}`);
      return true;
    } catch (error) {
      console.error('Failed to delete tab group:', error);
      return false;
    }
  }

  // 折叠/展开分组
  static async toggleGroupCollapse(groupId: number): Promise<boolean> {
    try {
      const group = await chrome.tabGroups.get(groupId);
      await chrome.tabGroups.update(groupId, { collapsed: !group.collapsed });
      console.log(`Toggled group ${groupId} collapse to ${!group.collapsed}`);
      return true;
    } catch (error) {
      console.error('Failed to toggle group collapse:', error);
      return false;
    }
  }

  // 智能分组：根据域名自动分组
  static async autoGroupByDomain(): Promise<boolean> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const domainGroups = new Map<string, chrome.tabs.Tab[]>();
      
      // 按域名分组
      tabs.forEach(tab => {
        if (tab.url) {
          try {
            const domain = new URL(tab.url).hostname;
            if (!domainGroups.has(domain)) {
              domainGroups.set(domain, []);
            }
            domainGroups.get(domain)!.push(tab);
          } catch (error) {
            // 忽略无效URL
          }
        }
      });
      
      // 为每个域名创建分组（只有多个标签页的域名）
      for (const [domain, domainTabs] of domainGroups) {
        if (domainTabs.length > 1) {
          const tabIds = domainTabs.map(tab => tab.id!).filter(id => id !== undefined);
          if (tabIds.length > 1) {
            const groupId = await chrome.tabs.group({ tabIds });
            await chrome.tabGroups.update(groupId, {
              title: domain,
              color: this.getColorForDomain(domain)
            });
          }
        }
      }
      
      console.log(`Auto-grouped tabs by domain: ${domainGroups.size} domains`);
      return true;
    } catch (error) {
      console.error('Failed to auto-group by domain:', error);
      return false;
    }
  }

  // 为域名选择颜色
  private static getColorForDomain(domain: string): chrome.tabGroups.ColorEnum {
    const colors: chrome.tabGroups.ColorEnum[] = [
      'blue', 'red', 'yellow', 'green', 'pink', 'purple', 'cyan', 'orange'
    ];
    
    // 基于域名哈希选择颜色
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
      hash = ((hash << 5) - hash + domain.charCodeAt(i)) & 0xffffffff;
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  // 智能分组：根据网站类型自动分组
  static async autoGroupByType(): Promise<boolean> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const typeGroups = new Map<string, chrome.tabs.Tab[]>();
      
      // 定义网站类型规则
      const typeRules = {
        'AI工具': ['chat.openai.com', 'gemini.google.com', 'claude.ai', 'perplexity.ai'],
        '开发工具': ['github.com', 'stackoverflow.com', 'developer.mozilla.org'],
        '社交媒体': ['twitter.com', 'facebook.com', 'linkedin.com', 'instagram.com'],
        '视频平台': ['youtube.com', 'bilibili.com', 'netflix.com'],
        '购物网站': ['amazon.com', 'taobao.com', 'jd.com'],
        '新闻资讯': ['news.google.com', 'bbc.com', 'cnn.com']
      };
      
      // 分类标签页
      tabs.forEach(tab => {
        if (tab.url) {
          try {
            const hostname = new URL(tab.url).hostname;
            let assigned = false;
            
            for (const [type, domains] of Object.entries(typeRules)) {
              if (domains.some(domain => hostname.includes(domain))) {
                if (!typeGroups.has(type)) {
                  typeGroups.set(type, []);
                }
                typeGroups.get(type)!.push(tab);
                assigned = true;
                break;
              }
            }
            
            // 未分类的放入"其他"组
            if (!assigned) {
              if (!typeGroups.has('其他')) {
                typeGroups.set('其他', []);
              }
              typeGroups.get('其他')!.push(tab);
            }
          } catch (error) {
            // 忽略无效URL
          }
        }
      });
      
      // 创建分组
      for (const [type, typeTabs] of typeGroups) {
        if (typeTabs.length > 1) {
          const tabIds = typeTabs.map(tab => tab.id!).filter(id => id !== undefined);
          if (tabIds.length > 1) {
            const groupId = await chrome.tabs.group({ tabIds });
            await chrome.tabGroups.update(groupId, {
              title: type,
              color: this.getColorForType(type)
            });
          }
        }
      }
      
      console.log(`Auto-grouped tabs by type: ${typeGroups.size} types`);
      return true;
    } catch (error) {
      console.error('Failed to auto-group by type:', error);
      return false;
    }
  }

  // 为类型选择颜色
  private static getColorForType(type: string): chrome.tabGroups.ColorEnum {
    const typeColors: Record<string, chrome.tabGroups.ColorEnum> = {
      'AI工具': 'purple',
      '开发工具': 'blue',
      '社交媒体': 'pink',
      '视频平台': 'red',
      '购物网站': 'orange',
      '新闻资讯': 'green',
      '其他': 'grey'
    };
    
    return typeColors[type] || 'grey';
  }

  // 保存分组到工作空间
  static async saveGroupToWorkspace(groupId: number, workspaceId: string): Promise<boolean> {
    try {
      const tabs = await this.getTabsInGroup(groupId);
      const group = await chrome.tabGroups.get(groupId);
      
      const tabGroup: TabGroup = {
        id: `group_${groupId}_${Date.now()}`,
        name: group.title || '未命名分组',
        color: group.color,
        icon: '📁',
        tabs: tabs,
        sortOrder: 0
      };
      
      // 保存到工作空间
      const workspace = await StorageManager.getWorkspace(workspaceId);
      if (workspace) {
        workspace.groups.push(tabGroup);
        workspace.updatedAt = new Date().toISOString();
        await StorageManager.saveWorkspace(workspace);
        console.log(`Saved group ${groupId} to workspace ${workspaceId}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to save group to workspace:', error);
      return false;
    }
  }
}
