import { Workspace, Tab, UserSettings, SyncData } from '../types';

export class StorageManager {
  private static readonly STORAGE_KEYS = {
    WORKSPACES: 'workspaces',
    ACTIVE_WORKSPACE: 'activeWorkspace',
    USER_SETTINGS: 'userSettings',
    SYNC_DATA: 'syncData'
  };

  // 初始化存储管理器
  static async initialize(): Promise<void> {
    try {
      // 确保有默认工作空间
      await this.ensureDefaultWorkspace();
      console.log('StorageManager initialized');
    } catch (error) {
      console.error('Failed to initialize StorageManager:', error);
    }
  }

  // 确保有默认工作空间
  static async ensureDefaultWorkspace(): Promise<void> {
    try {
      const workspaces = await this.getWorkspaces();

      if (workspaces.length === 0) {
        const defaultWorkspace: Workspace = {
          id: 'default_' + Date.now(),
          name: '默认工作空间',
          description: '系统默认工作空间',
          color: '#3B82F6',
          icon: '🏠',
          groups: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isDefault: true
        };

        await this.saveWorkspace(defaultWorkspace);
        console.log('Default workspace created');
      }
    } catch (error) {
      console.error('Failed to ensure default workspace:', error);
    }
  }

  // 获取所有工作空间
  static async getWorkspaces(): Promise<Workspace[]> {
    try {
      const result = await chrome.storage.sync.get([this.STORAGE_KEYS.WORKSPACES]);
      return result[this.STORAGE_KEYS.WORKSPACES] || [];
    } catch (error) {
      console.error('Failed to get workspaces:', error);
      return [];
    }
  }

  // 获取单个工作空间
  static async getWorkspace(workspaceId: string): Promise<Workspace | null> {
    try {
      const workspaces = await this.getWorkspaces();
      return workspaces.find(ws => ws.id === workspaceId) || null;
    } catch (error) {
      console.error('Failed to get workspace:', error);
      return null;
    }
  }

  // 保存工作空间
  static async saveWorkspace(workspace: Workspace): Promise<boolean> {
    try {
      const workspaces = await this.getWorkspaces();
      const existingIndex = workspaces.findIndex(ws => ws.id === workspace.id);
      
      if (existingIndex >= 0) {
        workspaces[existingIndex] = workspace;
      } else {
        workspaces.push(workspace);
      }
      
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.WORKSPACES]: workspaces
      });
      
      return true;
    } catch (error) {
      console.error('Failed to save workspace:', error);
      return false;
    }
  }

  // 删除工作空间
  static async deleteWorkspace(workspaceId: string): Promise<boolean> {
    try {
      const workspaces = await this.getWorkspaces();
      const filteredWorkspaces = workspaces.filter(ws => ws.id !== workspaceId);
      
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.WORKSPACES]: filteredWorkspaces
      });
      
      return true;
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      return false;
    }
  }

  // 获取活跃工作空间
  static async getActiveWorkspace(): Promise<string | null> {
    try {
      const result = await chrome.storage.sync.get([this.STORAGE_KEYS.ACTIVE_WORKSPACE]);
      return result[this.STORAGE_KEYS.ACTIVE_WORKSPACE] || null;
    } catch (error) {
      console.error('Failed to get active workspace:', error);
      return null;
    }
  }

  // 设置活跃工作空间
  static async setActiveWorkspace(workspaceId: string): Promise<boolean> {
    try {
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.ACTIVE_WORKSPACE]: workspaceId
      });
      return true;
    } catch (error) {
      console.error('Failed to set active workspace:', error);
      return false;
    }
  }  // 保存标签页到工作空间
  static async saveTabsToWorkspace(tabs: Tab[], workspaceId: string): Promise<boolean> {
    try {
      const workspace = await this.getWorkspace(workspaceId);
      if (!workspace) {
        throw new Error('Workspace not found');
      }

      // 创建一个新的分组来保存这些标签页
      const newGroup = {
        id: `group_${Date.now()}`,
        name: `保存的标签页 ${new Date().toLocaleString()}`,
        color: '#3B82F6',
        icon: 'bookmark',
        tabs: tabs,
        sortOrder: workspace.groups.length
      };

      workspace.groups.push(newGroup);
      workspace.updatedAt = new Date().toISOString();

      return await this.saveWorkspace(workspace);
    } catch (error) {
      console.error('Failed to save tabs to workspace:', error);
      return false;
    }
  }

  // 获取用户设置
  static async getUserSettings(): Promise<UserSettings> {
    try {
      const result = await chrome.storage.sync.get([this.STORAGE_KEYS.USER_SETTINGS]);
      return result[this.STORAGE_KEYS.USER_SETTINGS] || {
        theme: 'auto',
        autoSave: true,
        localSyncEnabled: false,
        shortcuts: {
          toggleWorkspace: 'Ctrl+Shift+W',
          quickSearch: 'Ctrl+Shift+F'
        }
      };
    } catch (error) {
      console.error('Failed to get user settings:', error);
      return {
        theme: 'auto',
        autoSave: true,
        localSyncEnabled: false,
        shortcuts: {
          toggleWorkspace: 'Ctrl+Shift+W',
          quickSearch: 'Ctrl+Shift+F'
        }
      };
    }
  }

  // 保存用户设置
  static async saveUserSettings(settings: UserSettings): Promise<boolean> {
    try {
      await chrome.storage.sync.set({
        [this.STORAGE_KEYS.USER_SETTINGS]: settings
      });
      return true;
    } catch (error) {
      console.error('Failed to save user settings:', error);
      return false;
    }
  }



  // 清除所有数据
  static async clearAllData(): Promise<boolean> {
    try {
      await chrome.storage.sync.clear();
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear all data:', error);
      return false;
    }
  }

  // 通用的获取数据方法
  static async getItem(key: string, useLocal: boolean = true): Promise<any> {
    try {
      const storage = useLocal ? chrome.storage.local : chrome.storage.sync;
      const result = await storage.get([key]);
      return result[key] || null;
    } catch (error) {
      console.error(`Failed to get item ${key}:`, error);
      return null;
    }
  }

  // 通用的设置数据方法
  static async setItem(key: string, value: any, useLocal: boolean = true): Promise<boolean> {
    try {
      const storage = useLocal ? chrome.storage.local : chrome.storage.sync;
      await storage.set({ [key]: value });
      return true;
    } catch (error) {
      console.error(`Failed to set item ${key}:`, error);
      return false;
    }
  }

  // 通用的删除数据方法
  static async removeItem(key: string, useLocal: boolean = true): Promise<boolean> {
    try {
      const storage = useLocal ? chrome.storage.local : chrome.storage.sync;
      await storage.remove([key]);
      return true;
    } catch (error) {
      console.error(`Failed to remove item ${key}:`, error);
      return false;
    }
  }
}