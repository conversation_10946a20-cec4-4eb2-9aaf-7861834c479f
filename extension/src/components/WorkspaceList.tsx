import React from 'react';
import { Workspace } from '../types';

interface WorkspaceListProps {
  workspaces: Workspace[];
  activeWorkspace: string | null;
  onWorkspaceChange: (workspaceId: string) => void;
  onOpenWorkspace: (workspaceId: string) => void;
}

const WorkspaceList: React.FC<WorkspaceListProps> = ({
  workspaces,
  activeWorkspace,
  onWorkspaceChange,
  onOpenWorkspace
}) => {
  if (workspaces.length === 0) {
    return (
      <div className="workspace-list">
        <div className="empty-state">
          <p>暂无工作空间</p>
          <button 
            className="btn btn-primary"
            onClick={() => chrome.runtime.openOptionsPage()}
          >
            创建工作空间
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="workspace-list">
      <h3>工作空间</h3>
      <div className="workspace-items">
        {workspaces.map(workspace => (
          <div 
            key={workspace.id}
            className={`workspace-item ${activeWorkspace === workspace.id ? 'active' : ''}`}
          >
            <div 
              className="workspace-info"
              onClick={() => onWorkspaceChange(workspace.id)}
            >
              <span 
                className="workspace-icon"
                style={{ color: workspace.color }}
              >
                {workspace.icon}
              </span>
              <div className="workspace-details">
                <span className="workspace-name">{workspace.name}</span>
                <span className="workspace-count">
                  {workspace.groups.reduce((total, group) => total + group.tabs.length, 0)} 个标签页
                </span>
              </div>
            </div>
            
            <div className="workspace-actions">
              <button
                className="btn btn-small"
                onClick={() => onOpenWorkspace(workspace.id)}
                title="打开工作空间"
              >
                🚀
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkspaceList;