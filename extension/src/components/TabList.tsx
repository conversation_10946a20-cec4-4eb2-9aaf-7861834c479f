import React from 'react';
import { Tab } from '../types';
import { TabManager } from '../services/TabManager';

interface TabListProps {
  tabs: Tab[];
  searchQuery: string;
}

const TabList: React.FC<TabListProps> = ({ tabs, searchQuery }) => {
  const handleTabClick = async (tab: Tab) => {
    if (tab.id) {
      await TabManager.activateTab(tab.id);
      window.close(); // 关闭popup
    }
  };

  const handleCloseTab = async (tab: Tab, event: React.MouseEvent) => {
    event.stopPropagation();
    if (tab.id) {
      await TabManager.closeTab(tab.id);
      // 这里应该触发重新获取标签页列表
    }
  };

  const getFaviconUrl = (tab: Tab) => {
    if (tab.favIconUrl) {
      return tab.favIconUrl;
    }
    // 使用Google的favicon服务作为备选
    try {
      const url = new URL(tab.url);
      return `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=16`;
    } catch {
      return '🌐';
    }
  };

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="highlight">{part}</mark>
      ) : (
        part
      )
    );
  };

  if (tabs.length === 0) {
    return (
      <div className="tab-list">
        <div className="empty-state">
          <p>{searchQuery ? '未找到匹配的标签页' : '当前窗口没有标签页'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="tab-list">
      <h3>当前标签页 ({tabs.length})</h3>
      <div className="tab-items">
        {tabs.map(tab => (
          <div 
            key={tab.id || tab.url}
            className={`tab-item ${tab.active ? 'active' : ''} ${tab.pinned ? 'pinned' : ''}`}
            onClick={() => handleTabClick(tab)}
          >
            <div className="tab-favicon">
              {typeof getFaviconUrl(tab) === 'string' ? (
                <img 
                  src={getFaviconUrl(tab)} 
                  alt=""
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                    (e.target as HTMLImageElement).nextElementSibling!.textContent = '🌐';
                  }}
                />
              ) : (
                <span>{getFaviconUrl(tab)}</span>
              )}
              <span style={{ display: 'none' }}>🌐</span>
            </div>
            
            <div className="tab-info">
              <div className="tab-title">
                {highlightText(tab.title, searchQuery)}
              </div>
              <div className="tab-url">
                {highlightText(tab.url, searchQuery)}
              </div>
            </div>
            
            <div className="tab-actions">
              {tab.pinned && <span className="pin-indicator" title="已固定">📌</span>}
              <button
                className="close-button"
                onClick={(e) => handleCloseTab(tab, e)}
                title="关闭标签页"
              >
                ✕
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TabList;