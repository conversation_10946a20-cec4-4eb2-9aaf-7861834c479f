import React, { useState, useEffect } from 'react';
import { BookmarkManager } from '../services/BookmarkManager';
import { BookmarkNode, BookmarkGroupMapping, ImportPreview, BookmarkConflict } from '../types';

interface BookmarkImportPanelProps {
  onImportComplete?: (result: { success: number; failed: number; errors: string[] }) => void;
}

export const BookmarkImportPanel: React.FC<BookmarkImportPanelProps> = ({
  onImportComplete
}) => {
  const [step, setStep] = useState<'start' | 'preview' | 'mapping' | 'importing' | 'complete'>('start');
  const [bookmarks, setBookmarks] = useState<BookmarkNode[]>([]);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [mappings, setMappings] = useState<BookmarkGroupMapping[]>([]);
  const [conflicts, setConflicts] = useState<BookmarkConflict[]>([]);
  const [loading, setLoading] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);
  const [selectedMappings, setSelectedMappings] = useState<Set<string>>(new Set());

  const handleStartImport = async () => {
    try {
      setLoading(true);
      setStep('preview');
      
      // 导入书签
      const importedBookmarks = await BookmarkManager.importBookmarks();
      setBookmarks(importedBookmarks);
      
      // 生成预览
      const importPreview = await BookmarkManager.previewImport(importedBookmarks);
      setPreview(importPreview);
      setMappings(importPreview.mappings);
      setConflicts(importPreview.conflicts);
      
      // 默认选择所有映射
      setSelectedMappings(new Set(importPreview.mappings.map(m => m.bookmarkId)));
      
    } catch (error) {
      console.error('Failed to start import:', error);
      alert('导入失败: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const handleMappingChange = (bookmarkId: string, targetGroupId: string, targetGroupName: string) => {
    setMappings(prev => prev.map(mapping => 
      mapping.bookmarkId === bookmarkId 
        ? { ...mapping, targetGroupId, targetGroupName, mappingType: 'manual' as const }
        : mapping
    ));
  };

  const handleSelectionChange = (bookmarkId: string, selected: boolean) => {
    setSelectedMappings(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(bookmarkId);
      } else {
        newSet.delete(bookmarkId);
      }
      return newSet;
    });
  };

  const handleConfirmImport = async () => {
    try {
      setLoading(true);
      setStep('importing');
      
      // 只导入选中的映射
      const selectedMappingsList = mappings.filter(m => selectedMappings.has(m.bookmarkId));
      
      const result = await BookmarkManager.batchImportBookmarks(selectedMappingsList);
      setImportResult(result);
      setStep('complete');
      
      if (onImportComplete) {
        onImportComplete(result);
      }
      
    } catch (error) {
      console.error('Failed to import bookmarks:', error);
      alert('导入失败: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const handleRestart = () => {
    setStep('start');
    setBookmarks([]);
    setPreview(null);
    setMappings([]);
    setConflicts([]);
    setImportResult(null);
    setSelectedMappings(new Set());
  };

  const renderStartStep = () => (
    <div className="import-start">
      <div className="start-header">
        <h3>📚 导入Chrome书签</h3>
        <p>将您的Chrome书签智能导入到AI工作台中，自动分组管理</p>
      </div>
      
      <div className="start-features">
        <div className="feature-item">
          <span className="feature-icon">🔍</span>
          <div>
            <h4>智能分析</h4>
            <p>自动分析书签内容和文件夹结构</p>
          </div>
        </div>
        
        <div className="feature-item">
          <span className="feature-icon">🎯</span>
          <div>
            <h4>智能映射</h4>
            <p>根据内容自动建议分组分配</p>
          </div>
        </div>
        
        <div className="feature-item">
          <span className="feature-icon">⚡</span>
          <div>
            <h4>批量导入</h4>
            <p>支持大量书签的快速批量导入</p>
          </div>
        </div>
      </div>
      
      <button 
        onClick={handleStartImport}
        disabled={loading}
        className="start-btn"
      >
        {loading ? '正在分析书签...' : '开始导入'}
      </button>
    </div>
  );

  const renderPreviewStep = () => (
    <div className="import-preview">
      <div className="preview-header">
        <h3>📋 导入预览</h3>
        <p>检查导入计划，确认后开始导入</p>
      </div>
      
      {preview && (
        <div className="preview-stats">
          <div className="stat-item">
            <span className="stat-number">{preview.totalBookmarks}</span>
            <span className="stat-label">总书签数</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{preview.mappedBookmarks}</span>
            <span className="stat-label">可导入</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{preview.conflictBookmarks}</span>
            <span className="stat-label">冲突</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{preview.newGroupsToCreate}</span>
            <span className="stat-label">新分组</span>
          </div>
        </div>
      )}
      
      {conflicts.length > 0 && (
        <div className="conflicts-section">
          <h4>⚠️ 冲突项目</h4>
          <div className="conflicts-list">
            {conflicts.map(conflict => (
              <div key={conflict.bookmarkId} className="conflict-item">
                <div className="conflict-info">
                  <strong>{conflict.bookmarkTitle}</strong>
                  <span className="conflict-type">{conflict.conflictType}</span>
                </div>
                <span className="conflict-action">{conflict.suggestedAction}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mappings-section">
        <h4>📁 分组映射</h4>
        <div className="mappings-list">
          {mappings.slice(0, 10).map(mapping => (
            <div key={mapping.bookmarkId} className="mapping-item">
              <label className="mapping-checkbox">
                <input
                  type="checkbox"
                  checked={selectedMappings.has(mapping.bookmarkId)}
                  onChange={(e) => handleSelectionChange(mapping.bookmarkId, e.target.checked)}
                />
                <div className="mapping-info">
                  <strong>{mapping.bookmarkTitle}</strong>
                  <span className="mapping-target">→ {mapping.targetGroupName}</span>
                  <span className="mapping-confidence">
                    {mapping.confidence ? `${Math.round(mapping.confidence * 100)}%` : '手动'}
                  </span>
                </div>
              </label>
            </div>
          ))}
          
          {mappings.length > 10 && (
            <div className="more-mappings">
              还有 {mappings.length - 10} 个映射...
            </div>
          )}
        </div>
      </div>
      
      <div className="preview-actions">
        <button onClick={handleRestart} className="secondary-btn">
          重新开始
        </button>
        <button 
          onClick={() => setStep('mapping')}
          className="primary-btn"
        >
          自定义映射
        </button>
        <button 
          onClick={handleConfirmImport}
          disabled={selectedMappings.size === 0}
          className="primary-btn"
        >
          确认导入 ({selectedMappings.size})
        </button>
      </div>
    </div>
  );

  const renderMappingStep = () => (
    <div className="import-mapping">
      <div className="mapping-header">
        <h3>🎯 自定义映射</h3>
        <p>调整书签到分组的映射关系</p>
      </div>
      
      <div className="mapping-controls">
        <button 
          onClick={() => setSelectedMappings(new Set(mappings.map(m => m.bookmarkId)))}
          className="control-btn"
        >
          全选
        </button>
        <button 
          onClick={() => setSelectedMappings(new Set())}
          className="control-btn"
        >
          全不选
        </button>
        <span className="selection-count">
          已选择 {selectedMappings.size} / {mappings.length}
        </span>
      </div>
      
      <div className="detailed-mappings">
        {mappings.map(mapping => (
          <div key={mapping.bookmarkId} className="detailed-mapping-item">
            <label className="mapping-checkbox">
              <input
                type="checkbox"
                checked={selectedMappings.has(mapping.bookmarkId)}
                onChange={(e) => handleSelectionChange(mapping.bookmarkId, e.target.checked)}
              />
            </label>
            
            <div className="bookmark-info">
              <strong>{mapping.bookmarkTitle}</strong>
              {mapping.bookmarkUrl && (
                <span className="bookmark-url">{mapping.bookmarkUrl}</span>
              )}
            </div>
            
            <div className="mapping-selector">
              <select
                value={mapping.targetGroupId}
                onChange={(e) => {
                  const option = e.target.selectedOptions[0];
                  handleMappingChange(
                    mapping.bookmarkId, 
                    e.target.value, 
                    option.text
                  );
                }}
              >
                <option value={mapping.targetGroupId}>{mapping.targetGroupName}</option>
                <option value="favorites">收藏夹</option>
                <option value="reading">稍后阅读</option>
                <option value="work">工作相关</option>
                <option value="personal">个人收藏</option>
              </select>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mapping-actions">
        <button onClick={() => setStep('preview')} className="secondary-btn">
          返回预览
        </button>
        <button 
          onClick={handleConfirmImport}
          disabled={selectedMappings.size === 0}
          className="primary-btn"
        >
          开始导入 ({selectedMappings.size})
        </button>
      </div>
    </div>
  );

  const renderImportingStep = () => (
    <div className="import-progress">
      <div className="progress-header">
        <h3>⚡ 正在导入</h3>
        <p>请稍候，正在导入您的书签...</p>
      </div>
      
      <div className="progress-animation">
        <div className="spinner"></div>
        <p>导入进行中...</p>
      </div>
    </div>
  );

  const renderCompleteStep = () => (
    <div className="import-complete">
      <div className="complete-header">
        <h3>✅ 导入完成</h3>
        <p>书签导入已完成</p>
      </div>
      
      {importResult && (
        <div className="import-results">
          <div className="result-stats">
            <div className="result-item success">
              <span className="result-number">{importResult.success}</span>
              <span className="result-label">成功导入</span>
            </div>
            {importResult.failed > 0 && (
              <div className="result-item error">
                <span className="result-number">{importResult.failed}</span>
                <span className="result-label">导入失败</span>
              </div>
            )}
          </div>
          
          {importResult.errors && importResult.errors.length > 0 && (
            <div className="error-details">
              <h4>错误详情:</h4>
              <ul>
                {importResult.errors.map((error: string, index: number) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      <div className="complete-actions">
        <button onClick={handleRestart} className="primary-btn">
          再次导入
        </button>
      </div>
    </div>
  );

  return (
    <div className="bookmark-import-panel">
      {step === 'start' && renderStartStep()}
      {step === 'preview' && renderPreviewStep()}
      {step === 'mapping' && renderMappingStep()}
      {step === 'importing' && renderImportingStep()}
      {step === 'complete' && renderCompleteStep()}

      <style jsx>{`
        .bookmark-import-panel {
          padding: 20px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          max-height: 500px;
          overflow-y: auto;
        }

        .start-header h3 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 18px;
        }

        .start-header p {
          margin: 0 0 20px 0;
          color: #6b7280;
          font-size: 14px;
        }

        .start-features {
          margin-bottom: 24px;
        }

        .feature-item {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
        }

        .feature-icon {
          font-size: 24px;
        }

        .feature-item h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;
        }

        .feature-item p {
          margin: 0;
          font-size: 12px;
          color: #6b7280;
        }

        .start-btn {
          width: 100%;
          padding: 12px;
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.2s;
        }

        .start-btn:hover:not(:disabled) {
          background: #2563eb;
        }

        .start-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .preview-stats {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 12px;
          margin: 16px 0;
        }

        .stat-item {
          text-align: center;
          padding: 12px;
          background: #f9fafb;
          border-radius: 6px;
        }

        .stat-number {
          display: block;
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
        }

        .stat-label {
          font-size: 12px;
          color: #6b7280;
        }

        .conflicts-section {
          margin: 16px 0;
          padding: 12px;
          background: #fef2f2;
          border-radius: 6px;
          border: 1px solid #fecaca;
        }

        .conflicts-section h4 {
          margin: 0 0 8px 0;
          color: #dc2626;
          font-size: 14px;
        }

        .conflict-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;
          border-bottom: 1px solid #fecaca;
        }

        .conflict-item:last-child {
          border-bottom: none;
        }

        .conflict-type {
          font-size: 12px;
          color: #dc2626;
          margin-left: 8px;
        }

        .mappings-section h4 {
          margin: 16px 0 8px 0;
          color: #1f2937;
          font-size: 14px;
        }

        .mapping-item {
          margin-bottom: 8px;
        }

        .mapping-checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          cursor: pointer;
        }

        .mapping-checkbox:hover {
          background: #f9fafb;
        }

        .mapping-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 2px;
        }

        .mapping-target {
          font-size: 12px;
          color: #6b7280;
        }

        .mapping-confidence {
          font-size: 11px;
          color: #059669;
          font-weight: 500;
        }

        .preview-actions {
          display: flex;
          gap: 8px;
          margin-top: 20px;
        }

        .primary-btn {
          padding: 8px 16px;
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          transition: background 0.2s;
        }

        .primary-btn:hover:not(:disabled) {
          background: #2563eb;
        }

        .primary-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .secondary-btn {
          padding: 8px 16px;
          background: white;
          color: #6b7280;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .secondary-btn:hover {
          background: #f9fafb;
        }

        .progress-animation {
          text-align: center;
          padding: 40px 20px;
        }

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #e5e7eb;
          border-top: 4px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .result-stats {
          display: flex;
          gap: 16px;
          margin: 16px 0;
        }

        .result-item {
          text-align: center;
          padding: 12px;
          border-radius: 6px;
        }

        .result-item.success {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
        }

        .result-item.error {
          background: #fef2f2;
          border: 1px solid #fecaca;
        }

        .result-number {
          display: block;
          font-size: 18px;
          font-weight: 600;
        }

        .result-item.success .result-number {
          color: #059669;
        }

        .result-item.error .result-number {
          color: #dc2626;
        }

        .result-label {
          font-size: 12px;
          color: #6b7280;
        }
      `}</style>
    </div>
  );
};
