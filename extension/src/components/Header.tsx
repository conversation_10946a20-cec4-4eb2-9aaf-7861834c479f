import React from 'react';

interface HeaderProps {
  onSaveCurrentTabs: () => void;
  activeWorkspace: string | null;
}

const Header: React.FC<HeaderProps> = ({ onSaveCurrentTabs, activeWorkspace }) => {
  return (
    <div className="header">
      <div className="header-title">
        <h1>🤖 AI工作台</h1>
        <span className="version">v1.0.0</span>
      </div>
      
      <div className="header-actions">
        <button 
          className="btn btn-primary"
          onClick={onSaveCurrentTabs}
          disabled={!activeWorkspace}
          title={activeWorkspace ? '保存当前标签页到工作空间' : '请先选择工作空间'}
        >
          💾 保存标签页
        </button>
        
        <button 
          className="btn btn-secondary"
          onClick={() => chrome.runtime.openOptionsPage()}
          title="打开设置页面"
        >
          ⚙️
        </button>
      </div>
    </div>
  );
};

export default Header;