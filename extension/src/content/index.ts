// Content Script - 在网页中运行的脚本

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script收到消息:', message);
  
  switch (message.type) {
    case 'GET_PAGE_INFO':
      handleGetPageInfo(sendResponse);
      return true;
      
    case 'HIGHLIGHT_SEARCH':
      handleHighlightSearch(message.query);
      break;
      
    case 'EXTRACT_CONTENT':
      handleExtractContent(sendResponse);
      return true;
  }
});

// 获取页面信息
function handleGetPageInfo(sendResponse: (response: any) => void) {
  try {
    const pageInfo = {
      title: document.title,
      url: window.location.href,
      description: getPageDescription(),
      keywords: getPageKeywords(),
      favicon: getFaviconUrl(),
      timestamp: new Date().toISOString()
    };
    
    sendResponse({ success: true, pageInfo });
  } catch (error) {
    console.error('获取页面信息失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取页面描述
function getPageDescription(): string {
  const metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
  if (metaDescription) {
    return metaDescription.content;
  }
  
  const ogDescription = document.querySelector('meta[property="og:description"]') as HTMLMetaElement;
  if (ogDescription) {
    return ogDescription.content;
  }
  
  // 尝试从页面内容中提取描述
  const firstParagraph = document.querySelector('p');
  if (firstParagraph && firstParagraph.textContent) {
    return firstParagraph.textContent.substring(0, 200);
  }
  
  return '';
}

// 获取页面关键词
function getPageKeywords(): string[] {
  const metaKeywords = document.querySelector('meta[name="keywords"]') as HTMLMetaElement;
  if (metaKeywords) {
    return metaKeywords.content.split(',').map(k => k.trim());
  }
  
  // 从标题中提取关键词
  const titleWords = document.title.split(/\s+/).filter(word => word.length > 2);
  return titleWords.slice(0, 5);
}

// 获取网站图标URL
function getFaviconUrl(): string {
  const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement ||
                 document.querySelector('link[rel="shortcut icon"]') as HTMLLinkElement ||
                 document.querySelector('link[rel="apple-touch-icon"]') as HTMLLinkElement;
  
  if (favicon) {
    return new URL(favicon.href, window.location.href).href;
  }
  
  // 默认favicon路径
  return new URL('/favicon.ico', window.location.origin).href;
}

// 高亮搜索结果
function handleHighlightSearch(query: string) {
  if (!query) return;
  
  // 移除之前的高亮
  removeHighlights();
  
  // 创建高亮样式
  if (!document.getElementById('ai-workspace-highlight-style')) {
    const style = document.createElement('style');
    style.id = 'ai-workspace-highlight-style';
    style.textContent = `
      .ai-workspace-highlight {
        background-color: #fef3c7 !important;
        color: #92400e !important;
        padding: 1px 2px !important;
        border-radius: 2px !important;
      }
    `;
    document.head.appendChild(style);
  }
  
  // 高亮文本
  highlightText(document.body, query);
}

// 移除高亮
function removeHighlights() {
  const highlights = document.querySelectorAll('.ai-workspace-highlight');
  highlights.forEach(highlight => {
    const parent = highlight.parentNode;
    if (parent) {
      parent.replaceChild(document.createTextNode(highlight.textContent || ''), highlight);
      parent.normalize();
    }
  });
}

// 高亮文本节点
function highlightText(node: Node, query: string) {
  if (node.nodeType === Node.TEXT_NODE) {
    const text = node.textContent || '';
    const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
    
    if (regex.test(text)) {
      const span = document.createElement('span');
      span.innerHTML = text.replace(regex, '<span class="ai-workspace-highlight">$1</span>');
      node.parentNode?.replaceChild(span, node);
    }
  } else if (node.nodeType === Node.ELEMENT_NODE) {
    const element = node as Element;
    // 跳过script、style等标签
    if (!['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(element.tagName)) {
      Array.from(element.childNodes).forEach(child => {
        highlightText(child, query);
      });
    }
  }
}

// 转义正则表达式特殊字符
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 提取页面内容
function handleExtractContent(sendResponse: (response: any) => void) {
  try {
    const content = {
      title: document.title,
      headings: extractHeadings(),
      links: extractLinks(),
      images: extractImages(),
      text: extractMainText()
    };
    
    sendResponse({ success: true, content });
  } catch (error) {
    console.error('提取页面内容失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 提取标题
function extractHeadings(): Array<{level: number, text: string}> {
  const headings: Array<{level: number, text: string}> = [];
  const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  
  headingElements.forEach(heading => {
    const level = parseInt(heading.tagName.substring(1));
    const text = heading.textContent?.trim() || '';
    if (text) {
      headings.push({ level, text });
    }
  });
  
  return headings;
}

// 提取链接
function extractLinks(): Array<{text: string, url: string}> {
  const links: Array<{text: string, url: string}> = [];
  const linkElements = document.querySelectorAll('a[href]');
  
  linkElements.forEach(link => {
    const text = link.textContent?.trim() || '';
    const href = (link as HTMLAnchorElement).href;
    if (text && href) {
      links.push({ text, url: href });
    }
  });
  
  return links.slice(0, 50); // 限制数量
}

// 提取图片
function extractImages(): Array<{alt: string, src: string}> {
  const images: Array<{alt: string, src: string}> = [];
  const imageElements = document.querySelectorAll('img[src]');
  
  imageElements.forEach(img => {
    const alt = (img as HTMLImageElement).alt || '';
    const src = (img as HTMLImageElement).src;
    if (src) {
      images.push({ alt, src });
    }
  });
  
  return images.slice(0, 20); // 限制数量
}

// 提取主要文本内容
function extractMainText(): string {
  // 尝试找到主要内容区域
  const mainSelectors = [
    'main',
    'article',
    '.content',
    '.main-content',
    '#content',
    '#main'
  ];
  
  for (const selector of mainSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      return element.textContent?.trim().substring(0, 1000) || '';
    }
  }
  
  // 如果没有找到主要内容区域，提取body的文本
  return document.body.textContent?.trim().substring(0, 1000) || '';
}

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

function initialize() {
  console.log('AI工作台 Content Script 已加载');
  
  // 可以在这里添加页面加载完成后的初始化逻辑
  // 比如自动检测页面类型，为AI工具网站添加特殊标记等
  detectAIWebsite();
}

// 检测是否为AI工具网站
function detectAIWebsite() {
  const hostname = window.location.hostname.toLowerCase();
  const aiWebsites = [
    'chat.openai.com',
    'gemini.google.com',
    'claude.ai',
    'perplexity.ai',
    'deepask.cc'
  ];
  
  if (aiWebsites.some(site => hostname.includes(site))) {
    console.log('检测到AI工具网站:', hostname);
    // 可以添加特殊的处理逻辑
    document.body.setAttribute('data-ai-workspace-site', 'true');
  }
}

export {};