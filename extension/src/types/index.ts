// Chrome Extension 相关类型定义

export interface Tab {
  id?: number;
  title: string;
  url: string;
  favIconUrl?: string;
  active?: boolean;
  pinned?: boolean;
  groupId?: number;
  windowId?: number;
  index?: number;
}

export interface TabGroup {
  id: string;
  name: string;
  color: string;
  icon?: string;
  tabs: Tab[];
  isPreset?: boolean;
  presetType?: string;
  sortOrder?: number;
}

export interface Workspace {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  groups: TabGroup[];
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PresetGroup {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  type: 'ai_primary' | 'ai_secondary' | 'ai_tools' | 'tech_forums' | 'collaboration' | 'productivity';
  websites: PresetWebsite[];
}

export interface PresetWebsite {
  name: string;
  url: string;
  description: string;
  tags: string[];
  favicon?: string;
}

// 收藏相关类型
export interface Favorite {
  id: string;
  userId: string;
  title: string;
  url: string;
  faviconUrl?: string;
  description?: string;
  tags: string[];
  category: string;
  isQuickAccess: boolean;
  visitCount: number;
  lastVisitedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 搜索相关类型
export interface SearchResult {
  id: string;
  type: 'tab' | 'favorite' | 'workspace' | 'group';
  title: string;
  url?: string;
  description?: string;
  faviconUrl?: string;
  workspaceName?: string;
  groupName?: string;
  relevanceScore: number;
  matchedFields: string[];
}

export interface SearchFilter {
  type?: 'tab' | 'favorite' | 'workspace' | 'group' | 'all';
  workspace?: string;
  group?: string;
  category?: string;
  tags?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface SearchOptions {
  query: string;
  filters: SearchFilter;
  sortBy: 'relevance' | 'date' | 'visits' | 'alphabetical';
  sortOrder: 'asc' | 'desc';
  limit: number;
  offset: number;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  defaultWorkspace?: string;
  autoSave: boolean;
  localSyncEnabled: boolean;
  shortcuts: {
    toggleWorkspace: string;
    quickSearch: string;
  };
  aiEnabled?: boolean;
}

// 本地同步配置接口
export interface LocalSyncConfig {
  autoExport: boolean;
  exportInterval: number; // 分钟
  maxBackups: number;
  lastExportTime?: string;
  deviceId?: string;
}

// 本地同步状态接口
export interface LocalSyncStatus {
  isExporting: boolean;
  lastExportTime?: string;
  pendingChanges: number;
  lastError?: string;
  backupCount: number;
}

export interface SyncData {
  workspaces: Workspace[];
  settings: UserSettings;
  lastSyncAt: string;
  deviceId: string;
}

// API 相关类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
}

export interface User {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatarUrl?: string;
  settings?: UserSettings;
  createdAt: string;
  lastLoginAt?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt: string;
}

// 增强功能相关类型定义

// 书签导入相关类型
export interface BookmarkNode extends chrome.bookmarks.BookmarkTreeNode {
  mappedGroupId?: string;
  importStatus?: 'pending' | 'imported' | 'skipped' | 'error';
  conflictReason?: string;
  originalPath?: string;
}

export interface BookmarkGroupMapping {
  bookmarkId: string;
  bookmarkTitle: string;
  bookmarkUrl?: string;
  targetGroupId: string;
  targetGroupName: string;
  mappingType: 'auto' | 'manual' | 'suggested';
  confidence?: number;
}

export interface ImportPreview {
  totalBookmarks: number;
  mappedBookmarks: number;
  unmappedBookmarks: number;
  conflictBookmarks: number;
  newGroupsToCreate: number;
  mappings: BookmarkGroupMapping[];
  conflicts: BookmarkConflict[];
}

export interface BookmarkConflict {
  bookmarkId: string;
  bookmarkTitle: string;
  bookmarkUrl: string;
  conflictType: 'duplicate_url' | 'duplicate_title' | 'invalid_url' | 'permission_denied';
  existingItem?: {
    id: string;
    title: string;
    type: 'favorite' | 'bookmark';
  };
  suggestedAction: 'skip' | 'replace' | 'rename' | 'merge';
}

// AI助手相关类型
export interface AICommand {
  id: string;
  type: 'search' | 'add_to_group' | 'create_group' | 'organize' | 'find_similar' | 'bulk_action';
  intent: string;
  entities: AIEntity[];
  parameters: Record<string, any>;
  confidence: number;
  originalText: string;
  timestamp: string;
}

export interface AIEntity {
  type: 'website' | 'group' | 'category' | 'action' | 'filter' | 'number';
  value: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
}

export interface AICommandResult {
  success: boolean;
  message: string;
  data?: any;
  affectedItems?: {
    type: 'tab' | 'bookmark' | 'group' | 'workspace';
    id: string;
    title: string;
    action: 'created' | 'updated' | 'deleted' | 'moved';
  }[];
  suggestedActions?: AISuggestedAction[];
}

export interface AISuggestedAction {
  id: string;
  type: 'create_group' | 'move_tabs' | 'pin_tabs' | 'organize_workspace';
  title: string;
  description: string;
  confidence: number;
  parameters: Record<string, any>;
}

export interface AIConversation {
  id: string;
  messages: AIMessage[];
  createdAt: string;
  updatedAt: string;
  context?: {
    activeWorkspace?: string;
    currentTabs?: Tab[];
    recentActions?: string[];
  };
}

export interface AIMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  command?: AICommand;
  result?: AICommandResult;
}

// 智能分类相关类型
export interface ClassificationRule {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  priority: number;
  enabled: boolean;
  rules: {
    domains?: string[];
    urlPatterns?: string[];
    titleKeywords?: string[];
    contentKeywords?: string[];
    excludeDomains?: string[];
    excludePatterns?: string[];
  };
  userDefined: boolean;
  accuracy?: number;
  usageCount?: number;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ClassificationResult {
  ruleId: string;
  ruleName: string;
  confidence: number;
  matchedCriteria: string[];
  suggestedGroup?: string;
  reasoning: string;
}

export interface ClassificationSuggestion {
  tabId: number;
  tabTitle: string;
  tabUrl: string;
  suggestions: ClassificationResult[];
  userFeedback?: 'accepted' | 'rejected' | 'modified';
  finalGroupId?: string;
  timestamp: string;
}

export interface ClassificationTrainingData {
  url: string;
  title: string;
  content?: string;
  groupId: string;
  groupName: string;
  userConfirmed: boolean;
  features: {
    domain: string;
    pathSegments: string[];
    titleWords: string[];
    contentWords?: string[];
  };
  timestamp: string;
}

// 组级固定相关类型
export interface GroupPinningPolicy {
  groupId: number;
  policy: 'auto' | 'manual' | 'disabled';
  pinNewTabs: boolean;
  unpinOnRemove: boolean;
  exceptions: number[]; // 例外的标签页ID
  createdAt: string;
  updatedAt: string;
}

export interface PinningAction {
  type: 'pin' | 'unpin' | 'policy_change';
  tabIds: number[];
  groupId?: number;
  policy?: string;
  reason: 'user_action' | 'group_policy' | 'auto_classification';
  timestamp: string;
}

// 扩展现有Tab接口
export interface EnhancedTab extends Tab {
  groupPinningPolicy?: 'auto' | 'manual' | 'disabled';
  classificationSuggestions?: ClassificationResult[];
  aiTags?: string[];
  lastClassified?: string;
  userModified?: boolean;
}

// API配置相关类型
export interface AIAPIConfig {
  provider: 'siliconflow' | 'openai' | 'anthropic' | 'local';
  apiKey: string;
  baseUrl: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  enabled: boolean;
}

// 统计和分析相关类型
export interface UsageStatistics {
  totalTabs: number;
  totalGroups: number;
  totalBookmarks: number;
  autoClassified: number;
  manuallyOrganized: number;
  aiCommandsExecuted: number;
  bookmarksImported: number;
  averageTabsPerGroup: number;
  mostUsedGroups: Array<{
    groupId: string;
    groupName: string;
    usageCount: number;
  }>;
  classificationAccuracy: number;
  lastUpdated: string;
}