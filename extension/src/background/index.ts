// Chrome Extension Background Script (Service Worker)
// 工作区管理器 - 专注于工作区管理功能

// 类型定义
interface Workspace {
  id: string;
  name: string;
  icon: string;
  color: string;
  groups: TabGroup[];
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TabGroup {
  id: string;
  name: string;
  color: string;
  tabs: TabInfo[];
}

interface TabInfo {
  id: number;
  title: string;
  url: string;
  favIconUrl?: string;
  groupId?: number;
  pinned?: boolean;
}

// 全局状态
let currentWorkspaceId: string = 'default';
let workspaces = new Map<string, Workspace>();

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('工作区管理器扩展已安装', details);

  // 初始化工作区管理器
  await initializeWorkspaceManager();

  if (details.reason === 'install') {
    // 首次安装时创建默认工作空间
    await initializeDefaultWorkspaces();

    console.log('扩展安装完成，请点击扩展图标打开工作区管理器');
  }
});

// 初始化工作区管理器
async function initializeWorkspaceManager() {
  try {
    console.log('初始化工作区管理器...');

    // 加载存储的工作区数据
    await loadWorkspacesFromStorage();

    // 设置标签页监听器
    setupTabListeners();

    // 设置侧边栏
    await setupSidePanel();

    console.log('工作区管理器初始化成功');
  } catch (error) {
    console.error('工作区管理器初始化失败:', error);
  }
}

// 加载工作区数据
async function loadWorkspacesFromStorage() {
  try {
    const result = await chrome.storage.local.get(['workspaces', 'currentWorkspaceId']);

    if (result.workspaces) {
      workspaces = new Map(Object.entries(result.workspaces));
    }

    if (result.currentWorkspaceId) {
      currentWorkspaceId = result.currentWorkspaceId;
    }

    // 如果没有工作区，创建默认工作区
    if (workspaces.size === 0) {
      console.log('没有找到工作区，创建默认工作区...');
      await initializeDefaultWorkspaces();
    }

    console.log('已加载工作区数据:', workspaces.size, '个工作区');
  } catch (error) {
    console.error('加载工作区数据失败:', error);
  }
}

// 初始化默认工作空间
async function initializeDefaultWorkspaces() {
  try {
    // AI工作主力
    const aiMainWorkspace: Workspace = {
      id: 'ai-main',
      name: 'AI工作主力',
      color: '#8b5cf6',
      icon: '🤖',
      groups: [{
        id: 'ai-main-group',
        name: 'AI主力工具',
        color: 'purple',
        tabs: [
          { id: 0, title: 'ChatGPT', url: 'https://chat.openai.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Gemini', url: 'https://gemini.google.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'LobeHub', url: 'https://chat-preview.lobehub.com/discover', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Perplexity', url: 'https://www.perplexity.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Grok', url: 'https://grok.x.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'AI Studio', url: 'https://aistudio.google.com/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // AI次选
    const aiSecondaryWorkspace: Workspace = {
      id: 'ai-secondary',
      name: 'AI次选',
      color: '#06b6d4',
      icon: '🔧',
      groups: [{
        id: 'ai-secondary-group',
        name: 'AI次选工具',
        color: 'cyan',
        tabs: [
          { id: 0, title: 'DeepAsk', url: 'https://deepask.cc/#/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'GPTFun', url: 'https://fun4ai.khthink.cn/login', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'C佬', url: 'https://new.clivia.fun/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'A佬', url: 'https://aabao.eu.cc/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'H佬', url: 'https://work.haomo.de/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Claude', url: 'https://demo.fuclaude.com/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // AI其他工具
    const aiToolsWorkspace: Workspace = {
      id: 'ai-tools',
      name: 'AI其他工具',
      color: '#f59e0b',
      icon: '⚡',
      groups: [{
        id: 'ai-tools-group',
        name: 'AI辅助工具',
        color: 'yellow',
        tabs: [
          { id: 0, title: 'Dify', url: 'https://dify.ai/', favIconUrl: '', groupId: 0 },
          { id: 0, title: '提示词优化', url: 'https://promptpilot.volcengine.com/home', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 技术论坛
    const techForumWorkspace: Workspace = {
      id: 'tech-forum',
      name: '技术论坛',
      color: '#10b981',
      icon: '💬',
      groups: [{
        id: 'tech-forum-group',
        name: '技术社区',
        color: 'green',
        tabs: [
          { id: 0, title: 'Linux.do', url: 'https://linux.do/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'NodeLoc', url: 'https://nodeloc.cc/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'NodeSeek', url: 'https://www.nodeseek.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Appinn', url: 'https://meta.appinn.net/latest', favIconUrl: '', groupId: 0 },
          { id: 0, title: 'Follow', url: 'https://app.follow.is/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 日常工作
    const dailyWorkWorkspace: Workspace = {
      id: 'daily-work',
      name: '日常工作',
      color: '#ef4444',
      icon: '💼',
      groups: [{
        id: 'daily-work-group',
        name: '工作平台',
        color: 'red',
        tabs: [
          { id: 0, title: '语雀', url: 'https://www.yuque.com/', favIconUrl: '', groupId: 0 },
          { id: 0, title: '飞书', url: 'https://p1b9rnchwd.feishu.cn/drive/home/', favIconUrl: '', groupId: 0 }
        ]
      }],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 创建默认工作区
    const defaultWorkspace: Workspace = {
      id: 'default',
      name: '默认工作区',
      color: '#3b82f6',
      icon: '🏠',
      groups: [],
      isDefault: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 添加所有工作区
    workspaces.set('default', defaultWorkspace);
    workspaces.set('ai-main', aiMainWorkspace);
    workspaces.set('ai-secondary', aiSecondaryWorkspace);
    workspaces.set('ai-tools', aiToolsWorkspace);
    workspaces.set('tech-forum', techForumWorkspace);
    workspaces.set('daily-work', dailyWorkWorkspace);

    // 设置默认当前工作区
    currentWorkspaceId = 'ai-main';

    await saveWorkspacesToStorage();
    console.log('默认工作区已创建:', workspaces.size, '个工作区');
  } catch (error) {
    console.error('创建默认工作区失败:', error);
  }
}

// 设置标签页监听器
function setupTabListeners() {
  // 监听标签页创建
  chrome.tabs.onCreated.addListener(async (tab) => {
    await handleTabCreated(tab);
  });

  // 监听标签页更新
  chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
      await handleTabUpdated(tab);
    }
  });

  // 监听标签页移除
  chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
    await handleTabRemoved(tabId);
  });

  // 标签页分组功能已移除，改用固定功能
}

// 设置侧边栏
async function setupSidePanel() {
  try {
    // 设置侧边栏默认路径
    await chrome.sidePanel.setOptions({
      path: 'sidepanel.html',
      enabled: true
    });

    // 监听action点击，打开侧边栏
    chrome.action.onClicked.addListener(async (tab) => {
      await chrome.sidePanel.open({ windowId: tab.windowId });
    });
  } catch (error) {
    console.error('设置侧边栏失败:', error);
  }
}

// 打开侧边栏
async function openSidePanel() {
  try {
    const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (currentTab?.windowId) {
      await chrome.sidePanel.open({ windowId: currentTab.windowId });
    }
  } catch (error) {
    console.error('打开侧边栏失败:', error);
  }
}

// 标签页事件处理函数
async function handleTabCreated(tab: chrome.tabs.Tab) {
  console.log('标签页创建:', tab.title, tab.url);
  // 可以在这里添加自动分组逻辑
}

async function handleTabUpdated(tab: chrome.tabs.Tab) {
  console.log('标签页更新:', tab.title, tab.url);
  // 可以在这里添加自动分类逻辑
}

async function handleTabRemoved(tabId: number) {
  console.log('标签页移除:', tabId);
  // 从工作区中移除对应的标签页记录
}

// 标签页分组处理函数已移除，改用固定功能

// 保存工作区数据到存储
async function saveWorkspacesToStorage() {
  try {
    const workspacesObj = Object.fromEntries(workspaces);
    await chrome.storage.local.set({
      workspaces: workspacesObj,
      currentWorkspaceId: currentWorkspaceId
    });
  } catch (error) {
    console.error('保存工作区数据失败:', error);
  }
}

// 监听快捷键命令
chrome.commands.onCommand.addListener(async (command) => {
  console.log('快捷键命令:', command);

  switch (command) {
    case 'toggle-workspace':
      await handleToggleWorkspace();
      break;
    case 'open-workspace-manager':
      // 快捷键触发也算用户手势，可以打开侧边栏
      await openSidePanel();
      break;
  }
});

// 切换工作空间
async function handleToggleWorkspace() {
  try {
    // 获取所有工作区，切换到下一个
    const workspaceIds = Array.from(workspaces.keys());
    const currentIndex = workspaceIds.indexOf(currentWorkspaceId);
    const nextIndex = (currentIndex + 1) % workspaceIds.length;
    const nextWorkspaceId = workspaceIds[nextIndex];

    await switchToWorkspace(nextWorkspaceId);
  } catch (error) {
    console.error('切换工作空间失败:', error);
  }
}

// 监听来自sidepanel的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);

  switch (message.action) {
    case 'GET_CURRENT_TABS':
      handleGetCurrentTabs(sendResponse);
      return true; // 保持消息通道开放

    case 'GET_CURRENT_WORKSPACE':
      handleGetCurrentWorkspace(sendResponse);
      return true;

    case 'GET_WORKSPACES':
      handleGetWorkspaces(sendResponse);
      return true;

    case 'GET_WORKSPACE':
      handleGetWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'SWITCH_WORKSPACE':
      handleSwitchWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'CREATE_WORKSPACE':
      handleCreateWorkspace(message, sendResponse);
      return true;

    case 'AI_AUTO_GROUP_PREVIEW':
      handleAIAutoGroupPreview(message.workspaceId, sendResponse);
      return true;

    case 'AI_AUTO_GROUP_APPLY':
      handleAIAutoGroupApply(message, sendResponse);
      return true;

    case 'ADD_TAB_TO_WORKSPACE':
      handleAddTabToWorkspace(message, sendResponse);
      return true;

    case 'AI_ANALYZE_URL':
      handleAIAnalyzeURL(message, sendResponse);
      return true;

    case 'HEALTH_CHECK':
      handleHealthCheck(sendResponse);
      return true;

    case 'DELETE_WORKSPACE':
      console.log('🗑️ 处理删除工作区请求:', message);
      handleDeleteWorkspace(message.workspaceId, sendResponse);
      return true;

    case 'DELETE_TAB_FROM_WORKSPACE':
      console.log('🗑️ 处理删除标签页请求:', message);
      handleDeleteTabFromWorkspace(message, sendResponse);
      return true;

    case 'BATCH_DELETE_TABS_FROM_WORKSPACE':
      handleBatchDeleteTabsFromWorkspace(message, sendResponse);
      return true;

    case 'VERIFY_PINNING':
      handleVerifyPinning(message.workspaceId, sendResponse);
      return true;

    case 'FORCE_APPLY_PINNING':
      handleForceApplyPinning(message.workspaceId, sendResponse);
      return true;

    case 'SYNC_WORKSPACE_TO_CLOUD':
      handleSyncWorkspaceToCloud(message.workspaceId, sendResponse);
      return true;

    case 'RESTORE_WORKSPACE_FROM_CLOUD':
      handleRestoreWorkspaceFromCloud(message.workspaceId, sendResponse);
      return true;

    case 'GET_SYNC_STATUS':
      handleGetSyncStatus(sendResponse);
      return true;

    case 'ENABLE_CLOUD_SYNC':
      handleEnableCloudSync(message.enabled, sendResponse);
      return true;
  }
});

// 消息处理函数实现

// 健康检查处理
async function handleHealthCheck(sendResponse: (response: any) => void) {
  try {
    sendResponse({
      success: true,
      timestamp: Date.now(),
      workspaceId: currentWorkspaceId,
      status: 'healthy'
    });
  } catch (error: any) {
    console.error('健康检查失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 删除工作区处理（增强版本，添加详细日志）
async function handleDeleteWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    console.log(`🗑️ [DEBUG-DELETE] ========== 开始删除工作区 ==========`);
    console.log(`🗑️ [DEBUG-DELETE] 工作区ID: ${workspaceId}`);

    if (!workspaceId) {
      console.error('❌ [DEBUG-DELETE] 工作区ID为空');
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    // 检查工作区是否存在
    console.log(`🔍 [DEBUG-DELETE] 检查工作区是否存在...`);
    console.log(`🔍 [DEBUG-DELETE] 当前工作区总数: ${workspaces.size}`);
    console.log(`🔍 [DEBUG-DELETE] 所有工作区ID:`, Array.from(workspaces.keys()));

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      console.error(`❌ [DEBUG-DELETE] 工作区不存在: ${workspaceId}`);
      console.error(`❌ [DEBUG-DELETE] 可用的工作区:`, Array.from(workspaces.entries()).map(([id, ws]) => ({ id, name: ws.name })));
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    console.log(`📋 [DEBUG-DELETE] 找到工作区: ${workspace.name}, 是否为默认: ${workspace.isDefault}`);

    // 检查是否是默认工作区
    if (workspace.isDefault) {
      console.error('❌ [DEBUG-DELETE] 尝试删除默认工作区');
      sendResponse({ success: false, error: '不能删除默认工作区' });
      return;
    }

    // 删除工作区
    console.log(`🗑️ [DEBUG-DELETE] 执行删除操作...`);
    const deleted = workspaces.delete(workspaceId);
    console.log(`🗑️ [DEBUG-DELETE] 删除操作结果: ${deleted}`);
    console.log(`🗑️ [DEBUG-DELETE] 删除后工作区总数: ${workspaces.size}`);

    if (deleted) {
      console.log(`💾 [DEBUG-DELETE] 保存工作区数据到存储...`);
      await saveWorkspacesToStorage();
      console.log(`💾 [DEBUG-DELETE] 工作区数据已保存`);

      // 如果删除的是当前工作区，切换到默认工作区
      if (currentWorkspaceId === workspaceId) {
        console.log(`🔄 [DEBUG-DELETE] 删除的是当前工作区，需要切换到默认工作区`);
        console.log(`🔄 [DEBUG-DELETE] 原当前工作区ID: ${currentWorkspaceId}`);

        const defaultWorkspace = Array.from(workspaces.values()).find(ws => ws.isDefault);
        if (defaultWorkspace) {
          currentWorkspaceId = defaultWorkspace.id;
          await saveWorkspacesToStorage();
          console.log(`✅ [DEBUG-DELETE] 已切换到默认工作区: ${defaultWorkspace.name} (ID: ${defaultWorkspace.id})`);
        } else {
          console.warn(`⚠️ [DEBUG-DELETE] 未找到默认工作区，重置当前工作区ID`);
          currentWorkspaceId = '';
        }
      }

      console.log(`✅ [DEBUG-DELETE] 工作区删除成功`);
      sendResponse({ success: true, message: '工作区已删除' });
    } else {
      console.error(`❌ [DEBUG-DELETE] 删除操作失败`);
      sendResponse({ success: false, error: '删除操作失败' });
    }
  } catch (error: any) {
    console.error(`❌ [DEBUG-DELETE] 删除工作区异常:`, error);
    console.error(`❌ [DEBUG-DELETE] 错误堆栈:`, error.stack);
    sendResponse({ success: false, error: error.message });
  }
}

// 从工作区删除标签页（修复版本，删除真实的浏览器标签页）
async function handleDeleteTabFromWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    console.log('🗑️ 开始删除标签页:', data);

    const { tabId, workspaceId } = data;

    if (!tabId || !workspaceId) {
      console.error('❌ 参数不完整:', { tabId, workspaceId });
      sendResponse({ success: false, error: '参数不完整' });
      return;
    }

    const tabIdInt = parseInt(tabId);
    console.log(`🗑️ 要删除的标签页ID: ${tabIdInt}`);

    // 首先删除真实的浏览器标签页
    try {
      await chrome.tabs.remove(tabIdInt);
      console.log(`✅ 成功删除浏览器标签页 ID: ${tabIdInt}`);
    } catch (error) {
      console.error(`❌ 删除浏览器标签页失败 ID: ${tabIdInt}`, error);
      sendResponse({ success: false, error: '删除浏览器标签页失败' });
      return;
    }

    // 然后从工作区配置中移除（如果存在）
    const workspace = workspaces.get(workspaceId);
    if (workspace && workspace.groups) {
      console.log('📋 找到工作区:', workspace.name, '分组数量:', workspace.groups.length);

      let tabDeleted = false;
      let deletedTabInfo = null;

      for (const group of workspace.groups) {
        console.log(`🔍 检查分组 ${group.name}，标签页数量: ${group.tabs.length}`);

        const tabIndex = group.tabs.findIndex(tab => {
          const matches = tab.id === tabIdInt;
          console.log(`  检查标签页 ${tab.id} (${tab.title}) 是否匹配 ${tabIdInt}: ${matches}`);
          return matches;
        });

        if (tabIndex !== -1) {
          deletedTabInfo = group.tabs[tabIndex];
          group.tabs.splice(tabIndex, 1);
          tabDeleted = true;
          console.log(`✅ 从分组 ${group.name} 删除标签页配置: ${deletedTabInfo.title}`);
          break;
        }
      }

      if (tabDeleted) {
        workspace.updatedAt = new Date().toISOString();
        workspaces.set(workspaceId, workspace);
        await saveWorkspacesToStorage();
        console.log('✅ 工作区配置已更新');
      }
    }

    sendResponse({ success: true, message: '标签页已删除' });
  } catch (error: any) {
    console.error('❌ 删除标签页异常:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 批量删除标签页（修复版本，删除真实的浏览器标签页）
async function handleBatchDeleteTabsFromWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    const { tabIds, workspaceId } = data;
    console.log('🗑️ 批量删除标签页请求:', { tabIds, workspaceId });

    if (!tabIds || !Array.isArray(tabIds) || !workspaceId) {
      sendResponse({ success: false, error: '参数不完整' });
      return;
    }

    let deletedCount = 0;
    let failedCount = 0;
    const tabIdsToDelete = tabIds.map(id => parseInt(id));
    console.log('🗑️ 要删除的标签页ID:', tabIdsToDelete);

    // 删除真实的浏览器标签页
    for (const tabId of tabIdsToDelete) {
      try {
        console.log(`🗑️ 尝试删除标签页 ID: ${tabId}`);
        await chrome.tabs.remove(tabId);
        deletedCount++;
        console.log(`✅ 成功删除标签页 ID: ${tabId}`);
      } catch (error) {
        console.error(`❌ 删除标签页失败 ID: ${tabId}`, error);
        failedCount++;
      }
    }

    // 同时从工作区配置中移除（如果存在）
    const workspace = workspaces.get(workspaceId);
    if (workspace && workspace.groups) {
      for (const group of workspace.groups) {
        const originalLength = group.tabs.length;
        group.tabs = group.tabs.filter(tab => !tabIdsToDelete.includes(tab.id || 0));
        const removedFromConfig = originalLength - group.tabs.length;
        if (removedFromConfig > 0) {
          console.log(`🗑️ 从工作区配置中移除了 ${removedFromConfig} 个标签页`);
        }
      }

      if (deletedCount > 0) {
        workspace.updatedAt = new Date().toISOString();
        workspaces.set(workspaceId, workspace);
        await saveWorkspacesToStorage();
      }
    }

    if (deletedCount > 0) {
      const message = failedCount > 0
        ? `已删除 ${deletedCount} 个标签页，${failedCount} 个失败`
        : `已删除 ${deletedCount} 个标签页`;
      sendResponse({ success: true, message, deletedCount, failedCount });
    } else {
      sendResponse({ success: false, error: '没有成功删除任何标签页' });
    }
  } catch (error: any) {
    console.error('批量删除标签页失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 验证固定状态处理
async function handleVerifyPinning(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);
    const allTabs = await chrome.tabs.query({ currentWindow: true });

    const verificationResults = [];
    let correctCount = 0;
    let incorrectCount = 0;

    for (const tab of allTabs) {
      if (!tab.id || !tab.url) continue;

      const shouldBePinned = pinnedUrls.some(pattern => tab.url!.includes(pattern));
      const currentlyPinned = tab.pinned || false;
      const isCorrect = shouldBePinned === currentlyPinned;

      if (isCorrect) {
        correctCount++;
      } else {
        incorrectCount++;
      }

      verificationResults.push({
        tabId: tab.id,
        title: tab.title,
        url: tab.url,
        shouldBePinned,
        currentlyPinned,
        isCorrect
      });
    }

    sendResponse({
      success: true,
      results: verificationResults,
      summary: {
        total: allTabs.length,
        correct: correctCount,
        incorrect: incorrectCount,
        accuracy: allTabs.length > 0 ? (correctCount / allTabs.length * 100).toFixed(1) : '0'
      }
    });
  } catch (error: any) {
    console.error('验证固定状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 强制应用固定设置处理
async function handleForceApplyPinning(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      sendResponse({ success: false, error: '工作区不存在' });
      return;
    }

    console.log(`强制应用工作区 ${workspaceId} 的固定设置...`);
    await applyWorkspacePinning(workspace);

    sendResponse({
      success: true,
      message: '固定设置已强制应用',
      workspaceId
    });
  } catch (error: any) {
    console.error('强制应用固定设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 同步工作区到云端处理
async function handleSyncWorkspaceToCloud(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    await syncWorkspaceToCloud(workspaceId);
    sendResponse({ success: true, message: '工作区已同步到云端' });
  } catch (error: any) {
    console.error('同步工作区到云端失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 从云端恢复工作区处理
async function handleRestoreWorkspaceFromCloud(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    if (!workspaceId) {
      sendResponse({ success: false, error: '工作区ID不能为空' });
      return;
    }

    const result = await chrome.storage.sync.get([`cloud_workspace_${workspaceId}`]);
    const cloudData = result[`cloud_workspace_${workspaceId}`];

    if (!cloudData) {
      sendResponse({ success: false, error: '云端没有找到该工作区数据' });
      return;
    }

    // 恢复工作区数据
    workspaces.set(workspaceId, cloudData.workspace);
    await saveWorkspacesToStorage();

    sendResponse({
      success: true,
      message: '工作区已从云端恢复',
      workspace: cloudData.workspace,
      timestamp: cloudData.timestamp
    });
  } catch (error: any) {
    console.error('从云端恢复工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取同步状态处理
async function handleGetSyncStatus(sendResponse: (response: any) => void) {
  try {
    const settings = await chrome.storage.sync.get(['cloudSyncEnabled']);
    const deviceId = await getDeviceId();
    const lastSync = await chrome.storage.local.get(['lastSyncTime']);

    // 获取云端工作区列表
    const allCloudData = await chrome.storage.sync.get();
    const cloudWorkspaces = Object.keys(allCloudData)
      .filter(key => key.startsWith('cloud_workspace_'))
      .map(key => ({
        workspaceId: key.replace('cloud_workspace_', ''),
        timestamp: allCloudData[key].timestamp,
        deviceId: allCloudData[key].deviceId
      }));

    sendResponse({
      success: true,
      status: {
        enabled: settings.cloudSyncEnabled || false,
        deviceId,
        lastSyncTime: lastSync.lastSyncTime,
        cloudWorkspaces,
        localWorkspaces: Array.from(workspaces.keys())
      }
    });
  } catch (error: any) {
    console.error('获取同步状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 启用/禁用云同步处理
async function handleEnableCloudSync(enabled: boolean, sendResponse: (response: any) => void) {
  try {
    await chrome.storage.sync.set({ cloudSyncEnabled: enabled });

    if (enabled) {
      // 启用时同步所有工作区
      for (const workspaceId of workspaces.keys()) {
        await syncWorkspaceToCloud(workspaceId);
      }
    }

    sendResponse({
      success: true,
      message: enabled ? '云同步已启用' : '云同步已禁用',
      enabled
    });
  } catch (error: any) {
    console.error('设置云同步状态失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取当前标签页（增强版本，支持状态验证和错误恢复）
async function handleGetCurrentTabs(sendResponse: (response: any) => void) {
  try {
    console.log('🔄 获取当前标签页...');

    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 获取到 ${tabs.length} 个标签页`);

    // 验证和增强标签页数据
    const enhancedTabs = await enhanceTabsData(tabs);

    // 验证标签页状态一致性
    const verifiedTabs = await verifyTabsConsistency(enhancedTabs);

    console.log(`✅ 标签页数据处理完成: ${verifiedTabs.length} 个有效标签页`);

    sendResponse({
      success: true,
      tabs: verifiedTabs,
      metadata: {
        totalTabs: tabs.length,
        validTabs: verifiedTabs.length,
        timestamp: Date.now(),
        windowId: tabs[0]?.windowId
      }
    });

  } catch (error: any) {
    console.error('❌ 获取标签页失败:', error);

    // 尝试恢复机制
    try {
      console.log('🔄 尝试恢复机制...');
      const fallbackTabs = await getFallbackTabs();
      sendResponse({
        success: true,
        tabs: fallbackTabs,
        warning: '使用恢复机制获取标签页',
        metadata: {
          totalTabs: fallbackTabs.length,
          validTabs: fallbackTabs.length,
          timestamp: Date.now(),
          fallback: true
        }
      });
    } catch (fallbackError: any) {
      console.error('❌ 恢复机制也失败:', fallbackError);
      sendResponse({
        success: false,
        error: error.message,
        fallbackError: fallbackError.message
      });
    }
  }
}

// 增强标签页数据
async function enhanceTabsData(tabs: chrome.tabs.Tab[]) {
  const enhancedTabs = [];

  for (const tab of tabs) {
    try {
      // 基本数据验证
      if (!tab.id || !tab.url) {
        console.warn('跳过无效标签页:', tab);
        continue;
      }

      // 增强标签页数据
      const enhancedTab = {
        ...tab,
        _enhanced: true,
        _timestamp: Date.now(),
        _hostname: extractHostname(tab.url),
        _isValid: true
      };

      enhancedTabs.push(enhancedTab);

    } catch (error) {
      console.warn(`处理标签页 ${tab.id} 失败:`, error);
      // 添加错误标记但保留标签页
      enhancedTabs.push({
        ...tab,
        _enhanced: false,
        _error: error.message,
        _timestamp: Date.now(),
        _isValid: false
      });
    }
  }

  return enhancedTabs;
}

// 验证标签页一致性
async function verifyTabsConsistency(tabs: any[]) {
  const verifiedTabs = [];
  let inconsistencyCount = 0;

  for (const tab of tabs) {
    try {
      if (!tab.id) {
        continue;
      }

      // 重新获取标签页状态以确保一致性
      const actualTab = await chrome.tabs.get(tab.id);

      // 检查关键状态是否一致
      const hasInconsistency =
        tab.pinned !== actualTab.pinned ||
        tab.title !== actualTab.title ||
        tab.url !== actualTab.url;

      if (hasInconsistency) {
        inconsistencyCount++;
        console.warn(`发现状态不一致的标签页 ${tab.id}:`, {
          cached: { pinned: tab.pinned, title: tab.title },
          actual: { pinned: actualTab.pinned, title: actualTab.title }
        });

        // 使用实际状态
        verifiedTabs.push({
          ...actualTab,
          _verified: true,
          _corrected: true,
          _originalState: {
            pinned: tab.pinned,
            title: tab.title,
            url: tab.url
          }
        });
      } else {
        verifiedTabs.push({
          ...tab,
          _verified: true,
          _corrected: false
        });
      }

    } catch (error) {
      console.warn(`验证标签页 ${tab.id} 失败:`, error);
      // 标签页可能已被关闭，跳过
      continue;
    }
  }

  if (inconsistencyCount > 0) {
    console.warn(`⚠️ 发现并修正了 ${inconsistencyCount} 个状态不一致的标签页`);
  }

  return verifiedTabs;
}

// 获取后备标签页数据
async function getFallbackTabs() {
  try {
    // 尝试从所有窗口获取标签页
    const allTabs = await chrome.tabs.query({});

    // 获取当前活动窗口
    const currentWindow = await chrome.windows.getCurrent();

    // 过滤当前窗口的标签页
    const currentWindowTabs = allTabs.filter(tab => tab.windowId === currentWindow.id);

    console.log(`🔄 后备机制获取到 ${currentWindowTabs.length} 个标签页`);
    return currentWindowTabs;

  } catch (error) {
    console.error('后备机制失败:', error);
    return [];
  }
}

// 提取主机名
function extractHostname(url: string): string {
  try {
    return new URL(url).hostname;
  } catch (error) {
    return url;
  }
}

// 获取当前工作区
async function handleGetCurrentWorkspace(sendResponse: (response: any) => void) {
  try {
    sendResponse({
      success: true,
      workspaceId: currentWorkspaceId
    });
  } catch (error: any) {
    console.error('获取当前工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取所有工作区
async function handleGetWorkspaces(sendResponse: (response: any) => void) {
  try {
    const workspacesArray = Array.from(workspaces.values());
    sendResponse({ success: true, workspaces: workspacesArray });
  } catch (error: any) {
    console.error('获取工作区列表失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取特定工作区
async function handleGetWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      sendResponse({ success: true, workspace });
    } else {
      sendResponse({ success: false, error: '工作区不存在' });
    }
  } catch (error: any) {
    console.error('获取工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 切换工作区
async function handleSwitchWorkspace(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    await switchToWorkspace(workspaceId);
    sendResponse({ success: true, workspaceId });
  } catch (error: any) {
    console.error('切换工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 创建新工作区
async function handleCreateWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    const newWorkspace: Workspace = {
      id: generateWorkspaceId(),
      name: data.name,
      icon: data.icon || '📁',
      color: data.color || '#3B82F6',
      groups: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    workspaces.set(newWorkspace.id, newWorkspace);
    await saveWorkspacesToStorage();

    sendResponse({ success: true, workspace: newWorkspace });
  } catch (error: any) {
    console.error('创建工作区失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI自动分组预览
async function handleAIAutoGroupPreview(workspaceId: string, sendResponse: (response: any) => void) {
  try {
    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });

    // 简单的AI分组逻辑（基于域名）
    const groups = await performSimpleAIGrouping(tabs);

    sendResponse({ success: true, groups });
  } catch (error: any) {
    console.error('AI自动分组预览失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI自动分组应用
async function handleAIAutoGroupApply(data: any, sendResponse: (response: any) => void) {
  try {
    const { workspaceId, groups } = data;

    // AI分组功能已改为基于固定状态
    await applyAIPinning(groups);

    sendResponse({ success: true, message: `已创建 ${groups.length} 个分组` });
  } catch (error: any) {
    console.error('AI自动分组应用失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 添加标签页到工作区
async function handleAddTabToWorkspace(data: any, sendResponse: (response: any) => void) {
  try {
    const { workspaceId, url } = data;

    // 创建新标签页
    const tab = await chrome.tabs.create({ url, active: false });

    sendResponse({ success: true, tab });
  } catch (error: any) {
    console.error('添加标签页失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// AI分析URL
async function handleAIAnalyzeURL(data: any, sendResponse: (response: any) => void) {
  try {
    const { url } = data;

    // 创建临时标签页来获取网站信息
    const tab = await chrome.tabs.create({ url, active: false });

    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 获取页面标题
    const updatedTab = await chrome.tabs.get(tab.id!);
    const title = updatedTab.title || new URL(url).hostname;

    // AI分析网站类型和建议分组
    const domain = new URL(url).hostname;
    const category = categorizeWebsite(domain);
    const suggestedGroup = {
      name: category,
      color: getCategoryColor(category)
    };

    // 关闭临时标签页
    await chrome.tabs.remove(tab.id!);

    sendResponse({
      success: true,
      title,
      suggestedGroup,
      category
    });
  } catch (error: any) {
    console.error('AI分析URL失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 核心工作区功能实现

// 切换到指定工作区（增强版本，支持错误恢复和状态验证）
async function switchToWorkspace(workspaceId: string) {
  const switchStartTime = Date.now();
  let rollbackData: any = null;

  try {
    console.log('🔄 开始工作区切换:', workspaceId);

    // 验证目标工作区是否存在
    const targetWorkspace = workspaces.get(workspaceId);
    if (!targetWorkspace) {
      throw new Error(`工作区 ${workspaceId} 不存在`);
    }

    // 准备回滚数据
    rollbackData = {
      previousWorkspaceId: currentWorkspaceId,
      timestamp: switchStartTime
    };

    console.log(`📋 从工作区 ${currentWorkspaceId} 切换到 ${workspaceId}`);

    // 阶段1: 保存当前会话状态（增强错误处理）
    console.log('📦 阶段1: 保存当前会话状态...');
    await executeWithRetry(() => saveCurrentSessionState(), 2, '保存会话状态');

    // 阶段2: 保存当前工作区的标签页状态
    console.log('💾 阶段2: 保存当前工作区状态...');
    await executeWithRetry(() => saveCurrentWorkspaceState(), 2, '保存工作区状态');

    // 阶段3: 智能挂起当前工作区的标签页
    console.log('😴 阶段3: 智能挂起当前标签页...');
    await executeWithRetry(() => suspendCurrentWorkspaceTabs(), 2, '挂起标签页');

    // 阶段4: 更新当前工作区ID
    console.log('🔄 阶段4: 更新工作区ID...');
    const previousWorkspaceId = currentWorkspaceId;
    currentWorkspaceId = workspaceId;
    await saveWorkspacesToStorage();

    // 阶段5: 统一标签页恢复（新架构 - 消除重复创建问题）
    console.log('🔄 阶段5: 统一标签页恢复...');
    const targetWorkspace = workspaces.get(workspaceId);
    if (targetWorkspace) {
      await executeWithRetry(() => unifiedTabRestoration(targetWorkspace), 2, '统一标签页恢复');
    }

    // 阶段7: 验证切换结果
    console.log('🔍 阶段7: 验证切换结果...');
    const verificationResult = await verifyWorkspaceSwitchResult(workspaceId);

    if (!verificationResult.success) {
      console.warn('⚠️ 工作区切换验证失败:', verificationResult.issues);
      // 尝试修复问题
      await fixWorkspaceSwitchIssues(verificationResult.issues);
    }

    // 阶段8: 同步到云端（如果启用，非阻塞）
    console.log('☁️ 阶段8: 同步到云端...');
    syncWorkspaceToCloud(workspaceId).catch(error => {
      console.warn('云同步失败（非致命错误）:', error);
    });

    const switchDuration = Date.now() - switchStartTime;
    console.log(`✅ 工作区切换完成: ${workspaceId} (耗时: ${switchDuration}ms)`);

    // 记录切换历史
    await recordWorkspaceSwitchHistory(previousWorkspaceId, workspaceId, switchDuration);

  } catch (error) {
    console.error('❌ 工作区切换失败:', error);

    // 尝试回滚
    if (rollbackData) {
      console.log('🔄 尝试回滚工作区切换...');
      await rollbackWorkspaceSwitch(rollbackData);
    }

    throw error;
  }
}

// 带重试的执行函数
async function executeWithRetry(operation: () => Promise<void>, maxRetries: number, operationName: string) {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      await operation();
      if (attempt > 1) {
        console.log(`✅ ${operationName} 重试成功 (第${attempt}次尝试)`);
      }
      return;
    } catch (error) {
      lastError = error as Error;
      console.warn(`⚠️ ${operationName} 失败 (第${attempt}次尝试):`, error);

      if (attempt <= maxRetries) {
        const delay = Math.min(1000 * attempt, 3000); // 递增延迟，最大3秒
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`${operationName} 在 ${maxRetries + 1} 次尝试后仍然失败: ${lastError?.message}`);
}

// 验证工作区切换结果
async function verifyWorkspaceSwitchResult(workspaceId: string) {
  try {
    const issues: string[] = [];

    // 验证当前工作区ID
    if (currentWorkspaceId !== workspaceId) {
      issues.push(`工作区ID不匹配: 期望 ${workspaceId}, 实际 ${currentWorkspaceId}`);
    }

    // 验证标签页状态
    const tabs = await chrome.tabs.query({ currentWindow: true });
    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);

    let pinningIssues = 0;
    for (const tab of tabs) {
      if (!tab.url) continue;

      const shouldBePinned = pinnedUrls.some(pattern => tab.url!.includes(pattern));
      const actuallyPinned = tab.pinned || false;

      if (shouldBePinned !== actuallyPinned) {
        pinningIssues++;
      }
    }

    if (pinningIssues > 0) {
      issues.push(`${pinningIssues} 个标签页的固定状态不正确`);
    }

    return {
      success: issues.length === 0,
      issues
    };

  } catch (error) {
    return {
      success: false,
      issues: [`验证过程失败: ${(error as Error).message}`]
    };
  }
}

// 修复工作区切换问题
async function fixWorkspaceSwitchIssues(issues: string[]) {
  console.log('🔧 尝试修复工作区切换问题:', issues);

  try {
    // 重新应用固定设置
    const workspace = workspaces.get(currentWorkspaceId);
    if (workspace) {
      await applyWorkspacePinning(workspace);
    }

    console.log('✅ 工作区切换问题修复完成');
  } catch (error) {
    console.error('❌ 修复工作区切换问题失败:', error);
  }
}

// 回滚工作区切换
async function rollbackWorkspaceSwitch(rollbackData: any) {
  try {
    console.log('🔄 回滚工作区切换到:', rollbackData.previousWorkspaceId);

    // 恢复工作区ID
    currentWorkspaceId = rollbackData.previousWorkspaceId;
    await saveWorkspacesToStorage();

    // 尝试恢复之前的会话
    await restoreWorkspaceSession(rollbackData.previousWorkspaceId);

    console.log('✅ 工作区切换回滚完成');
  } catch (rollbackError) {
    console.error('❌ 工作区切换回滚失败:', rollbackError);
  }
}

// 记录工作区切换历史
async function recordWorkspaceSwitchHistory(fromWorkspaceId: string, toWorkspaceId: string, duration: number) {
  try {
    const historyEntry = {
      from: fromWorkspaceId,
      to: toWorkspaceId,
      timestamp: Date.now(),
      duration,
      success: true
    };

    // 获取现有历史
    const result = await chrome.storage.local.get(['workspaceSwitchHistory']);
    const history = result.workspaceSwitchHistory || [];

    // 添加新记录
    history.push(historyEntry);

    // 保留最近100条记录
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    // 保存历史
    await chrome.storage.local.set({ workspaceSwitchHistory: history });

  } catch (error) {
    console.warn('记录工作区切换历史失败:', error);
  }
}

// 保存当前会话状态（增强版本，支持数据验证和压缩）
async function saveCurrentSessionState() {
  try {
    console.log('💾 开始保存当前会话状态...');

    const currentWindow = await chrome.windows.getCurrent({ populate: true });
    if (!currentWindow.tabs) {
      throw new Error('无法获取当前窗口的标签页');
    }

    console.log(`📋 当前窗口有 ${currentWindow.tabs.length} 个标签页`);

    // 验证和清理标签页数据
    const validTabs = currentWindow.tabs.filter(tab => tab.id && tab.url);
    console.log(`✅ 有效标签页: ${validTabs.length} 个`);

    // 创建会话数据
    const sessionData = {
      windowId: currentWindow.id,
      workspaceId: currentWorkspaceId,
      timestamp: Date.now(),
      version: '2.0', // 版本标识
      metadata: {
        totalTabs: currentWindow.tabs.length,
        validTabs: validTabs.length,
        pinnedTabs: validTabs.filter(tab => tab.pinned).length,
        activeTabs: validTabs.filter(tab => tab.active).length,
        discardedTabs: validTabs.filter(tab => tab.discarded).length
      },
      tabs: await Promise.all(validTabs.map(async tab => ({
        id: tab.id,
        url: tab.url,
        title: tab.title || '',
        favIconUrl: tab.favIconUrl,
        pinned: tab.pinned || false,
        active: tab.active || false,
        index: tab.index || 0,
        discarded: tab.discarded || false,
        groupId: tab.groupId,
        // 添加额外的状态信息
        lastAccessed: Date.now(),
        hostname: extractHostname(tab.url || ''),
        isImportant: await isImportantTab(tab)
      })))
    };

    // 压缩会话数据（如果标签页过多）
    const compressedSessionData = await compressSessionData(sessionData);

    // 保存到本地存储
    const storageKey = `session_${currentWorkspaceId}`;
    await chrome.storage.local.set({
      [storageKey]: compressedSessionData,
      [`${storageKey}_backup`]: sessionData, // 保留未压缩的备份
      lastSessionSave: Date.now(),
      sessionSaveCount: await getSessionSaveCount() + 1
    });

    console.log(`✅ 会话状态已保存: ${sessionData.tabs.length} 个标签页 (工作区: ${currentWorkspaceId})`);

    // 清理旧的会话数据
    await cleanupOldSessionData();

  } catch (error) {
    console.error('❌ 保存会话状态失败:', error);

    // 尝试简化保存
    await saveSimplifiedSessionState();
  }
}

// 判断是否为重要标签页
async function isImportantTab(tab: chrome.tabs.Tab): Promise<boolean> {
  try {
    if (!tab.url) return false;

    // 重要网站列表
    const importantDomains = [
      'gmail.com', 'docs.google.com', 'github.com', 'stackoverflow.com',
      'notion.so', 'figma.com', 'slack.com', 'discord.com',
      'yuque.com', 'feishu.cn'
    ];

    // 检查是否为重要域名
    const isImportantDomain = importantDomains.some(domain => tab.url!.includes(domain));

    // 检查是否包含表单或编辑内容
    const hasEditContent = tab.url.includes('edit') ||
                          tab.url.includes('compose') ||
                          tab.url.includes('new') ||
                          tab.url.includes('create');

    return isImportantDomain || hasEditContent || tab.pinned || false;

  } catch (error) {
    console.warn('判断标签页重要性失败:', error);
    return false;
  }
}

// 压缩会话数据
async function compressSessionData(sessionData: any) {
  try {
    // 如果标签页数量不多，不需要压缩
    if (sessionData.tabs.length <= 20) {
      return sessionData;
    }

    console.log('🗜️ 压缩会话数据...');

    // 保留重要标签页的完整信息，其他标签页只保留基本信息
    const compressedTabs = sessionData.tabs.map((tab: any) => {
      if (tab.isImportant || tab.pinned || tab.active) {
        return tab; // 保留完整信息
      } else {
        // 只保留基本信息
        return {
          id: tab.id,
          url: tab.url,
          title: tab.title,
          pinned: tab.pinned,
          active: tab.active,
          index: tab.index,
          _compressed: true
        };
      }
    });

    return {
      ...sessionData,
      tabs: compressedTabs,
      _compressed: true,
      _originalTabCount: sessionData.tabs.length
    };

  } catch (error) {
    console.warn('压缩会话数据失败:', error);
    return sessionData;
  }
}

// 获取会话保存次数
async function getSessionSaveCount(): Promise<number> {
  try {
    const result = await chrome.storage.local.get(['sessionSaveCount']);
    return result.sessionSaveCount || 0;
  } catch (error) {
    return 0;
  }
}

// 保存简化的会话状态（后备方案）
async function saveSimplifiedSessionState() {
  try {
    console.log('🔄 保存简化会话状态...');

    const tabs = await chrome.tabs.query({ currentWindow: true });
    const simplifiedData = {
      workspaceId: currentWorkspaceId,
      timestamp: Date.now(),
      simplified: true,
      tabUrls: tabs.filter(tab => tab.url && !tab.url.startsWith('chrome://')).map(tab => tab.url)
    };

    await chrome.storage.local.set({
      [`session_${currentWorkspaceId}_simple`]: simplifiedData
    });

    console.log(`✅ 简化会话状态已保存: ${simplifiedData.tabUrls.length} 个URL`);

  } catch (error) {
    console.error('❌ 保存简化会话状态也失败:', error);
  }
}

// 清理旧的会话数据
async function cleanupOldSessionData() {
  try {
    const result = await chrome.storage.local.get();
    const keysToDelete: string[] = [];
    const currentTime = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

    for (const [key, value] of Object.entries(result)) {
      if (key.startsWith('session_') && typeof value === 'object' && value !== null) {
        const sessionData = value as any;
        if (sessionData.timestamp && currentTime - sessionData.timestamp > maxAge) {
          keysToDelete.push(key);
        }
      }
    }

    if (keysToDelete.length > 0) {
      await chrome.storage.local.remove(keysToDelete);
      console.log(`🧹 清理了 ${keysToDelete.length} 个过期会话数据`);
    }

  } catch (error) {
    console.warn('清理旧会话数据失败:', error);
  }
}

// 智能挂起当前工作区的标签页（修复版本，保持固定状态）
async function suspendCurrentWorkspaceTabs() {
  try {
    console.log(`😴 [DEBUG-SUSPEND] ========== 开始智能挂起当前工作区标签页 ==========`);
    console.log(`😴 [DEBUG-SUSPEND] 当前工作区ID: ${currentWorkspaceId}`);

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [DEBUG-SUSPEND] 当前窗口共有 ${allTabs.length} 个标签页`);

    // 保存当前标签页的固定状态，以便后续恢复
    const tabPinnedStates = new Map<number, boolean>();

    // 记录挂起前的标签页状态
    console.log(`📋 [DEBUG-SUSPEND] ========== 挂起前标签页状态 ==========`);
    allTabs.forEach((tab, index) => {
      if (tab.id) {
        tabPinnedStates.set(tab.id, tab.pinned || false);
      }

      console.log(`📋 [DEBUG-SUSPEND] 标签页 ${index + 1}: ${tab.title}`);
      console.log(`📋 [DEBUG-SUSPEND]   - URL: ${tab.url}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 活跃状态: ${tab.active ? '活跃' : '非活跃'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 挂起状态: ${tab.discarded ? '已挂起' : '未挂起'}`);
      console.log(`📋 [DEBUG-SUSPEND]   - 标签页ID: ${tab.id}`);
    });

    // 保存固定状态到存储，以便工作区切换后恢复
    await chrome.storage.local.set({
      [`pinnedStates_${currentWorkspaceId}`]: Object.fromEntries(tabPinnedStates)
    });
    console.log(`💾 [DEBUG-SUSPEND] 已保存工作区 ${currentWorkspaceId} 的固定状态`);

    // 智能筛选需要挂起的标签页（只挂起非活跃的标签页）
    const suspensionCandidates = allTabs.filter(tab => {
      // 不挂起活跃标签页
      if (tab.active) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过活跃标签页: ${tab.title}`);
        return false;
      }

      // 不挂起已经挂起的标签页
      if (tab.discarded) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过已挂起标签页: ${tab.title}`);
        return false;
      }

      // 不挂起Chrome内部页面
      if (tab.url && (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://'))) {
        console.log(`⏭️ [DEBUG-SUSPEND] 跳过Chrome内部页面: ${tab.title}`);
        return false;
      }

      return true;
    });

    console.log(`🎯 [DEBUG-SUSPEND] 识别出 ${suspensionCandidates.length} 个挂起候选标签页`);

    if (suspensionCandidates.length === 0) {
      console.log('📝 [DEBUG-SUSPEND] 没有需要挂起的标签页');
      return;
    }

    // 执行批量挂起操作
    const suspensionResults = await performBatchSuspension(suspensionCandidates);

    // 验证挂起结果
    await verifySuspensionResults(suspensionResults);

    console.log(`✅ 智能挂起完成: ${suspensionResults.successful}/${suspensionCandidates.length} 个标签页已挂起`);

  } catch (error) {
    console.error('❌ 智能挂起标签页失败:', error);

    // 尝试简单挂起作为后备方案
    await performSimpleSuspension();
  }
}

// 识别挂起候选标签页
async function identifySuspensionCandidates(allTabs: chrome.tabs.Tab[]) {
  const candidates: chrome.tabs.Tab[] = [];
  const currentTime = Date.now();

  for (const tab of allTabs) {
    try {
      // 基本过滤条件
      if (!tab.id || tab.pinned || tab.active || tab.discarded) {
        continue;
      }

      // 跳过特殊页面
      if (!tab.url ||
          tab.url.startsWith('chrome://') ||
          tab.url.startsWith('chrome-extension://') ||
          tab.url === 'chrome://newtab/') {
        continue;
      }

      // 智能判断是否应该跳过挂起
      const shouldSkip = await shouldSkipTabSuspension(tab, currentTime, 5 * 60 * 1000);
      if (shouldSkip) {
        console.log(`⏭️ 跳过挂起重要标签页: ${tab.title}`);
        continue;
      }

      candidates.push(tab);

    } catch (error) {
      console.warn(`评估标签页 ${tab.id} 挂起条件失败:`, error);
    }
  }

  return candidates;
}

// 执行批量挂起操作
async function performBatchSuspension(tabs: chrome.tabs.Tab[]) {
  const results = {
    successful: 0,
    failed: 0,
    errors: [] as Array<{tabId: number, title: string, error: string}>
  };

  // 分批处理，避免同时挂起太多标签页
  const batchSize = 5;
  const batches = [];

  for (let i = 0; i < tabs.length; i += batchSize) {
    batches.push(tabs.slice(i, i + batchSize));
  }

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    console.log(`😴 处理第 ${batchIndex + 1}/${batches.length} 批标签页 (${batch.length} 个)`);

    // 并行处理当前批次
    const batchPromises = batch.map(async (tab) => {
      try {
        if (tab.id) {
          await chrome.tabs.discard(tab.id);
          results.successful++;
          console.log(`✅ 已挂起: ${tab.title}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          tabId: tab.id || 0,
          title: tab.title || '未知标题',
          error: (error as Error).message
        });
        console.warn(`❌ 挂起失败: ${tab.title}`, error);
      }
    });

    await Promise.all(batchPromises);

    // 批次间短暂延迟
    if (batchIndex < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  return results;
}

// 验证挂起结果
async function verifySuspensionResults(results: any) {
  try {
    if (results.failed > 0) {
      console.warn(`⚠️ ${results.failed} 个标签页挂起失败:`, results.errors);
    }

    // 等待一段时间后验证挂起状态
    await new Promise(resolve => setTimeout(resolve, 1000));

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    const discardedCount = allTabs.filter(tab => tab.discarded).length;
    const totalTabs = allTabs.length;

    console.log(`📊 挂起验证结果: ${discardedCount}/${totalTabs} 个标签页已挂起`);

    // 如果挂起比例过低，记录警告
    if (totalTabs > 5 && discardedCount / totalTabs < 0.2) {
      console.warn('⚠️ 挂起比例较低，可能存在问题');
    }

  } catch (error) {
    console.error('验证挂起结果失败:', error);
  }
}

// 简单挂起作为后备方案
async function performSimpleSuspension() {
  try {
    console.log('🔄 执行简单挂起后备方案...');

    const allTabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false,
      active: false,
      discarded: false
    });

    let suspendedCount = 0;
    for (const tab of allTabs) {
      try {
        if (tab.id && tab.url && !tab.url.startsWith('chrome://')) {
          await chrome.tabs.discard(tab.id);
          suspendedCount++;
        }
      } catch (error) {
        console.warn(`简单挂起失败: ${tab.title}`, error);
      }
    }

    console.log(`✅ 简单挂起完成: ${suspendedCount} 个标签页`);

  } catch (error) {
    console.error('❌ 简单挂起也失败:', error);
  }
}

// 恢复工作区会话
async function restoreWorkspaceSession(workspaceId: string) {
  try {
    console.log('🔄 恢复工作区会话:', workspaceId);

    // 尝试从本地存储恢复会话
    const sessionKey = `session_${workspaceId}`;
    const result = await chrome.storage.local.get([sessionKey]);
    const sessionData = result[sessionKey];

    if (sessionData && sessionData.tabs && sessionData.tabs.length > 0) {
      console.log(`📋 找到保存的会话: ${sessionData.tabs.length} 个标签页`);
      await restoreTabsFromSession(sessionData.tabs);
    } else {
      console.log('📋 没有找到保存的会话，从工作区配置恢复');
      await restoreWorkspaceFromConfig(workspaceId);
    }
  } catch (error) {
    console.error('❌ 恢复工作区会话失败:', error);
  }
}

// 从会话数据恢复标签页
async function restoreTabsFromSession(sessionTabs: any[]) {
  try {
    console.log('🔄 从会话数据恢复标签页...');

    // 获取当前标签页
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const currentUrls = new Set(currentTabs.map(tab => tab.url));

    let restoredCount = 0;
    for (const sessionTab of sessionTabs) {
      // 跳过已存在的标签页
      if (currentUrls.has(sessionTab.url)) {
        continue;
      }

      try {
        const newTab = await chrome.tabs.create({
          url: sessionTab.url,
          active: false,
          pinned: sessionTab.pinned || false,
          index: sessionTab.index
        });

        if (newTab.id) {
          restoredCount++;

          // 如果原来是挂起状态，立即挂起新标签页
          if (sessionTab.discarded) {
            setTimeout(async () => {
              try {
                await chrome.tabs.discard(newTab.id!);
              } catch (error) {
                console.warn('挂起恢复的标签页失败:', error);
              }
            }, 1000);
          }
        }
      } catch (error) {
        console.warn(`恢复标签页失败: ${sessionTab.url}`, error);
      }
    }

    console.log(`✅ 从会话恢复了 ${restoredCount} 个标签页`);
  } catch (error) {
    console.error('❌ 从会话恢复标签页失败:', error);
  }
}

// 从工作区配置恢复标签页
async function restoreWorkspaceFromConfig(workspaceId: string) {
  try {
    const workspace = workspaces.get(workspaceId);
    if (!workspace || !workspace.groups || workspace.groups.length === 0) {
      console.log('工作区没有配置的标签页');
      return;
    }

    console.log('🔄 从工作区配置恢复标签页...');

    // 收集所有标签页信息
    const allTabsToRestore: any[] = [];
    for (const group of workspace.groups) {
      if (group.tabs && group.tabs.length > 0) {
        allTabsToRestore.push(...group.tabs);
      }
    }

    // 获取工作区的固定URL模式
    const pinnedUrls = definePinnedUrlsForWorkspace(workspaceId);

    // 创建标签页
    let restoredCount = 0;
    for (const tabInfo of allTabsToRestore) {
      if (!tabInfo.url) continue;

      try {
        // 判断是否应该固定
        const shouldBePinned = pinnedUrls.some(pattern => tabInfo.url.includes(pattern));

        // 创建新标签页
        const newTab = await chrome.tabs.create({
          url: tabInfo.url,
          active: false,
          pinned: shouldBePinned
        });

        if (newTab.id) {
          restoredCount++;
          console.log(`恢复标签页: ${tabInfo.title} (${shouldBePinned ? '固定' : '普通'})`);
        }
      } catch (error) {
        console.error('创建标签页失败:', tabInfo.url, error);
      }
    }

    console.log(`✅ 从配置恢复了 ${restoredCount} 个标签页`);
  } catch (error) {
    console.error('❌ 从配置恢复标签页失败:', error);
  }
}

// 同步工作区到云端
async function syncWorkspaceToCloud(workspaceId: string) {
  try {
    // 检查是否启用云同步
    const settings = await chrome.storage.sync.get(['cloudSyncEnabled']);
    if (!settings.cloudSyncEnabled) {
      return;
    }

    console.log('☁️ 同步工作区到云端:', workspaceId);

    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      const syncData = {
        workspaceId,
        workspace,
        timestamp: Date.now(),
        deviceId: await getDeviceId()
      };

      await chrome.storage.sync.set({
        [`cloud_workspace_${workspaceId}`]: syncData
      });

      console.log('✅ 工作区已同步到云端');
    }
  } catch (error) {
    console.warn('⚠️ 云同步失败:', error);
  }
}

// 获取设备ID
async function getDeviceId(): Promise<string> {
  try {
    const result = await chrome.storage.local.get(['deviceId']);
    if (result.deviceId) {
      return result.deviceId;
    }

    const newDeviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    await chrome.storage.local.set({ deviceId: newDeviceId });
    return newDeviceId;
  } catch (error) {
    console.error('获取设备ID失败:', error);
    return 'unknown_device';
  }
}

// 保存当前工作区状态（修复版本，严格过滤临时标签页）
async function saveCurrentWorkspaceState() {
  try {
    console.log(`💾 [DEBUG-SAVE] ========== 开始保存当前工作区状态 ==========`);
    console.log(`💾 [DEBUG-SAVE] 当前工作区ID: ${currentWorkspaceId}`);

    // 如果没有当前工作区，跳过保存
    if (!currentWorkspaceId) {
      console.log(`⚠️ [DEBUG-SAVE] 没有当前工作区，跳过保存`);
      return;
    }

    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const workspace = workspaces.get(currentWorkspaceId);

    if (workspace) {
      console.log(`📋 [DEBUG-SAVE] 当前工作区: ${workspace.name}，有 ${currentTabs.length} 个标签页`);

      // 获取当前工作区的预定义固定URL模式
      const pinnedUrls = definePinnedUrlsForWorkspace(currentWorkspaceId);
      console.log(`📌 [DEBUG-SAVE] 预定义固定URL模式:`, pinnedUrls);

      // 如果没有预定义的固定URL模式，不保存任何标签页
      if (!pinnedUrls || pinnedUrls.length === 0) {
        console.log(`⚠️ [DEBUG-SAVE] 工作区没有预定义固定URL模式，不保存标签页状态`);
        return;
      }

      console.log(`🔍 [DEBUG-SAVE] ========== 分析当前标签页 ==========`);

      // 只保存真正符合预定义模式且当前为固定状态的标签页
      const validPinnedTabs = currentTabs.filter(tab => {
        if (!tab.url || !tab.pinned) {
          console.log(`⏭️ [DEBUG-SAVE] 跳过非固定标签页: ${tab.title} - 固定状态: ${tab.pinned}`);
          return false;
        }

        // 严格检查：必须同时满足预定义模式匹配AND当前为固定状态
        const matchesPredefinedPattern = pinnedUrls.some(pattern => {
          const matches = tab.url!.includes(pattern);
          console.log(`🔍 [DEBUG-SAVE] 检查模式 "${pattern}" 是否匹配 "${tab.url}": ${matches}`);
          return matches;
        });

        if (matchesPredefinedPattern && tab.pinned) {
          console.log(`✅ [DEBUG-SAVE] 保存有效的预定义固定标签页: ${tab.title} - ${tab.url}`);
          return true;
        } else {
          console.log(`⏭️ [DEBUG-SAVE] 跳过标签页: ${tab.title} - 匹配模式: ${matchesPredefinedPattern}, 固定状态: ${tab.pinned}`);
          return false;
        }
      });

      console.log(`📊 [DEBUG-SAVE] 严格过滤结果: 总共${currentTabs.length}个标签页，保存${validPinnedTabs.length}个有效固定标签页`);

      // 只有当有有效的固定标签页时才更新工作区配置
      if (validPinnedTabs.length > 0) {
        workspace.groups = await organizeTabsIntoGroups(validPinnedTabs);
        workspace.updatedAt = new Date().toISOString();
        workspaces.set(currentWorkspaceId, workspace);
        await saveWorkspacesToStorage();

        console.log(`✅ [DEBUG-SAVE] 工作区状态保存完成: 保存了 ${validPinnedTabs.length} 个有效固定标签页`);
        console.log(`✅ [DEBUG-SAVE] 工作区更新时间: ${workspace.updatedAt}`);
      } else {
        console.log(`⚠️ [DEBUG-SAVE] 没有有效的固定标签页，不更新工作区配置`);
      }
    } else {
      console.error(`❌ [DEBUG-SAVE] 工作区不存在: ${currentWorkspaceId}`);
    }
  } catch (error) {
    console.error('❌ [DEBUG-SAVE] 保存工作区状态失败:', error);
  }
}

// 应用工作区标签页可见性
async function applyWorkspaceTabVisibility(workspaceId: string) {
  try {
    console.log(`🔄 [DEBUG-VISIBILITY] ========== 开始应用工作区标签页可见性 ==========`);
    console.log(`🔄 [DEBUG-VISIBILITY] 目标工作区ID: ${workspaceId}`);

    const workspace = workspaces.get(workspaceId);
    if (!workspace) {
      console.warn(`❌ [DEBUG-VISIBILITY] 工作区不存在: ${workspaceId}`);
      return;
    }

    console.log(`🔄 [DEBUG-VISIBILITY] 工作区名称: ${workspace.name}`);

    // 实现真正的工作区标签页可见性控制
    // 1. 获取当前所有标签页
    console.log(`📋 [DEBUG-VISIBILITY] 步骤1: 获取当前所有标签页...`);
    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [DEBUG-VISIBILITY] 步骤1: 获取到 ${allTabs.length} 个当前标签页`);

    // 记录当前标签页状态
    allTabs.forEach((tab, index) => {
      console.log(`📋 [DEBUG-VISIBILITY] 当前标签页 ${index + 1}: ${tab.title}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - URL: ${tab.url}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
      console.log(`📋 [DEBUG-VISIBILITY]   - 挂起状态: ${tab.discarded ? '已挂起' : '未挂起'}`);
    });

    // 2. 保存当前标签页状态到全局存储
    console.log(`💾 [DEBUG-VISIBILITY] 步骤2: 保存当前标签页状态...`);
    await saveCurrentTabsState(allTabs);
    console.log(`💾 [DEBUG-VISIBILITY] 步骤2: 已保存当前标签页状态`);

    // 3. 暂停所有非固定标签页而非关闭
    console.log(`😴 [DEBUG-VISIBILITY] 步骤3: 暂停非固定标签页...`);
    await suspendAllNonPinnedTabs();
    console.log(`😴 [DEBUG-VISIBILITY] 步骤3: 已暂停非固定标签页`);

    // 4. 恢复工作区的标签页
    console.log(`🔄 [DEBUG-VISIBILITY] 步骤4: 恢复工作区标签页...`);
    await restoreWorkspaceTabs(workspace);
    console.log(`🔄 [DEBUG-VISIBILITY] 步骤4: 已恢复工作区标签页`);

    // 5. 等待标签页加载完成，然后应用工作区特定的固定设置
    console.log(`📌 [DEBUG-VISIBILITY] 步骤5: 等待标签页加载完成...`);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

    console.log(`📌 [DEBUG-VISIBILITY] 步骤5: 开始应用工作区固定设置...`);
    await applyWorkspacePinning(workspace);
    console.log(`✅ [DEBUG-VISIBILITY] 工作区切换完成: ${workspace.name}`);

  } catch (error) {
    console.error(`❌ [DEBUG-VISIBILITY] 应用工作区标签页可见性失败:`, error);
    console.error(`❌ [DEBUG-VISIBILITY] 错误堆栈:`, error.stack);
  }
}

// 增强的标签页状态保存
async function saveCurrentTabsState(tabs: chrome.tabs.Tab[]) {
  try {
    const enhancedTabsState = await Promise.all(tabs.map(async (tab) => {
      const basicState = {
        id: tab.id,
        url: tab.url,
        title: tab.title,
        favIconUrl: tab.favIconUrl,
        pinned: tab.pinned,
        index: tab.index,
        active: tab.active,
        discarded: tab.discarded,
        lastAccessed: Date.now() // 记录当前时间作为最后访问时间
      };

      // 尝试获取更详细的会话信息
      try {
        if (tab.id) {
          // 使用sessions API保存更详细的状态
          const sessionInfo = await chrome.sessions.getRecentlyClosed({ maxResults: 1 });
          return {
            ...basicState,
            sessionData: sessionInfo.length > 0 ? sessionInfo[0] : null
          };
        }
      } catch (sessionError) {
        console.warn('获取会话信息失败:', sessionError);
      }

      return basicState;
    }));

    // 保存到全局标签页状态
    await chrome.storage.local.set({
      globalTabsState: enhancedTabsState,
      lastSaveTime: Date.now(),
      workspaceId: currentWorkspaceId
    });

    console.log('已保存增强的标签页状态:', enhancedTabsState.length, '个标签页');
  } catch (error) {
    console.error('保存标签页状态失败:', error);
  }
}

// 关闭所有非固定标签页（保留用于兼容性）
async function closeAllNonPinnedTabs() {
  try {
    const allTabs = await chrome.tabs.query({ currentWindow: true, pinned: false });

    for (const tab of allTabs) {
      if (tab.id && tab.url !== 'chrome://newtab/') {
        await chrome.tabs.remove(tab.id);
      }
    }

    console.log('已关闭所有非固定标签页');
  } catch (error) {
    console.error('关闭标签页失败:', error);
  }
}

// 智能暂停非固定标签页（修复版本，解决标签页重复问题）
async function suspendAllNonPinnedTabs() {
  try {
    console.log('😴 开始智能暂停非固定标签页...');

    const allTabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false
    });

    console.log(`📋 找到 ${allTabs.length} 个非固定标签页`);

    const recentlyActiveThreshold = 5 * 60 * 1000; // 5分钟
    const currentTime = Date.now();

    let suspendedCount = 0;
    let skippedCount = 0;

    for (const tab of allTabs) {
      // 跳过新标签页和当前活动标签页
      if (!tab.id || tab.url === 'chrome://newtab/' || tab.active) {
        continue;
      }

      // 跳过Chrome内部页面
      if (tab.url && (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://'))) {
        continue;
      }

      // 智能暂停逻辑：跳过最近活跃的标签页
      const shouldSkipSuspension = await shouldSkipTabSuspension(tab, currentTime, recentlyActiveThreshold);

      if (shouldSkipSuspension) {
        console.log(`⏭️ 跳过暂停重要标签页: ${tab.title}`);
        skippedCount++;
        continue;
      }

      // 如果标签页还没有被暂停，则暂停它
      if (!tab.discarded) {
        try {
          await chrome.tabs.discard(tab.id);
          suspendedCount++;
          console.log(`✅ 标签页 ${tab.id} (${tab.title}) 已智能暂停`);
        } catch (error) {
          console.warn(`❌ 暂停标签页 ${tab.id} 失败:`, error);
          // 如果暂停失败，记录但不关闭标签页
        }
      } else {
        console.log(`⏭️ 标签页 ${tab.id} (${tab.title}) 已经被暂停`);
        skippedCount++;
      }
    }

    console.log(`✅ 智能标签页暂停完成: 暂停 ${suspendedCount} 个，跳过 ${skippedCount} 个`);
  } catch (error) {
    console.error('❌ 智能暂停标签页失败:', error);
  }
}

// 判断是否应该跳过标签页暂停
async function shouldSkipTabSuspension(tab: chrome.tabs.Tab, currentTime: number, threshold: number): Promise<boolean> {
  try {
    // 检查标签页是否包含表单数据（简单启发式）
    if (tab.url && (tab.url.includes('form') || tab.url.includes('edit') || tab.url.includes('compose'))) {
      return true;
    }

    // 检查是否是重要网站（可以根据需要扩展）
    const importantDomains = ['gmail.com', 'docs.google.com', 'github.com', 'stackoverflow.com'];
    if (tab.url && importantDomains.some(domain => tab.url!.includes(domain))) {
      return true;
    }

    // 其他情况允许暂停
    return false;
  } catch (error) {
    console.warn('检查标签页暂停条件失败:', error);
    return false; // 默认允许暂停
  }
}

// 保存所有标签页到存储
async function saveAllTabsToStorage() {
  try {
    const allTabs = await chrome.tabs.query({ currentWindow: true });
    const tabsData = allTabs.map(tab => ({
      id: tab.id,
      url: tab.url,
      title: tab.title,
      favIconUrl: tab.favIconUrl,
      pinned: tab.pinned,
      groupId: tab.groupId,
      index: tab.index
    }));

    await chrome.storage.local.set({
      [`allTabs_${Date.now()}`]: tabsData
    });

    console.log('已保存', tabsData.length, '个标签页到存储');
  } catch (error) {
    console.error('保存标签页失败:', error);
  }
}

// 隐藏不属于工作区的标签页
async function hideNonWorkspaceTabs(workspaceId: string) {
  try {
    const workspace = workspaces.get(workspaceId);
    if (!workspace || !workspace.groups) {
      return;
    }

    // 获取工作区中的所有URL
    const workspaceUrls = new Set<string>();
    workspace.groups.forEach(group => {
      group.tabs.forEach(tab => {
        if (tab.url) workspaceUrls.add(tab.url);
      });
    });

    // 获取当前所有标签页
    const allTabs = await chrome.tabs.query({ currentWindow: true });

    // 关闭不属于工作区的标签页（保留固定标签页和新标签页）
    for (const tab of allTabs) {
      if (tab.pinned) continue; // 保留固定标签页
      if (!tab.url || tab.url === 'chrome://newtab/') continue; // 保留新标签页

      if (!workspaceUrls.has(tab.url) && tab.id) {
        await chrome.tabs.remove(tab.id);
      }
    }

    console.log('已隐藏不属于工作区的标签页');
  } catch (error) {
    console.error('隐藏标签页失败:', error);
  }
}

// 应用工作区特定的固定设置（增强版本）
async function applyWorkspacePinning(workspace: Workspace) {
  try {
    console.log(`📌 [DEBUG-PIN] ========== 开始应用工作区固定设置 ==========`);
    console.log(`📌 [DEBUG-PIN] 工作区名称: ${workspace.name}`);
    console.log(`📌 [DEBUG-PIN] 工作区ID: ${workspace.id}`);

    const pinnedUrls = definePinnedUrlsForWorkspace(workspace.id);
    console.log(`📌 [DEBUG-PIN] 固定URL模式:`, pinnedUrls);

    const allTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📌 [DEBUG-PIN] 当前窗口有 ${allTabs.length} 个标签页`);

    // 收集需要固定和取消固定的标签页
    const tabsToPin: chrome.tabs.Tab[] = [];
    const tabsToUnpin: chrome.tabs.Tab[] = [];
    const pinningResults: Array<{tab: chrome.tabs.Tab, shouldBePinned: boolean, currentlyPinned: boolean}> = [];

    console.log(`📌 [DEBUG-PIN] ========== 分析每个标签页的固定状态 ==========`);
    for (const tab of allTabs) {
      if (!tab.id || !tab.url) {
        console.log(`⚠️ [DEBUG-PIN] 跳过无效标签页: ${tab.title} (ID: ${tab.id}, URL: ${tab.url})`);
        continue;
      }

      console.log(`📌 [DEBUG-PIN] 分析标签页: ${tab.title}`);
      console.log(`📌 [DEBUG-PIN]   - URL: ${tab.url}`);
      console.log(`📌 [DEBUG-PIN]   - 当前固定状态: ${tab.pinned ? '已固定' : '未固定'}`);

      const shouldBePinned = pinnedUrls.some(pattern => {
        const matches = tab.url!.includes(pattern);
        console.log(`📌 [DEBUG-PIN]   - 检查模式 "${pattern}": ${matches ? '匹配' : '不匹配'}`);
        return matches;
      });

      const currentlyPinned = tab.pinned || false;

      console.log(`📌 [DEBUG-PIN]   - 应该固定: ${shouldBePinned}`);
      console.log(`📌 [DEBUG-PIN]   - 当前固定: ${currentlyPinned}`);
      console.log(`📌 [DEBUG-PIN]   - 需要操作: ${shouldBePinned !== currentlyPinned ? '是' : '否'}`);

      pinningResults.push({
        tab,
        shouldBePinned,
        currentlyPinned
      });

      // 收集需要更改状态的标签页
      if (currentlyPinned !== shouldBePinned) {
        if (shouldBePinned) {
          tabsToPin.push(tab);
          console.log(`📌 [DEBUG-PIN] ✅ 需要固定: ${tab.title}`);
        } else {
          tabsToUnpin.push(tab);
          console.log(`📌 [DEBUG-PIN] ❌ 需要取消固定: ${tab.title}`);
        }
      } else {
        console.log(`📌 [DEBUG-PIN] ✅ 固定状态正确: ${tab.title} (${currentlyPinned ? '已固定' : '未固定'})`);
      }
    }

    // 批量执行固定操作
    const pinningOperations = await performBatchPinning(tabsToPin, tabsToUnpin);

    // 验证固定操作的完整性
    const verificationResult = await verifyPinningCompleteness(workspace.id, pinningResults);

    console.log(`✅ 工作区 ${workspace.id} 固定设置应用完成:`, {
      totalTabs: allTabs.length,
      pinned: pinningOperations.pinned,
      unpinned: pinningOperations.unpinned,
      failed: pinningOperations.failed,
      verificationPassed: verificationResult.passed
    });

    if (!verificationResult.passed) {
      console.warn('固定操作验证失败，尝试修复:', verificationResult.issues);
      await fixPinningIssues(verificationResult.issues);
    }

  } catch (error) {
    console.error('应用工作区固定设置失败:', error);
  }
}

// 批量执行固定操作
async function performBatchPinning(tabsToPin: chrome.tabs.Tab[], tabsToUnpin: chrome.tabs.Tab[]) {
  const results = {
    pinned: 0,
    unpinned: 0,
    failed: 0
  };

  // 批量固定标签页
  for (const tab of tabsToPin) {
    try {
      if (tab.id) {
        await chrome.tabs.update(tab.id, { pinned: true });
        results.pinned++;
        console.log(`✅ 标签页 ${tab.title} 已固定`);
      }
    } catch (error) {
      console.error(`固定标签页失败: ${tab.title}`, error);
      results.failed++;
    }
  }

  // 批量取消固定标签页
  for (const tab of tabsToUnpin) {
    try {
      if (tab.id) {
        await chrome.tabs.update(tab.id, { pinned: false });
        results.unpinned++;
        console.log(`✅ 标签页 ${tab.title} 已取消固定`);
      }
    } catch (error) {
      console.error(`取消固定标签页失败: ${tab.title}`, error);
      results.failed++;
    }
  }

  return results;
}

// 验证固定操作的完整性
async function verifyPinningCompleteness(workspaceId: string, expectedResults: Array<{tab: chrome.tabs.Tab, shouldBePinned: boolean, currentlyPinned: boolean}>) {
  try {
    // 等待一小段时间让固定操作生效
    await new Promise(resolve => setTimeout(resolve, 500));

    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const issues: Array<{tabId: number, title: string, expected: boolean, actual: boolean}> = [];

    for (const expected of expectedResults) {
      const currentTab = currentTabs.find(tab => tab.id === expected.tab.id);
      if (currentTab) {
        const actualPinned = currentTab.pinned || false;
        if (actualPinned !== expected.shouldBePinned) {
          issues.push({
            tabId: currentTab.id!,
            title: currentTab.title || '',
            expected: expected.shouldBePinned,
            actual: actualPinned
          });
        }
      }
    }

    return {
      passed: issues.length === 0,
      issues
    };
  } catch (error) {
    console.error('验证固定操作完整性失败:', error);
    return { passed: false, issues: [] };
  }
}

// 修复固定问题
async function fixPinningIssues(issues: Array<{tabId: number, title: string, expected: boolean, actual: boolean}>) {
  console.log(`尝试修复 ${issues.length} 个固定问题...`);

  for (const issue of issues) {
    try {
      await chrome.tabs.update(issue.tabId, { pinned: issue.expected });
      console.log(`🔧 修复标签页 ${issue.title}: ${issue.expected ? '固定' : '取消固定'}`);
    } catch (error) {
      console.error(`修复标签页固定状态失败: ${issue.title}`, error);
    }
  }
}

// 恢复工作区标签页（修复版本，解决重复创建问题）
async function restoreWorkspaceTabs(workspace: Workspace) {
  try {
    console.log(`🔄 [DEBUG-RESTORE] ========== 开始恢复工作区标签页 ==========`);
    console.log(`🔄 [DEBUG-RESTORE] 工作区名称: ${workspace.name}`);
    console.log(`🔄 [DEBUG-RESTORE] 工作区ID: ${workspace.id}`);

    // 获取工作区的预定义固定URL模式（这是真正应该创建的标签页）
    const pinnedUrls = definePinnedUrlsForWorkspace(workspace.id);
    console.log(`📌 [DEBUG-RESTORE] 预定义固定URL模式:`, pinnedUrls);

    if (!pinnedUrls || pinnedUrls.length === 0) {
      console.log('📝 [DEBUG-RESTORE] 工作区没有预定义的固定标签页');
      return;
    }

    // 获取当前窗口中的所有标签页，用于去重检查
    // 添加短暂延迟以确保之前创建的标签页已经完全加载
    await new Promise(resolve => setTimeout(resolve, 500));
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [DEBUG-RESTORE] 当前窗口有 ${currentTabs.length} 个标签页`);

    // 创建增强的URL去重机制
    const existingUrls = new Set<string>();
    const existingTabsMap = new Map<string, chrome.tabs.Tab>();

    console.log(`🔍 [DEBUG-RESTORE] ========== 分析现有标签页 ==========`);
    for (const tab of currentTabs) {
      if (tab.url) {
        // 标准化URL匹配
        const normalizedUrl = normalizeUrl(tab.url);
        existingUrls.add(normalizedUrl);
        existingTabsMap.set(normalizedUrl, tab);

        console.log(`📋 [DEBUG-RESTORE] 现有标签页: ${tab.title}`);
        console.log(`📋 [DEBUG-RESTORE]   - 原始URL: ${tab.url}`);
        console.log(`📋 [DEBUG-RESTORE]   - 标准化URL: ${normalizedUrl}`);
        console.log(`📋 [DEBUG-RESTORE]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
        console.log(`📋 [DEBUG-RESTORE]   - 标签页ID: ${tab.id}`);
      }
    }

    // 基于预定义模式创建标签页，而不是依赖可能过时的工作区配置
    console.log(`🔄 [DEBUG-RESTORE] ========== 基于预定义模式创建标签页 ==========`);

    let restoredCount = 0;
    let skippedCount = 0;
    let updatedCount = 0;

    // 尝试恢复之前保存的固定状态
    // 注意：这里应该恢复的是当前工作区的固定状态，而不是目标工作区的
    // 因为我们要恢复的是之前在这个工作区中的标签页状态
    const savedPinnedStates = await chrome.storage.local.get([`pinnedStates_${workspace.id}`]);
    const pinnedStates = savedPinnedStates[`pinnedStates_${workspace.id}`] || {};
    console.log(`📌 [DEBUG-RESTORE] 恢复保存的固定状态 (工作区: ${workspace.id}):`, pinnedStates);

    // 为每个预定义的固定URL模式创建标签页
    for (const pattern of pinnedUrls) {
      console.log(`🔍 [DEBUG-RESTORE] 处理URL模式: ${pattern}`);

      // 构建完整的URL（根据模式推断）
      const fullUrl = pattern.startsWith('http') ? pattern : `https://${pattern}`;
      const normalizedUrl = normalizeUrl(fullUrl);

      console.log(`🔍 [DEBUG-RESTORE] 完整URL: ${fullUrl}`);
      console.log(`🔍 [DEBUG-RESTORE] 标准化URL: ${normalizedUrl}`);

      // 检查是否已存在匹配的标签页（增强匹配逻辑）
      let existingTab = null;
      for (const [existingNormalizedUrl, tab] of existingTabsMap) {
        // 使用更严格的匹配逻辑：URL包含模式 或 模式包含在URL中
        const urlMatchesPattern = tab.url!.includes(pattern);
        const normalizedUrlMatchesPattern = existingNormalizedUrl.includes(pattern);

        if (urlMatchesPattern || normalizedUrlMatchesPattern) {
          existingTab = tab;
          console.log(`📋 [DEBUG-RESTORE] 找到匹配的现有标签页: ${tab.title} - ${tab.url}`);
          console.log(`📋 [DEBUG-RESTORE]   - URL匹配模式: ${urlMatchesPattern}`);
          console.log(`📋 [DEBUG-RESTORE]   - 标准化URL匹配模式: ${normalizedUrlMatchesPattern}`);
          break;
        }
      }

      if (existingTab) {
        console.log(`⏭️ [DEBUG-RESTORE] 跳过重复标签页: ${existingTab.title} - ${existingTab.url}`);
        skippedCount++;

        // 恢复固定状态（优先使用保存的状态，否则使用预定义规则）
        const shouldBePinned = existingTab.id && pinnedStates[existingTab.id] !== undefined
          ? pinnedStates[existingTab.id]
          : true; // 预定义的固定标签页默认应该固定

        if (existingTab.pinned !== shouldBePinned && existingTab.id) {
          await chrome.tabs.update(existingTab.id, { pinned: shouldBePinned });
          console.log(`📌 [DEBUG-RESTORE] 恢复标签页固定状态: ${existingTab.title} (${shouldBePinned ? '固定' : '取消固定'})`);
          updatedCount++;
        }
      } else {
        // 创建新的固定标签页
        console.log(`✅ [DEBUG-RESTORE] 创建新的固定标签页: ${fullUrl}`);
        try {
          const newTab = await chrome.tabs.create({
            url: fullUrl,
            active: false,
            pinned: true
          });

          if (newTab.id) {
            restoredCount++;
            console.log(`✅ [DEBUG-RESTORE] 创建标签页成功: ${newTab.title} (固定)`);

            // 添加到现有标签页映射中，避免后续重复
            const newNormalizedUrl = normalizeUrl(fullUrl);
            existingTabsMap.set(newNormalizedUrl, newTab);
          }
        } catch (error) {
          console.error(`❌ [DEBUG-RESTORE] 创建标签页失败: ${fullUrl}`, error);
        }
      }
    }

    console.log(`📊 [DEBUG-RESTORE] ========== 恢复工作区标签页完成 ==========`);
    console.log(`📊 [DEBUG-RESTORE] 创建新标签页: ${restoredCount} 个`);
    console.log(`📊 [DEBUG-RESTORE] 跳过重复标签页: ${skippedCount} 个`);
    console.log(`📊 [DEBUG-RESTORE] 更新固定状态: ${updatedCount} 个`);
    console.log(`📊 [DEBUG-RESTORE] 总计处理: ${restoredCount + skippedCount} 个标签页`);

  } catch (error) {
    console.error('❌ 恢复工作区标签页失败:', error);
  }

}

// 应用工作区标签页分组（已合并到restoreWorkspaceTabs中）
// 此函数已废弃，功能合并到applyWorkspaceTabVisibility中

// 为工作区组织当前标签页
async function organizeCurrentTabsForWorkspace(workspaceId: string) {
  try {
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    const groups = await performSimpleAIGrouping(currentTabs);

    // AI分组功能已改为基于固定状态
    await applyAIPinning(groups);

    // 保存到工作区
    const workspace = workspaces.get(workspaceId);
    if (workspace) {
      workspace.groups = await organizeTabsIntoGroups(currentTabs);
      workspaces.set(workspaceId, workspace);
      await saveWorkspacesToStorage();
    }
  } catch (error) {
    console.error('组织工作区标签页失败:', error);
  }
}

// 将标签页组织成分组结构
async function organizeTabsIntoGroups(tabs: chrome.tabs.Tab[]): Promise<TabGroup[]> {
  const groupMap = new Map<number, TabGroup>();

  for (const tab of tabs) {
    const groupId = tab.groupId || -1;

    if (!groupMap.has(groupId)) {
      let groupName = '未分组';
      let groupColor = 'grey';

      if (groupId !== -1) {
        try {
          const group = await chrome.tabGroups.get(groupId);
          groupName = group.title || `分组 ${groupId}`;
          groupColor = group.color;
        } catch (error) {
          console.warn('获取分组信息失败:', groupId);
        }
      }

      groupMap.set(groupId, {
        id: groupId.toString(),
        name: groupName,
        color: groupColor,
        tabs: []
      });
    }

    const group = groupMap.get(groupId)!;
    group.tabs.push({
      id: tab.id || 0,
      title: tab.title || '',
      url: tab.url || '',
      favIconUrl: tab.favIconUrl,
      groupId: tab.groupId
    });
  }

  return Array.from(groupMap.values());
}

// 生成工作区ID
function generateWorkspaceId(): string {
  return 'workspace_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// URL标准化函数，用于去重检查（修复版本，避免过度标准化）
function normalizeUrl(url: string): string {
  try {
    console.log(`🔍 [DEBUG-URL] 开始标准化URL: ${url}`);

    const urlObj = new URL(url);
    console.log(`🔍 [DEBUG-URL] 解析后的URL对象:`, {
      protocol: urlObj.protocol,
      hostname: urlObj.hostname,
      pathname: urlObj.pathname,
      search: urlObj.search,
      hash: urlObj.hash
    });

    // 移除fragment（#后面的部分）
    const originalHash = urlObj.hash;
    urlObj.hash = '';
    if (originalHash) {
      console.log(`🔍 [DEBUG-URL] 移除hash: ${originalHash}`);
    }

    // 只移除明确的跟踪参数，保留其他查询参数
    const trackingParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'fbclid', 'gclid'];
    const removedParams: string[] = [];
    trackingParams.forEach(param => {
      if (urlObj.searchParams.has(param)) {
        removedParams.push(`${param}=${urlObj.searchParams.get(param)}`);
        urlObj.searchParams.delete(param);
      }
    });

    if (removedParams.length > 0) {
      console.log(`🔍 [DEBUG-URL] 移除跟踪参数: ${removedParams.join(', ')}`);
    }

    // 保持原始路径，不强制添加/
    const normalizedUrl = urlObj.toString();
    console.log(`✅ [DEBUG-URL] 标准化完成: ${url} → ${normalizedUrl}`);
    console.log(`🔍 [DEBUG-URL] URL变化: ${url === normalizedUrl ? '无变化' : '已变化'}`);

    return normalizedUrl;
  } catch (error) {
    // 如果URL解析失败，返回原始URL
    console.warn('❌ [DEBUG-URL] URL标准化失败:', url, error);
    return url;
  }
}

// 定义每个工作区的固定URL模式
function definePinnedUrlsForWorkspace(workspaceId: string): string[] {
  const pinnedUrlPatterns: { [key: string]: string[] } = {
    'ai-main': [
      'chat.openai.com',
      'gemini.google.com',
      'lobehub.com',
      'perplexity.ai',
      'grok.x.ai',
      'aistudio.google.com'
    ],
    'ai-secondary': [
      'deepask.cc',
      'fun4ai.khthink.cn',
      'clivia.fun',
      'aabao.eu.cc',
      'haomo.de',
      'fuclaude.com'
    ],
    'ai-tools': [
      'dify.ai',
      'promptpilot.volcengine.com'
    ],
    'tech-forum': [
      'linux.do',
      'nodeloc.cc',
      'nodeseek.com',
      'appinn.net',
      'follow.is'
    ],
    'daily-work': [
      'yuque.com',
      'feishu.cn'
    ]
  };

  return pinnedUrlPatterns[workspaceId] || [];
}

// 简单的AI分组逻辑
async function performSimpleAIGrouping(tabs: chrome.tabs.Tab[]) {
  const groups = new Map<string, { name: string; color: string; tabs: chrome.tabs.Tab[] }>();

  tabs.forEach(tab => {
    if (!tab.url) return;

    try {
      const domain = new URL(tab.url).hostname;
      const category = categorizeWebsite(domain);

      if (!groups.has(category)) {
        groups.set(category, {
          name: category,
          color: getCategoryColor(category),
          tabs: []
        });
      }

      groups.get(category)!.tabs.push(tab);
    } catch (error) {
      console.error('分析标签页失败:', tab.url, error);
    }
  });

  return Array.from(groups.values()).filter(group => group.tabs.length > 1);
}

// 网站分类逻辑
function categorizeWebsite(domain: string): string {
  const categories: Record<string, string[]> = {
    '开发工具': ['github.com', 'stackoverflow.com', 'developer.mozilla.org', 'codepen.io'],
    'AI工具': ['openai.com', 'claude.ai', 'gemini.google.com', 'perplexity.ai'],
    '社交媒体': ['twitter.com', 'facebook.com', 'linkedin.com', 'instagram.com'],
    '视频平台': ['youtube.com', 'bilibili.com', 'netflix.com', 'twitch.tv'],
    '购物网站': ['amazon.com', 'taobao.com', 'jd.com', 'ebay.com'],
    '新闻资讯': ['news.google.com', 'bbc.com', 'cnn.com', 'reuters.com']
  };

  for (const [category, domains] of Object.entries(categories)) {
    if (domains.some((d: string) => domain.includes(d))) {
      return category;
    }
  }

  return '其他';
}

// 获取分类颜色
function getCategoryColor(category: string): chrome.tabGroups.ColorEnum {
  const colors: Record<string, chrome.tabGroups.ColorEnum> = {
    '开发工具': 'blue',
    'AI工具': 'purple',
    '社交媒体': 'pink',
    '视频平台': 'red',
    '购物网站': 'orange',
    '新闻资讯': 'green',
    '其他': 'grey'
  };

  return colors[category] || 'grey';
}

// AI智能固定功能（替代分组）
async function applyAIPinning(groups: { name: string; color: string; tabs: chrome.tabs.Tab[] }[]) {
  try {
    // 定义重要类别，这些类别的标签页将被固定
    const importantCategories = ['开发工具', 'AI工具', '生产力工具'];

    for (const group of groups) {
      const shouldPin = importantCategories.includes(group.name);

      for (const tab of group.tabs) {
        if (tab.id && tab.pinned !== shouldPin) {
          try {
            await chrome.tabs.update(tab.id, { pinned: shouldPin });
            console.log(`${shouldPin ? '固定' : '取消固定'}标签页: ${tab.title} (${group.name})`);
          } catch (error) {
            console.error(`更新标签页固定状态失败: ${tab.title}`, error);
          }
        }
      }
    }
  } catch (error) {
    console.error('应用AI智能固定失败:', error);
    throw error;
  }
}

// 内存管理和性能优化
async function optimizeMemoryUsage() {
  try {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    const discardedCount = tabs.filter(tab => tab.discarded).length;
    const totalTabs = tabs.length;

    console.log(`内存优化状态: ${discardedCount}/${totalTabs} 标签页已暂停`);

    // 如果暂停的标签页比例过低，进行额外的内存优化
    if (totalTabs > 10 && discardedCount / totalTabs < 0.3) {
      await performAdditionalMemoryOptimization();
    }
  } catch (error) {
    console.error('内存优化检查失败:', error);
  }
}

// 执行额外的内存优化
async function performAdditionalMemoryOptimization() {
  try {
    const tabs = await chrome.tabs.query({
      currentWindow: true,
      pinned: false,
      active: false,
      discarded: false
    });

    // 按最后访问时间排序，优先暂停最久未访问的标签页
    const sortedTabs = tabs.sort((a, b) => {
      // 简单的启发式：根据index判断访问顺序
      return (b.index || 0) - (a.index || 0);
    });

    // 暂停最久未访问的标签页（最多暂停一半）
    const tabsToSuspend = sortedTabs.slice(0, Math.floor(sortedTabs.length / 2));

    for (const tab of tabsToSuspend) {
      if (tab.id) {
        try {
          await chrome.tabs.discard(tab.id);
          console.log(`额外内存优化: 暂停标签页 ${tab.title}`);
        } catch (error) {
          console.warn(`额外暂停失败: ${tab.title}`, error);
        }
      }
    }
  } catch (error) {
    console.error('执行额外内存优化失败:', error);
  }
}

// 定期执行内存优化（每5分钟）
setInterval(optimizeMemoryUsage, 5 * 60 * 1000);

// ========== 新架构：统一标签页恢复系统 ==========

/**
 * 统一标签页恢复函数（重构版本）
 * 解决重复创建、时序问题、去重失效等核心问题
 */
async function unifiedTabRestoration(workspace: Workspace) {
  try {
    console.log(`🔄 [UNIFIED-RESTORE] ========== 开始统一标签页恢复 ==========`);
    console.log(`🔄 [UNIFIED-RESTORE] 工作区: ${workspace.name} (ID: ${workspace.id})`);

    // 步骤1: 获取预定义的固定URL模式
    const pinnedUrls = definePinnedUrlsForWorkspace(workspace.id);
    console.log(`📌 [UNIFIED-RESTORE] 预定义固定URL模式:`, pinnedUrls);

    if (!pinnedUrls || pinnedUrls.length === 0) {
      console.log('📝 [UNIFIED-RESTORE] 工作区没有预定义的固定标签页，跳过恢复');
      return;
    }

    // 步骤2: 获取当前所有标签页（实时获取，避免时序问题）
    const currentTabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`📋 [UNIFIED-RESTORE] 当前窗口有 ${currentTabs.length} 个标签页`);

    // 步骤3: 构建智能去重映射表
    const tabRegistry = buildTabRegistry(currentTabs);
    console.log(`🗂️ [UNIFIED-RESTORE] 标签页注册表构建完成，包含 ${tabRegistry.size} 个条目`);

    // 步骤4: 恢复固定状态（如果有保存的状态）
    const savedPinnedStates = await chrome.storage.local.get([`pinnedStates_${workspace.id}`]);
    const pinnedStates = savedPinnedStates[`pinnedStates_${workspace.id}`] || {};
    console.log(`💾 [UNIFIED-RESTORE] 恢复保存的固定状态:`, pinnedStates);

    // 步骤5: 处理每个预定义URL模式
    let createdCount = 0;
    let skippedCount = 0;
    let updatedCount = 0;

    for (const pattern of pinnedUrls) {
      const result = await processUrlPattern(pattern, tabRegistry, pinnedStates);
      createdCount += result.created ? 1 : 0;
      skippedCount += result.skipped ? 1 : 0;
      updatedCount += result.updated ? 1 : 0;
    }

    console.log(`✅ [UNIFIED-RESTORE] 统一标签页恢复完成:`);
    console.log(`📊 [UNIFIED-RESTORE] 创建: ${createdCount}, 跳过: ${skippedCount}, 更新: ${updatedCount}`);

  } catch (error) {
    console.error('❌ [UNIFIED-RESTORE] 统一标签页恢复失败:', error);
    throw error;
  }
}

/**
 * 构建标签页注册表（智能去重映射）
 */
function buildTabRegistry(tabs: chrome.tabs.Tab[]): Map<string, chrome.tabs.Tab> {
  const registry = new Map<string, chrome.tabs.Tab>();

  console.log(`🗂️ [TAB-REGISTRY] ========== 构建标签页注册表 ==========`);

  for (const tab of tabs) {
    if (!tab.url || !tab.id) continue;

    // 多重索引策略：原始URL、标准化URL、域名
    const originalUrl = tab.url;
    const normalizedUrl = normalizeUrl(tab.url);
    const domain = extractDomain(tab.url);

    // 注册多个索引
    registry.set(originalUrl, tab);
    registry.set(normalizedUrl, tab);
    registry.set(domain, tab);

    console.log(`📝 [TAB-REGISTRY] 注册标签页: ${tab.title}`);
    console.log(`📝 [TAB-REGISTRY]   - 原始URL: ${originalUrl}`);
    console.log(`📝 [TAB-REGISTRY]   - 标准化URL: ${normalizedUrl}`);
    console.log(`📝 [TAB-REGISTRY]   - 域名: ${domain}`);
    console.log(`📝 [TAB-REGISTRY]   - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
  }

  return registry;
}

/**
 * 提取域名
 */
function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return url;
  }
}

/**
 * 处理单个URL模式
 */
async function processUrlPattern(
  pattern: string,
  tabRegistry: Map<string, chrome.tabs.Tab>,
  pinnedStates: Record<string, boolean>
): Promise<{created: boolean, skipped: boolean, updated: boolean}> {

  console.log(`🔍 [PATTERN-PROCESS] ========== 处理URL模式: ${pattern} ==========`);

  // 构建完整URL
  const fullUrl = pattern.startsWith('http') ? pattern : `https://${pattern}`;
  const normalizedUrl = normalizeUrl(fullUrl);
  const domain = extractDomain(fullUrl);

  console.log(`🔍 [PATTERN-PROCESS] 完整URL: ${fullUrl}`);
  console.log(`🔍 [PATTERN-PROCESS] 标准化URL: ${normalizedUrl}`);
  console.log(`🔍 [PATTERN-PROCESS] 域名: ${domain}`);

  // 智能匹配现有标签页
  const existingTab = findMatchingTab(pattern, fullUrl, normalizedUrl, domain, tabRegistry);

  if (existingTab) {
    console.log(`⏭️ [PATTERN-PROCESS] 找到匹配的现有标签页: ${existingTab.title}`);

    // 恢复或应用固定状态
    const shouldBePinned = existingTab.id && pinnedStates[existingTab.id] !== undefined
      ? pinnedStates[existingTab.id]
      : true; // 预定义的固定标签页默认应该固定

    if (existingTab.pinned !== shouldBePinned && existingTab.id) {
      await chrome.tabs.update(existingTab.id, { pinned: shouldBePinned });
      console.log(`📌 [PATTERN-PROCESS] 更新固定状态: ${existingTab.title} (${shouldBePinned ? '固定' : '取消固定'})`);
      return { created: false, skipped: true, updated: true };
    }

    return { created: false, skipped: true, updated: false };
  } else {
    // 创建新标签页
    console.log(`✅ [PATTERN-PROCESS] 创建新的固定标签页: ${fullUrl}`);
    try {
      const newTab = await chrome.tabs.create({
        url: fullUrl,
        active: false,
        pinned: true
      });

      if (newTab.id) {
        console.log(`✅ [PATTERN-PROCESS] 标签页创建成功: ${newTab.title || 'Loading...'}`);

        // 将新标签页添加到注册表，避免后续重复创建
        const newNormalizedUrl = normalizeUrl(fullUrl);
        const newDomain = extractDomain(fullUrl);
        tabRegistry.set(fullUrl, newTab);
        tabRegistry.set(newNormalizedUrl, newTab);
        tabRegistry.set(newDomain, newTab);

        return { created: true, skipped: false, updated: false };
      }
    } catch (error) {
      console.error(`❌ [PATTERN-PROCESS] 创建标签页失败: ${fullUrl}`, error);
    }
  }

  return { created: false, skipped: false, updated: false };
}

/**
 * 智能匹配现有标签页
 */
function findMatchingTab(
  pattern: string,
  fullUrl: string,
  normalizedUrl: string,
  domain: string,
  tabRegistry: Map<string, chrome.tabs.Tab>
): chrome.tabs.Tab | null {

  console.log(`🔍 [TAB-MATCH] 开始智能匹配，模式: ${pattern}`);

  // 匹配策略1: 精确URL匹配
  if (tabRegistry.has(fullUrl)) {
    const tab = tabRegistry.get(fullUrl)!;
    console.log(`✅ [TAB-MATCH] 精确URL匹配: ${tab.title}`);
    return tab;
  }

  // 匹配策略2: 标准化URL匹配
  if (tabRegistry.has(normalizedUrl)) {
    const tab = tabRegistry.get(normalizedUrl)!;
    console.log(`✅ [TAB-MATCH] 标准化URL匹配: ${tab.title}`);
    return tab;
  }

  // 匹配策略3: 域名匹配
  if (tabRegistry.has(domain)) {
    const tab = tabRegistry.get(domain)!;
    console.log(`✅ [TAB-MATCH] 域名匹配: ${tab.title}`);
    return tab;
  }

  // 匹配策略4: 模式包含匹配
  for (const [key, tab] of tabRegistry) {
    if (key.includes(pattern) || pattern.includes(key)) {
      console.log(`✅ [TAB-MATCH] 模式包含匹配: ${tab.title} (键: ${key})`);
      return tab;
    }
  }

  console.log(`❌ [TAB-MATCH] 未找到匹配的标签页`);
  return null;
}

console.log('工作区管理器后台脚本加载完成');