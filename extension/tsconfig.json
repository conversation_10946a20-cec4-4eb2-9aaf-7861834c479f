{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["chrome"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}