// Chrome扩展工作区管理器 - 快速调试测试脚本
// 在浏览器控制台中运行此脚本来快速检查问题

console.log('🔧 开始快速调试测试...');

// 测试1: 检查当前工作区状态
async function testCurrentWorkspaceState() {
  console.log('\n📋 测试1: 检查当前工作区状态');
  
  try {
    const result = await chrome.storage.local.get(['currentWorkspaceId', 'workspaces']);
    console.log('当前工作区ID:', result.currentWorkspaceId);
    console.log('工作区总数:', result.workspaces ? Object.keys(result.workspaces).length : 0);
    
    if (result.workspaces) {
      Object.entries(result.workspaces).forEach(([id, workspace]) => {
        console.log(`工作区: ${workspace.name} (ID: ${id})`);
        console.log(`  - 分组数量: ${workspace.groups ? workspace.groups.length : 0}`);
        console.log(`  - 是否默认: ${workspace.isDefault || false}`);
      });
    }
  } catch (error) {
    console.error('❌ 获取工作区状态失败:', error);
  }
}

// 测试2: 检查当前标签页状态
async function testCurrentTabsState() {
  console.log('\n📋 测试2: 检查当前标签页状态');
  
  try {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    console.log(`当前窗口有 ${tabs.length} 个标签页:`);
    
    tabs.forEach((tab, index) => {
      console.log(`标签页 ${index + 1}: ${tab.title}`);
      console.log(`  - URL: ${tab.url}`);
      console.log(`  - 固定状态: ${tab.pinned ? '已固定' : '未固定'}`);
      console.log(`  - 活跃状态: ${tab.active ? '活跃' : '非活跃'}`);
      console.log(`  - 挂起状态: ${tab.discarded ? '已挂起' : '未挂起'}`);
      console.log(`  - 标签页ID: ${tab.id}`);
    });
  } catch (error) {
    console.error('❌ 获取标签页状态失败:', error);
  }
}

// 测试3: 模拟工作区切换
async function testWorkspaceSwitch(workspaceId) {
  console.log(`\n🔄 测试3: 模拟切换到工作区 ${workspaceId}`);
  
  try {
    // 发送切换工作区消息
    const response = await chrome.runtime.sendMessage({
      action: 'SWITCH_WORKSPACE',
      workspaceId: workspaceId
    });
    
    console.log('切换响应:', response);
    
    // 等待一段时间后检查结果
    setTimeout(async () => {
      await testCurrentTabsState();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 工作区切换失败:', error);
  }
}

// 测试4: 检查URL标准化
function testUrlNormalization() {
  console.log('\n🔍 测试4: 检查URL标准化');
  
  const testUrls = [
    'https://chat.openai.com/',
    'https://chat.openai.com/?utm_source=test',
    'https://chat.openai.com/#section',
    'https://gemini.google.com/app',
    'https://gemini.google.com/app?utm_campaign=test'
  ];
  
  testUrls.forEach(url => {
    try {
      const urlObj = new URL(url);
      urlObj.hash = '';
      ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term', 'fbclid', 'gclid'].forEach(param => {
        urlObj.searchParams.delete(param);
      });
      const normalized = urlObj.toString();
      
      console.log(`原始URL: ${url}`);
      console.log(`标准化: ${normalized}`);
      console.log(`是否变化: ${url !== normalized ? '是' : '否'}`);
      console.log('---');
    } catch (error) {
      console.error(`URL标准化失败: ${url}`, error);
    }
  });
}

// 测试5: 检查固定URL模式匹配
function testPinnedUrlPatterns() {
  console.log('\n📌 测试5: 检查固定URL模式匹配');
  
  const patterns = {
    'ai-main': [
      'chat.openai.com',
      'gemini.google.com',
      'lobehub.com',
      'perplexity.ai',
      'grok.x.ai',
      'aistudio.google.com'
    ],
    'ai-secondary': [
      'deepask.cc',
      'fun4ai.khthink.cn',
      'clivia.fun',
      'aabao.eu.cc',
      'haomo.de'
    ]
  };
  
  const testUrls = [
    'https://chat.openai.com/',
    'https://gemini.google.com/app',
    'https://deepask.cc/',
    'https://google.com/',
    'https://github.com/'
  ];
  
  Object.entries(patterns).forEach(([workspaceId, urlPatterns]) => {
    console.log(`工作区 ${workspaceId} 的固定模式:`);
    
    testUrls.forEach(testUrl => {
      const shouldBePinned = urlPatterns.some(pattern => testUrl.includes(pattern));
      console.log(`  ${testUrl} -> ${shouldBePinned ? '应该固定' : '不应该固定'}`);
    });
    console.log('---');
  });
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行所有快速测试...\n');
  
  await testCurrentWorkspaceState();
  await testCurrentTabsState();
  testUrlNormalization();
  testPinnedUrlPatterns();
  
  console.log('\n✅ 所有快速测试完成！');
  console.log('\n📋 可用的单独测试函数:');
  console.log('- testCurrentWorkspaceState()');
  console.log('- testCurrentTabsState()');
  console.log('- testWorkspaceSwitch("ai-main")');
  console.log('- testUrlNormalization()');
  console.log('- testPinnedUrlPatterns()');
}

// 自动运行测试
runAllTests();

// 导出函数供手动调用
window.debugTests = {
  testCurrentWorkspaceState,
  testCurrentTabsState,
  testWorkspaceSwitch,
  testUrlNormalization,
  testPinnedUrlPatterns,
  runAllTests
};

console.log('\n💡 提示: 测试函数已添加到 window.debugTests 对象中');
console.log('例如: debugTests.testWorkspaceSwitch("ai-main")');
