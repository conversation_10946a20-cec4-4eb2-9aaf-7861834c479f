#!/usr/bin/env node

/**
 * Chrome扩展构建验证脚本
 * 验证dist目录中的所有必要文件是否存在且格式正确
 */

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, 'dist');

// 必需的文件列表
const requiredFiles = [
    'manifest.json',
    'background.js',
    'sidepanel.html',
    'workspace-manager.js',
    'content.js',
    'options.html',
    'options.js',
    'vendors.js'
];

// 必需的目录
const requiredDirs = [
    'icons'
];

// 图标文件
const requiredIcons = [
    'icons/icon16.png',
    'icons/icon32.png',
    'icons/icon48.png',
    'icons/icon128.png'
];

console.log('🔍 开始验证Chrome扩展构建...\n');

let hasErrors = false;

// 检查dist目录是否存在
if (!fs.existsSync(distDir)) {
    console.error('❌ dist目录不存在！请先运行 npm run build');
    process.exit(1);
}

console.log('✅ dist目录存在');

// 检查必需文件
console.log('\n📁 检查必需文件:');
for (const file of requiredFiles) {
    const filePath = path.join(distDir, file);
    if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ ${file} (${Math.round(stats.size / 1024)}KB)`);
    } else {
        console.error(`❌ 缺少文件: ${file}`);
        hasErrors = true;
    }
}

// 检查必需目录
console.log('\n📂 检查必需目录:');
for (const dir of requiredDirs) {
    const dirPath = path.join(distDir, dir);
    if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
        console.log(`✅ ${dir}/`);
    } else {
        console.error(`❌ 缺少目录: ${dir}/`);
        hasErrors = true;
    }
}

// 检查图标文件
console.log('\n🎨 检查图标文件:');
for (const icon of requiredIcons) {
    const iconPath = path.join(distDir, icon);
    if (fs.existsSync(iconPath)) {
        const stats = fs.statSync(iconPath);
        console.log(`✅ ${icon} (${stats.size}B)`);
    } else {
        console.error(`❌ 缺少图标: ${icon}`);
        hasErrors = true;
    }
}

// 验证manifest.json
console.log('\n📋 验证manifest.json:');
try {
    const manifestPath = path.join(distDir, 'manifest.json');
    const manifestContent = fs.readFileSync(manifestPath, 'utf8');
    const manifest = JSON.parse(manifestContent);
    
    // 检查关键字段
    const requiredFields = ['manifest_version', 'name', 'version', 'permissions', 'background'];
    for (const field of requiredFields) {
        if (manifest[field]) {
            console.log(`✅ ${field}: ${typeof manifest[field] === 'object' ? 'OK' : manifest[field]}`);
        } else {
            console.error(`❌ manifest.json缺少字段: ${field}`);
            hasErrors = true;
        }
    }
    
    // 检查manifest版本
    if (manifest.manifest_version === 3) {
        console.log('✅ 使用Manifest V3');
    } else {
        console.error('❌ 不是Manifest V3');
        hasErrors = true;
    }
    
} catch (error) {
    console.error('❌ manifest.json格式错误:', error.message);
    hasErrors = true;
}

// 检查文件大小
console.log('\n📊 文件大小统计:');
const totalSize = requiredFiles.reduce((total, file) => {
    const filePath = path.join(distDir, file);
    if (fs.existsSync(filePath)) {
        return total + fs.statSync(filePath).size;
    }
    return total;
}, 0);

console.log(`总大小: ${Math.round(totalSize / 1024)}KB`);

if (totalSize > 5 * 1024 * 1024) { // 5MB
    console.warn('⚠️  扩展包较大，可能影响加载速度');
}

// 最终结果
console.log('\n' + '='.repeat(50));
if (hasErrors) {
    console.error('❌ 构建验证失败！请修复上述问题后重新构建。');
    process.exit(1);
} else {
    console.log('🎉 构建验证成功！');
    console.log('📦 扩展已准备就绪，可以加载到Chrome中。');
    console.log('\n📖 安装步骤:');
    console.log('1. 打开 chrome://extensions/');
    console.log('2. 开启"开发者模式"');
    console.log('3. 点击"加载已解压的扩展程序"');
    console.log('4. 选择 dist/ 文件夹');
    console.log('\n🚀 享受使用您的工作区管理器！');
}
