# Chrome扩展工作区管理器

一个专业的Chrome扩展，用于管理AI工具、技术论坛和工作工具的标签页，支持工作区切换、智能固定和标签页挂起功能。

## 🚀 快速开始

### 构建扩展
```bash
npm run build
```

### 安装到Chrome
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist/` 文件夹

## 🎯 主要功能

### ✅ 已修复的问题
- **标签页重复bug修复**: 工作区切换时不再创建重复标签页
- **智能去重机制**: 自动检测并跳过已存在的URL
- **优化挂起逻辑**: 避免挂起即将恢复的标签页

### 🎨 Material Design 3.0 UI
- **现代化设计**: 采用Material Design 3.0设计系统
- **中文优化**: 针对320-400px宽度优化中文显示
- **响应式布局**: 适配侧边栏界面
- **交互动画**: 流畅的悬停和点击效果

### 🔧 核心功能
- 🏢 **工作区管理**: 创建、切换、删除工作区
- 📌 **智能固定**: 自动固定常用标签页
- 😴 **标签页挂起**: 使用chrome.tabs.discard()节省内存
- 💾 **状态保存**: 自动保存工作区状态
- ☁️ **云同步**: 支持跨设备同步（可选）

## 🛠️ 开发环境

### VS Code调试配置
项目已配置完整的VS Code调试环境：

#### 可用的调试配置
- **调试Chrome扩展 - Background Script**: 调试后台服务脚本
- **调试Chrome扩展 - Sidepanel**: 调试侧边栏界面
- **启动Chrome并加载扩展**: 在真实环境中测试扩展
- **调试TypeScript源码**: 监听源码变化并自动构建
- **完整调试环境**: 同时启动源码监听和Chrome调试

#### 快捷键
- `F5`: 启动调试
- `Ctrl+Shift+P` → `Tasks: Run Task` → 选择任务

### 可用的任务
- `build-extension`: 构建扩展
- `dev-watch`: 开发模式监听文件变化
- `clean-build`: 清理并重新构建
- `clean-dist`: 清理构建目录

## 📦 项目结构

```
extension/
├── .vscode/                 # VS Code配置
│   ├── launch.json         # 调试配置
│   ├── tasks.json          # 任务配置
│   ├── settings.json       # 工作区设置
│   └── extensions.json     # 推荐扩展
├── src/                    # 源代码
│   ├── background/         # 后台脚本
│   ├── content/           # 内容脚本
│   ├── options/           # 选项页面
│   ├── icons/             # 图标资源
│   └── sidepanel.html     # 侧边栏界面
├── dist/                  # 构建输出
├── manifest.json          # 扩展清单
├── package.json           # 项目配置
├── webpack.config.js      # 构建配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 本文档
```

## 🎮 使用方法

### 快捷键
- `Ctrl+Shift+W` (Mac: `Cmd+Shift+W`) - 切换工作区
- `Ctrl+Shift+Space` (Mac: `Cmd+Shift+Space`) - 打开工作区管理器

### 基本操作
1. **创建工作区**: 点击"+"按钮创建新工作区
2. **切换工作区**: 点击工作区名称进行切换
3. **管理标签页**: 在工作区中添加、移除标签页
4. **固定设置**: 配置每个工作区的固定标签页

## 🔧 技术栈

- **前端**: React 18 + TypeScript
- **构建**: Webpack 5
- **设计**: Material Design 3.0
- **Chrome APIs**: tabs, storage, sessions, sidePanel
- **开发工具**: VS Code + ESLint + Prettier

## 🐛 故障排除

### 常见问题
1. **扩展无法加载**
   - 确保选择的是 `dist/` 文件夹
   - 检查manifest.json是否存在

2. **功能异常**
   - 打开Chrome开发者工具查看控制台错误
   - 检查扩展权限是否正确授予

3. **界面显示问题**
   - 刷新扩展页面
   - 重新加载扩展

### 调试模式
- 访问 `chrome://extensions/`
- 找到工作区管理器扩展
- 点击"详细信息"查看错误日志

## 📝 开发信息

- **版本**: 2.0.0
- **兼容性**: Chrome 88+
- **许可证**: MIT

## 🎉 享受使用！

您的Chrome扩展工作区管理器现在已经准备就绪。开始创建您的第一个工作区，体验高效的标签页管理吧！
