# Chrome扩展工作区管理器 - 详细调试测试指南

## 🔧 调试环境准备

### 1. 重新加载扩展
1. 访问 `chrome://extensions/`
2. 找到"AI工作区管理器"扩展
3. 点击刷新按钮重新加载（应用最新的调试日志）

### 2. 打开开发者工具
1. 按 `F12` 或右键 → 检查
2. 切换到 **Console** 标签页
3. 清空控制台：`console.clear()`
4. 保持开发者工具打开状态进行所有测试

### 3. 打开扩展侧边栏
- 点击扩展图标或使用快捷键打开侧边栏

---

## 🐛 问题1：固定标签页重复创建问题

### 测试步骤：
1. **准备阶段**
   ```
   - 关闭所有标签页，只保留一个空白页
   - 在控制台中输入：console.clear() 清空日志
   ```

2. **执行测试**
   ```
   - 切换到"AI工作主力"工作区
   - 观察创建的标签页数量
   - 再次切换到"AI工作主力"工作区
   - 检查是否出现重复标签页
   ```

### 关键调试日志：
```
🔄 [DEBUG-RESTORE] ========== 开始恢复工作区标签页 ==========
🔄 [DEBUG-RESTORE] 工作区名称: AI工作主力
📋 [DEBUG-RESTORE] 总共需要恢复 X 个标签页
🔍 [DEBUG-RESTORE] ========== 分析现有标签页 ==========
📋 [DEBUG-RESTORE] 现有标签页: [标签页名称]
📋 [DEBUG-RESTORE]   - 原始URL: [URL]
📋 [DEBUG-RESTORE]   - 标准化URL: [标准化后的URL]
🔍 [DEBUG-URL] 开始标准化URL: [URL]
✅ [DEBUG-URL] 标准化完成: [原始URL] → [标准化URL]
🔍 [DEBUG-RESTORE] ========== 检查标签页 X ==========
🔍 [DEBUG-RESTORE] 步骤1: 精确URL匹配检查
⏭️ [DEBUG-RESTORE] 跳过重复标签页(精确匹配): [标签页名称]
🔍 [DEBUG-RESTORE] 步骤2: 标准化URL匹配检查
⏭️ [DEBUG-RESTORE] 跳过重复标签页(标准化匹配): [标签页名称]
✅ [DEBUG-RESTORE] 创建标签页成功: [标签页名称]
```

### 预期行为：
- 每个URL只应该有一个标签页实例
- 控制台应显示跳过重复标签页的日志
- 多次切换不应创建重复标签页

### 问题现象：
- 同一URL出现多个标签页
- 控制台没有显示跳过重复的日志
- URL标准化逻辑异常

---

## 🐛 问题2：标签页错误固定问题

### 测试步骤：
1. **准备阶段**
   ```
   - 切换到"AI工作主力"工作区
   - 手动打开2-3个临时标签页（如：google.com, github.com）
   - 确认这些标签页没有被固定
   ```

2. **执行测试**
   ```
   - 切换到"AI次选"工作区
   - 立即切换回"AI工作主力"工作区
   - 检查之前手动打开的临时标签页状态
   ```

### 关键调试日志：
```
💾 [DEBUG-SAVE] ========== 开始保存当前工作区状态 ==========
💾 [DEBUG-SAVE] 当前工作区ID: ai-main
📋 [DEBUG-SAVE] ========== 分析当前标签页 ==========
📋 [DEBUG-SAVE] 标签页 X: [标签页名称]
📋 [DEBUG-SAVE]   - URL: [URL]
📋 [DEBUG-SAVE]   - 固定状态: 已固定/未固定
🔍 [DEBUG-SAVE] 检查模式 "[模式]" 是否匹配 "[URL]": true/false
✅ [DEBUG-SAVE] 保存预定义固定标签页: [标签页名称]
⏭️ [DEBUG-SAVE] 跳过用户临时标签页: [标签页名称]
📊 [DEBUG-SAVE] 过滤结果: 总共X个标签页，保存X个，跳过X个
```

### 预期行为：
- 临时标签页不应该被自动固定
- 控制台应显示跳过临时标签页的日志
- 工作区配置中不应包含临时标签页

### 问题现象：
- 临时标签页被错误固定
- 控制台显示保存了不应该保存的标签页
- 工作区配置被污染

---

## 🐛 问题3：删除功能失效问题

### 测试步骤：
1. **创建测试工作区**
   ```
   - 在工作区管理界面创建一个新的测试工作区
   - 添加一些标签页到测试工作区
   ```

2. **执行删除测试**
   ```
   - 点击测试工作区的"查看"按钮
   - 在详情页面点击"删除"按钮
   - 确认删除操作
   - 检查工作区是否被成功删除
   ```

### 关键调试日志：
```
🗑️ [DEBUG-UI-DELETE] ========== 开始删除工作区 ==========
🗑️ [DEBUG-UI-DELETE] 工作区ID: [工作区ID]
📤 [DEBUG-UI-DELETE] 发送删除请求到后台...
📥 [DEBUG-UI-DELETE] 收到后台响应: {success: true/false}
🗑️ [DEBUG-DELETE] ========== 开始删除工作区 ==========
🔍 [DEBUG-DELETE] 检查工作区是否存在...
🔍 [DEBUG-DELETE] 当前工作区总数: X
📋 [DEBUG-DELETE] 找到工作区: [工作区名称]
🗑️ [DEBUG-DELETE] 执行删除操作...
🗑️ [DEBUG-DELETE] 删除操作结果: true/false
✅ [DEBUG-DELETE] 工作区删除成功
```

### 预期行为：
- 删除确认对话框正常显示
- 控制台显示完整的删除过程日志
- 工作区从列表中消失
- 显示删除成功通知

### 问题现象：
- 点击删除按钮无响应
- 控制台没有删除相关日志
- 工作区没有被删除
- 出现错误提示

---

## 🐛 问题4：固定标签页状态丢失问题

### 测试步骤：
1. **准备阶段**
   ```
   - 切换到"AI工作主力"工作区
   - 确认所有预定义标签页都已正确固定
   - 记录固定标签页的数量和位置
   ```

2. **执行测试**
   ```
   - 切换到"AI次选"工作区
   - 观察"AI工作主力"的固定标签页是否被正确挂起
   - 切换回"AI工作主力"工作区
   - 检查固定标签页是否恢复固定状态
   ```

### 关键调试日志：
```
😴 [DEBUG-SUSPEND] ========== 开始智能挂起当前工作区标签页 ==========
📋 [DEBUG-SUSPEND] ========== 挂起前标签页状态 ==========
📋 [DEBUG-SUSPEND] 标签页 X: [标签页名称]
📋 [DEBUG-SUSPEND]   - 固定状态: 已固定/未固定
📋 [DEBUG-SUSPEND]   - 挂起状态: 已挂起/未挂起
📌 [DEBUG-PIN] ========== 开始应用工作区固定设置 ==========
📌 [DEBUG-PIN] ========== 分析每个标签页的固定状态 ==========
📌 [DEBUG-PIN] 分析标签页: [标签页名称]
📌 [DEBUG-PIN]   - 应该固定: true/false
📌 [DEBUG-PIN]   - 当前固定: true/false
📌 [DEBUG-PIN]   - 需要操作: 是/否
📌 [DEBUG-PIN] ✅ 需要固定: [标签页名称]
📌 [DEBUG-PIN] ❌ 需要取消固定: [标签页名称]
```

### 预期行为：
- 切换工作区时，固定标签页应该被挂起（discarded）而不是取消固定
- 切换回原工作区时，固定标签页应该恢复并保持固定状态
- 标签页的固定状态应该与工作区配置一致

### 问题现象：
- 固定标签页在工作区切换后失去固定状态
- 标签页被关闭而不是挂起
- 恢复工作区时固定状态不正确

---

## 📋 测试完成后请提供：

1. **每个问题的具体现象描述**
2. **相关的控制台日志截图或完整文本**
3. **标签页状态截图（显示固定状态）**
4. **工作区配置信息（如果相关）**
5. **重现步骤的详细记录**

## 🔧 有用的控制台命令：

```javascript
// 查看当前工作区ID
chrome.storage.local.get(['currentWorkspaceId'])

// 查看所有工作区配置
chrome.storage.local.get(['workspaces'])

// 查看当前标签页状态
chrome.tabs.query({currentWindow: true})

// 清空控制台
console.clear()
```

有了这些详细的调试日志，我们就能精确定位问题所在并提供针对性的修复方案！
