# Chrome扩展工作区管理器 - 安装说明

## 🚀 构建完成

您的Chrome扩展工作区管理器已成功构建！构建产物位于 `dist/` 目录中。

## 📦 构建内容

构建生成的文件包括：
- `manifest.json` - 扩展清单文件
- `background.js` - 后台服务脚本
- `sidepanel.html` - 侧边栏界面
- `workspace-manager.js` - 工作区管理脚本
- `options.html` / `options.js` - 选项页面
- `content.js` - 内容脚本
- `icons/` - 扩展图标
- `vendors.js` - 第三方库

## 🔧 安装步骤

### 1. 开启开发者模式
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 在右上角开启"开发者模式"

### 2. 加载扩展
1. 点击"加载已解压的扩展程序"
2. 选择项目的 `dist/` 文件夹
3. 点击"选择文件夹"

### 3. 验证安装
- 扩展图标应该出现在Chrome工具栏中
- 点击扩展图标可以打开侧边栏
- 检查是否有任何错误信息

## ✨ 主要功能

### 🎯 已修复的问题
- ✅ **标签页重复bug修复**: 工作区切换时不再创建重复标签页
- ✅ **智能去重机制**: 自动检测并跳过已存在的URL
- ✅ **优化挂起逻辑**: 避免挂起即将恢复的标签页

### 🎨 Material Design 3.0 UI
- ✅ **现代化设计**: 采用Material Design 3.0设计系统
- ✅ **中文优化**: 针对320-400px宽度优化中文显示
- ✅ **响应式布局**: 适配侧边栏界面
- ✅ **交互动画**: 流畅的悬停和点击效果

### 🔧 核心功能
- 🏢 **工作区管理**: 创建、切换、删除工作区
- 📌 **智能固定**: 自动固定常用标签页
- 😴 **标签页挂起**: 使用chrome.tabs.discard()节省内存
- 💾 **状态保存**: 自动保存工作区状态
- ☁️ **云同步**: 支持跨设备同步（可选）

## 🎮 使用方法

### 快捷键
- `Ctrl+Shift+W` (Mac: `Cmd+Shift+W`) - 切换工作区
- `Ctrl+Shift+Space` (Mac: `Cmd+Shift+Space`) - 打开工作区管理器

### 基本操作
1. **创建工作区**: 点击"+"按钮创建新工作区
2. **切换工作区**: 点击工作区名称进行切换
3. **管理标签页**: 在工作区中添加、移除标签页
4. **固定设置**: 配置每个工作区的固定标签页

## 🐛 故障排除

### 常见问题
1. **扩展无法加载**
   - 确保选择的是 `dist/` 文件夹
   - 检查manifest.json是否存在

2. **功能异常**
   - 打开Chrome开发者工具查看控制台错误
   - 检查扩展权限是否正确授予

3. **界面显示问题**
   - 刷新扩展页面
   - 重新加载扩展

### 调试模式
- 访问 `chrome://extensions/`
- 找到工作区管理器扩展
- 点击"详细信息"查看错误日志

## 📝 开发信息

- **版本**: 2.0.0
- **构建工具**: Webpack 5
- **技术栈**: TypeScript + React + Material Design 3.0
- **兼容性**: Chrome 88+

## 🎉 享受使用！

您的Chrome扩展工作区管理器现在已经准备就绪。开始创建您的第一个工作区，体验高效的标签页管理吧！
