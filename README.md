# AI工作台 Chrome插件

一个类似Workona的智能标签页管理和AI工具工作空间Chrome插件，帮助用户高效管理标签页和AI工具。

## 🚀 功能特性

### 核心功能
- **🗂️ 空间管理**: 按项目整理工作，专注更轻松
- **📑 标签页分组**: 智能分类，场景切换更高效  
- **⭐ 收藏标签页**: 保存重要页面，一键访问
- **🔍 强力搜索**: 快速定位标签页和文档
- **☁️ 多端同步**: 跨设备同步，工作始终井井有条

### 预设分组
- **🚀 AI工作主力**: ChatGPT、Gemini、LobeHub、Perplexity、Grok、AI Studio
- **🔧 AI次选**: DeepAsk、GPTFun、C佬、A佬、H佬、Claude
- **🛠️ AI其他工具**: Dify、提示词优化
- **💬 技术论坛**: Linux.do、NodeLoc、NodeSeek、小众软件、Follow
- **📝 日常工作**: 语雀、飞书

## 🏗️ 技术架构

### 前端 (Chrome Extension)
- **框架**: React 18 + TypeScript
- **构建工具**: Webpack 5
- **API**: Chrome Extension Manifest V3
- **样式**: CSS Modules + 响应式设计

### 后端 (Node.js API)
- **框架**: Express.js
- **数据库**: MySQL 8.0 + mysql2
- **认证**: JWT Token
- **安全**: Helmet + CORS + Rate Limiting

### 数据库设计
- 用户认证和会话管理
- 工作空间和标签页分组
- 收藏和同步数据管理

## 📦 项目结构

```
ai-workspace-chrome-extension/
├── extension/          # Chrome扩展前端
│   ├── src/           # React源码
│   ├── public/        # 静态资源
│   └── dist/          # 构建输出
├── server/            # Node.js后端
│   ├── controllers/   # 控制器
│   ├── models/        # 数据模型
│   ├── routes/        # 路由定义
│   └── middleware/    # 中间件
├── database/          # 数据库脚本
│   ├── schema.sql     # 表结构
│   └── seeds.sql      # 初始数据
└── docs/              # 项目文档
```

## 🛠️ 开发环境设置

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Chrome浏览器

### 安装依赖
```bash
# 安装所有依赖
npm run install:all

# 或分别安装
npm install
cd extension && npm install
cd ../server && npm install
```

### 开发模式
```bash
# 同时启动前端和后端开发服务器
npm run dev

# 或分别启动
npm run dev:extension  # Chrome扩展开发模式
npm run dev:server     # 后端API开发模式
```

### 构建生产版本
```bash
npm run build
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 分别测试
npm run test:extension
npm run test:server
```

## 📋 开发任务列表

- [x] 项目基础架构搭建
- [ ] MySQL数据库设计与实现
- [ ] Node.js后端API基础框架
- [ ] 用户认证系统实现
- [ ] Chrome Extension基础框架
- [ ] 标签页管理核心功能
- [ ] 预设AI工具分组配置
- [ ] 工作空间管理API
- [ ] 标签页收藏和搜索功能
- [ ] 数据同步系统实现
- [ ] 用户界面优化和交互体验
- [ ] 测试和部署配置

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢 Workona 提供的设计灵感
- 感谢 Chrome Extension 社区的技术支持
- 感谢所有贡献者的努力