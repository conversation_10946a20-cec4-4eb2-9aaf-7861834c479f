const Workspace = require('../models/Workspace');
const TabGroup = require('../models/TabGroup');
const SavedTab = require('../models/SavedTab');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// 获取用户的所有工作空间
const getWorkspaces = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    logger.info('Getting workspaces for user', { userId });
    
    const workspaces = await Workspace.findByUserId(userId);
    
    // 为每个工作空间获取统计信息
    const workspacesWithStats = await Promise.all(
      workspaces.map(async (workspace) => {
        const stats = await workspace.getStats();
        return {
          ...workspace.toJSON(),
          stats
        };
      })
    );
    
    res.json({
      status: 'success',
      data: {
        workspaces: workspacesWithStats
      }
    });
  } catch (error) {
    logger.error('Failed to get workspaces', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

// 获取单个工作空间详情
const getWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.id;
    
    logger.info('Getting workspace details', { userId, workspaceId });
    
    const workspace = await Workspace.findById(workspaceId);
    
    if (!workspace) {
      return next(new AppError('Workspace not found', 404));
    }
    
    // 检查权限
    if (workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    // 获取分组和标签页
    const groups = await TabGroup.findByWorkspaceId(workspaceId);
    const groupsWithTabs = await Promise.all(
      groups.map(async (group) => {
        const tabs = await group.getTabs();
        const stats = await group.getStats();
        return {
          ...group.toJSON(),
          tabs: tabs.map(tab => new SavedTab(tab).toJSON()),
          stats
        };
      })
    );
    
    const stats = await workspace.getStats();
    
    res.json({
      status: 'success',
      data: {
        workspace: {
          ...workspace.toJSON(),
          groups: groupsWithTabs,
          stats
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get workspace', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.id
    });
    next(error);
  }
};

// 创建新工作空间
const createWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { name, description, color, icon, isDefault } = req.body;
    
    logger.info('Creating workspace', { userId, name });
    
    const workspace = await Workspace.create({
      userId,
      name,
      description,
      color,
      icon,
      isDefault
    });
    
    logger.info('Workspace created successfully', { 
      userId, 
      workspaceId: workspace.id,
      name: workspace.name
    });
    
    res.status(201).json({
      status: 'success',
      message: 'Workspace created successfully',
      data: {
        workspace: workspace.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to create workspace', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

// 更新工作空间
const updateWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.id;
    const updateData = req.body;
    
    logger.info('Updating workspace', { userId, workspaceId, updateData });
    
    const workspace = await Workspace.findById(workspaceId);
    
    if (!workspace) {
      return next(new AppError('Workspace not found', 404));
    }
    
    // 检查权限
    if (workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    await workspace.update(updateData);
    
    logger.info('Workspace updated successfully', { 
      userId, 
      workspaceId: workspace.id 
    });
    
    res.json({
      status: 'success',
      message: 'Workspace updated successfully',
      data: {
        workspace: workspace.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to update workspace', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.id
    });
    next(error);
  }
};

// 删除工作空间
const deleteWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.id;
    
    logger.info('Deleting workspace', { userId, workspaceId });
    
    const workspace = await Workspace.findById(workspaceId);
    
    if (!workspace) {
      return next(new AppError('Workspace not found', 404));
    }
    
    // 检查权限
    if (workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    await workspace.delete();
    
    logger.info('Workspace deleted successfully', { 
      userId, 
      workspaceId 
    });
    
    res.json({
      status: 'success',
      message: 'Workspace deleted successfully'
    });
  } catch (error) {
    logger.error('Failed to delete workspace', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.id
    });
    next(error);
  }
};

// 复制工作空间
const duplicateWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.id;
    const { name } = req.body;
    
    logger.info('Duplicating workspace', { userId, workspaceId, newName: name });
    
    const workspace = await Workspace.findById(workspaceId);
    
    if (!workspace) {
      return next(new AppError('Workspace not found', 404));
    }
    
    // 检查权限
    if (workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    const newWorkspace = await workspace.duplicate(name);
    
    logger.info('Workspace duplicated successfully', { 
      userId, 
      originalWorkspaceId: workspaceId,
      newWorkspaceId: newWorkspace.id
    });
    
    res.status(201).json({
      status: 'success',
      message: 'Workspace duplicated successfully',
      data: {
        workspace: newWorkspace.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to duplicate workspace', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.id
    });
    next(error);
  }
};

// 更新工作空间排序
const updateWorkspaceOrder = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { workspaceOrders } = req.body;
    
    logger.info('Updating workspace order', { userId, workspaceOrders });
    
    await Workspace.updateSortOrder(userId, workspaceOrders);
    
    logger.info('Workspace order updated successfully', { userId });
    
    res.json({
      status: 'success',
      message: 'Workspace order updated successfully'
    });
  } catch (error) {
    logger.error('Failed to update workspace order', { 
      error: error.message,
      userId: req.user?.id
    });
    next(error);
  }
};

// 搜索工作空间
const searchWorkspaces = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { q: searchQuery } = req.query;
    
    if (!searchQuery) {
      return next(new AppError('Search query is required', 400));
    }
    
    logger.info('Searching workspaces', { userId, searchQuery });
    
    const workspaces = await Workspace.search(userId, searchQuery);
    
    res.json({
      status: 'success',
      data: {
        workspaces: workspaces.map(workspace => workspace.toJSON()),
        query: searchQuery
      }
    });
  } catch (error) {
    logger.error('Failed to search workspaces', { 
      error: error.message,
      userId: req.user?.id,
      searchQuery: req.query.q
    });
    next(error);
  }
};

// 获取默认工作空间
const getDefaultWorkspace = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    logger.info('Getting default workspace', { userId });
    
    let defaultWorkspace = await Workspace.findDefaultByUserId(userId);
    
    // 如果没有默认工作空间，创建一个
    if (!defaultWorkspace) {
      defaultWorkspace = await Workspace.create({
        userId,
        name: 'AI工作台',
        description: '默认的AI工具工作空间',
        color: '#3B82F6',
        icon: '🤖',
        isDefault: true
      });
      
      logger.info('Created default workspace', { 
        userId, 
        workspaceId: defaultWorkspace.id 
      });
    }
    
    res.json({
      status: 'success',
      data: {
        workspace: defaultWorkspace.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to get default workspace', { 
      error: error.message,
      userId: req.user?.id
    });
    next(error);
  }
};

module.exports = {
  getWorkspaces,
  getWorkspace,
  createWorkspace,
  updateWorkspace,
  deleteWorkspace,
  duplicateWorkspace,
  updateWorkspaceOrder,
  searchWorkspaces,
  getDefaultWorkspace
};
