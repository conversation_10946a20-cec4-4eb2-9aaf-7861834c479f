const { query } = require('../config/database');
const { AppError } = require('../middleware/errorHandler');

class Workspace {
  constructor(workspaceData) {
    this.id = workspaceData.id;
    this.userId = workspaceData.user_id;
    this.name = workspaceData.name;
    this.description = workspaceData.description;
    this.color = workspaceData.color;
    this.icon = workspaceData.icon;
    this.isDefault = workspaceData.is_default;
    this.sortOrder = workspaceData.sort_order;
    this.settings = workspaceData.settings;
    this.createdAt = workspaceData.created_at;
    this.updatedAt = workspaceData.updated_at;
  }

  // 创建新工作空间
  static async create({ userId, name, description, color = '#3B82F6', icon = '📁', isDefault = false }) {
    try {
      // 如果设置为默认工作空间，先取消其他默认工作空间
      if (isDefault) {
        await query(
          'UPDATE workspaces SET is_default = FALSE WHERE user_id = ?',
          [userId]
        );
      }

      // 获取排序顺序
      const sortOrderResult = await query(
        'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM workspaces WHERE user_id = ?',
        [userId]
      );
      const sortOrder = sortOrderResult[0].next_order;

      // 插入新工作空间
      const result = await query(
        `INSERT INTO workspaces (user_id, name, description, color, icon, is_default, sort_order) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [userId, name, description, color, icon, isDefault, sortOrder]
      );

      // 返回新创建的工作空间
      const newWorkspace = await this.findById(result.insertId);
      return newWorkspace;
    } catch (error) {
      console.error('Failed to create workspace:', error);
      throw new AppError('Failed to create workspace', 500);
    }
  }

  // 根据ID查找工作空间
  static async findById(id) {
    try {
      const workspaces = await query(
        'SELECT * FROM workspaces WHERE id = ?',
        [id]
      );
      
      if (workspaces.length === 0) {
        return null;
      }
      
      return new Workspace(workspaces[0]);
    } catch (error) {
      console.error('Failed to find workspace by id:', error);
      throw new AppError('Failed to find workspace', 500);
    }
  }

  // 根据用户ID获取所有工作空间
  static async findByUserId(userId) {
    try {
      const workspaces = await query(
        'SELECT * FROM workspaces WHERE user_id = ? ORDER BY sort_order ASC, created_at ASC',
        [userId]
      );
      
      return workspaces.map(workspace => new Workspace(workspace));
    } catch (error) {
      console.error('Failed to find workspaces by user id:', error);
      throw new AppError('Failed to find workspaces', 500);
    }
  }

  // 获取用户的默认工作空间
  static async findDefaultByUserId(userId) {
    try {
      const workspaces = await query(
        'SELECT * FROM workspaces WHERE user_id = ? AND is_default = TRUE LIMIT 1',
        [userId]
      );
      
      if (workspaces.length === 0) {
        return null;
      }
      
      return new Workspace(workspaces[0]);
    } catch (error) {
      console.error('Failed to find default workspace:', error);
      throw new AppError('Failed to find default workspace', 500);
    }
  }

  // 更新工作空间
  async update(updateData) {
    try {
      const allowedFields = ['name', 'description', 'color', 'icon', 'is_default', 'sort_order', 'settings'];
      const updates = [];
      const values = [];

      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key)) {
          const dbField = key === 'isDefault' ? 'is_default' : 
                         key === 'sortOrder' ? 'sort_order' : key;
          updates.push(`${dbField} = ?`);
          values.push(typeof value === 'object' ? JSON.stringify(value) : value);
        }
      }

      if (updates.length === 0) {
        throw new AppError('No valid fields to update', 400);
      }

      // 如果设置为默认工作空间，先取消其他默认工作空间
      if (updateData.isDefault || updateData.is_default) {
        await query(
          'UPDATE workspaces SET is_default = FALSE WHERE user_id = ? AND id != ?',
          [this.userId, this.id]
        );
      }

      values.push(this.id);
      await query(
        `UPDATE workspaces SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      // 重新获取更新后的工作空间数据
      const updatedWorkspace = await Workspace.findById(this.id);
      Object.assign(this, updatedWorkspace);
    } catch (error) {
      console.error('Failed to update workspace:', error);
      throw new AppError('Failed to update workspace', 500);
    }
  }

  // 删除工作空间
  async delete() {
    try {
      // 检查是否为默认工作空间
      if (this.isDefault) {
        throw new AppError('Cannot delete default workspace', 400);
      }

      // 删除工作空间（级联删除相关的分组和标签页）
      await query('DELETE FROM workspaces WHERE id = ?', [this.id]);
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      throw new AppError('Failed to delete workspace', 500);
    }
  }

  // 获取工作空间的分组
  async getGroups() {
    try {
      const groups = await query(
        'SELECT * FROM tab_groups WHERE workspace_id = ? ORDER BY sort_order ASC, created_at ASC',
        [this.id]
      );
      
      return groups;
    } catch (error) {
      console.error('Failed to get workspace groups:', error);
      throw new AppError('Failed to get workspace groups', 500);
    }
  }

  // 获取工作空间的统计信息
  async getStats() {
    try {
      const stats = await query(
        `SELECT 
          COUNT(DISTINCT tg.id) as group_count,
          COUNT(st.id) as tab_count,
          COUNT(CASE WHEN st.is_pinned = TRUE THEN 1 END) as pinned_count
         FROM workspaces w
         LEFT JOIN tab_groups tg ON w.id = tg.workspace_id
         LEFT JOIN saved_tabs st ON tg.id = st.group_id
         WHERE w.id = ?`,
        [this.id]
      );
      
      return stats[0];
    } catch (error) {
      console.error('Failed to get workspace stats:', error);
      throw new AppError('Failed to get workspace stats', 500);
    }
  }

  // 复制工作空间
  async duplicate(newName) {
    try {
      // 创建新工作空间
      const newWorkspace = await Workspace.create({
        userId: this.userId,
        name: newName || `${this.name} (副本)`,
        description: this.description,
        color: this.color,
        icon: this.icon,
        isDefault: false
      });

      // 复制分组
      const groups = await this.getGroups();
      for (const group of groups) {
        const newGroupResult = await query(
          `INSERT INTO tab_groups (workspace_id, name, description, color, icon, is_preset, preset_type, sort_order)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [newWorkspace.id, group.name, group.description, group.color, group.icon, 
           group.is_preset, group.preset_type, group.sort_order]
        );

        // 复制标签页
        const tabs = await query(
          'SELECT * FROM saved_tabs WHERE group_id = ?',
          [group.id]
        );

        for (const tab of tabs) {
          await query(
            `INSERT INTO saved_tabs (user_id, group_id, title, url, favicon_url, description, tags, is_pinned)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [this.userId, newGroupResult.insertId, tab.title, tab.url, tab.favicon_url,
             tab.description, tab.tags, tab.is_pinned]
          );
        }
      }

      return newWorkspace;
    } catch (error) {
      console.error('Failed to duplicate workspace:', error);
      throw new AppError('Failed to duplicate workspace', 500);
    }
  }

  // 更新排序顺序
  static async updateSortOrder(userId, workspaceOrders) {
    try {
      for (const { id, sortOrder } of workspaceOrders) {
        await query(
          'UPDATE workspaces SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
          [sortOrder, id, userId]
        );
      }
    } catch (error) {
      console.error('Failed to update workspace sort order:', error);
      throw new AppError('Failed to update workspace sort order', 500);
    }
  }

  // 搜索工作空间
  static async search(userId, searchQuery) {
    try {
      const workspaces = await query(
        `SELECT * FROM workspaces 
         WHERE user_id = ? AND (name LIKE ? OR description LIKE ?)
         ORDER BY sort_order ASC, created_at ASC`,
        [userId, `%${searchQuery}%`, `%${searchQuery}%`]
      );
      
      return workspaces.map(workspace => new Workspace(workspace));
    } catch (error) {
      console.error('Failed to search workspaces:', error);
      throw new AppError('Failed to search workspaces', 500);
    }
  }

  // 转换为安全的JSON格式
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      color: this.color,
      icon: this.icon,
      isDefault: this.isDefault,
      sortOrder: this.sortOrder,
      settings: this.settings,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Workspace;
