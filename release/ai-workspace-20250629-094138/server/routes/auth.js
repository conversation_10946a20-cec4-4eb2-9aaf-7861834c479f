const express = require('express');
const router = express.Router();

const authController = require('../controllers/authController');
const { authenticate } = require('../middleware/auth');
const {
  validateRegister,
  validateLogin,
  validateUpdateProfile,
  validateChangePassword
} = require('../middleware/validation');

// 公开路由（不需要认证）
router.post('/register', validateRegister, authController.register);
router.post('/login', validateLogin, authController.login);

// 需要认证的路由
router.use(authenticate); // 以下所有路由都需要认证

router.get('/profile', authController.getProfile);
router.put('/profile', validateUpdateProfile, authController.updateProfile);
router.post('/change-password', validateChangePassword, authController.changePassword);
router.post('/logout', authController.logout);
router.get('/verify', authController.verifyToken);
router.post('/refresh', authController.refreshToken);

module.exports = router;