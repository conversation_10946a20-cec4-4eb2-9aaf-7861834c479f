const User = require('../models/User');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

// JWT认证中间件
const authenticate = async (req, res, next) => {
  try {
    // 获取token
    let token;
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
    }

    if (!token) {
      return next(new AppError('Access token is required', 401));
    }

    // 验证token
    const decoded = User.verifyToken(token);
    
    // 获取用户信息
    const user = await User.findById(decoded.id);
    if (!user) {
      return next(new AppError('User not found or inactive', 401));
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;
    
    logger.info('User authenticated', { 
      userId: user.id, 
      username: user.username 
    });
    
    next();
  } catch (error) {
    logger.error('Authentication failed', { error: error.message });
    next(new AppError('Authentication failed', 401));
  }
};

// 可选认证中间件（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    let token;
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else if (req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
    }

    if (token) {
      try {
        const decoded = User.verifyToken(token);
        const user = await User.findById(decoded.id);
        if (user) {
          req.user = user;
          req.token = token;
        }
      } catch (error) {
        // 忽略token验证错误，继续处理请求
        logger.warn('Optional auth failed', { error: error.message });
      }
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

// 权限检查中间件
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401));
    }

    // 这里可以扩展角色权限检查
    // 目前所有认证用户都有相同权限
    next();
  };
};

module.exports = {
  authenticate,
  optionalAuth,
  authorize
};