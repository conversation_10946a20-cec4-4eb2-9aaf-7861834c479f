// 简化版logger，避免winston依赖问题
const fs = require('fs');
const path = require('path');

// 创建logs目录
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3
};

const currentLevel = levels[process.env.LOG_LEVEL] || levels.info;

// 格式化时间
const formatTime = () => {
  return new Date().toISOString().replace('T', ' ').substr(0, 19);
};

// 写入日志文件
const writeToFile = (level, message, meta = {}) => {
  const timestamp = formatTime();
  const logEntry = {
    timestamp,
    level,
    message,
    ...meta
  };
  
  const logLine = JSON.stringify(logEntry) + '\n';
  
  // 写入对应级别的日志文件
  const filename = level === 'error' ? 'error.log' : 'combined.log';
  const filepath = path.join(logsDir, filename);
  
  fs.appendFileSync(filepath, logLine);
};

// 控制台输出
const logToConsole = (level, message, meta = {}) => {
  const timestamp = formatTime();
  const colors = {
    error: '\x1b[31m',
    warn: '\x1b[33m', 
    info: '\x1b[36m',
    debug: '\x1b[37m'
  };
  const reset = '\x1b[0m';
  
  let output = `${colors[level]}${timestamp} [${level.toUpperCase()}]: ${message}${reset}`;
  if (Object.keys(meta).length > 0) {
    output += ` ${JSON.stringify(meta)}`;
  }
  
  console.log(output);
};

// 创建logger对象
const logger = {
  error: (message, meta = {}) => {
    if (levels.error <= currentLevel) {
      logToConsole('error', message, meta);
      writeToFile('error', message, meta);
    }
  },
  
  warn: (message, meta = {}) => {
    if (levels.warn <= currentLevel) {
      logToConsole('warn', message, meta);
      writeToFile('warn', message, meta);
    }
  },
  
  info: (message, meta = {}) => {
    if (levels.info <= currentLevel) {
      logToConsole('info', message, meta);
      writeToFile('info', message, meta);
    }
  },
  
  debug: (message, meta = {}) => {
    if (levels.debug <= currentLevel) {
      logToConsole('debug', message, meta);
      writeToFile('debug', message, meta);
    }
  }
};

module.exports = logger;