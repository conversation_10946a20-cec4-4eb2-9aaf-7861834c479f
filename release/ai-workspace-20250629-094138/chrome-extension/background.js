(()=>{"use strict";class t{static STORAGE_KEYS={WORKSPACES:"workspaces",ACTIVE_WORKSPACE:"activeWorkspace",USER_SETTINGS:"userSettings",SYNC_DATA:"syncData",AUTH_TOKEN:"authToken"};static async getWorkspaces(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.WORKSPACES]))[this.STORAGE_KEYS.WORKSPACES]||[]}catch(t){return console.error("Failed to get workspaces:",t),[]}}static async getWorkspace(t){try{return(await this.getWorkspaces()).find(e=>e.id===t)||null}catch(t){return console.error("Failed to get workspace:",t),null}}static async saveWorkspace(t){try{const e=await this.getWorkspaces(),r=e.findIndex(e=>e.id===t.id);return r>=0?e[r]=t:e.push(t),await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:e}),!0}catch(t){return console.error("Failed to save workspace:",t),!1}}static async deleteWorkspace(t){try{const e=(await this.getWorkspaces()).filter(e=>e.id!==t);return await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:e}),!0}catch(t){return console.error("Failed to delete workspace:",t),!1}}static async getActiveWorkspace(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.ACTIVE_WORKSPACE]))[this.STORAGE_KEYS.ACTIVE_WORKSPACE]||null}catch(t){return console.error("Failed to get active workspace:",t),null}}static async setActiveWorkspace(t){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.ACTIVE_WORKSPACE]:t}),!0}catch(t){return console.error("Failed to set active workspace:",t),!1}}static async saveTabsToWorkspace(t,e){try{const r=await this.getWorkspace(e);if(!r)throw new Error("Workspace not found");const a={id:`group_${Date.now()}`,name:`保存的标签页 ${(new Date).toLocaleString()}`,color:"#3B82F6",icon:"bookmark",tabs:t,sortOrder:r.groups.length};return r.groups.push(a),r.updatedAt=(new Date).toISOString(),await this.saveWorkspace(r)}catch(t){return console.error("Failed to save tabs to workspace:",t),!1}}static async getUserSettings(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.USER_SETTINGS]))[this.STORAGE_KEYS.USER_SETTINGS]||{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}catch(t){return console.error("Failed to get user settings:",t),{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}}static async saveUserSettings(t){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.USER_SETTINGS]:t}),!0}catch(t){return console.error("Failed to save user settings:",t),!1}}static async getAuthToken(){try{return(await chrome.storage.local.get([this.STORAGE_KEYS.AUTH_TOKEN]))[this.STORAGE_KEYS.AUTH_TOKEN]||null}catch(t){return console.error("Failed to get auth token:",t),null}}static async saveAuthToken(t){try{return await chrome.storage.local.set({[this.STORAGE_KEYS.AUTH_TOKEN]:t}),!0}catch(t){return console.error("Failed to save auth token:",t),!1}}static async clearAuthToken(){try{return await chrome.storage.local.remove([this.STORAGE_KEYS.AUTH_TOKEN]),!0}catch(t){return console.error("Failed to clear auth token:",t),!1}}static async clearAllData(){try{return await chrome.storage.sync.clear(),await chrome.storage.local.clear(),!0}catch(t){return console.error("Failed to clear all data:",t),!1}}}chrome.runtime.onInstalled.addListener(async e=>{console.log("AI工作台扩展已安装",e),"install"===e.reason&&(await async function(){try{const e={id:"default",name:"AI工作台",description:"默认的AI工具工作空间",color:"#3B82F6",icon:"🤖",groups:[],isDefault:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};await t.saveWorkspace(e),await t.setActiveWorkspace("default"),console.log("默认工作空间已创建")}catch(t){console.error("创建默认工作空间失败:",t)}}(),chrome.tabs.create({url:chrome.runtime.getURL("options.html")}))}),chrome.tabs.onUpdated.addListener(async(e,r,a)=>{"complete"===r.status&&a.url&&await async function(e){try{(await t.getUserSettings()).autoSave&&console.log("标签页更新:",e.title,e.url)}catch(t){console.error("处理标签页更新失败:",t)}}(a)}),chrome.commands.onCommand.addListener(async t=>{switch(console.log("快捷键命令:",t),t){case"toggle-workspace":await async function(){try{chrome.action.openPopup()}catch(t){console.error("切换工作空间失败:",t)}}();break;case"quick-search":await async function(){try{chrome.action.openPopup()}catch(t){console.error("快速搜索失败:",t)}}()}}),chrome.runtime.onMessage.addListener((e,r,a)=>{switch(console.log("收到消息:",e),e.type){case"GET_CURRENT_TABS":return async function(t){try{t({success:!0,tabs:await chrome.tabs.query({currentWindow:!0})})}catch(e){console.error("获取标签页失败:",e),t({success:!1,error:e.message})}}(a),!0;case"OPEN_WORKSPACE":return async function(e,r){try{const a=await t.getWorkspace(e);if(!a)throw new Error("工作空间不存在");const s=[];a.groups.forEach(t=>{t.tabs.forEach(t=>{t.url&&s.push(t.url)})});for(const t of s)await chrome.tabs.create({url:t,active:!1});r({success:!0,message:`已打开 ${s.length} 个标签页`})}catch(t){console.error("打开工作空间失败:",t),r({success:!1,error:t.message})}}(e.workspaceId,a),!0;case"SAVE_TABS":return async function(e,r,a){try{await t.saveTabsToWorkspace(e,r),a({success:!0,message:"标签页已保存"})}catch(t){console.error("保存标签页失败:",t),a({success:!1,error:t.message})}}(e.tabs,e.workspaceId,a),!0}})})();