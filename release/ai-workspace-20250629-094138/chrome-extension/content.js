(()=>{"use strict";function e(){const e=document.querySelector('meta[name="description"]');if(e)return e.content;const t=document.querySelector('meta[property="og:description"]');if(t)return t.content;const n=document.querySelector("p");return n&&n.textContent?n.textContent.substring(0,200):""}function t(){const e=document.querySelector('meta[name="keywords"]');return e?e.content.split(",").map(e=>e.trim()):document.title.split(/\s+/).filter(e=>e.length>2).slice(0,5)}function n(){const e=document.querySelector('link[rel="icon"]')||document.querySelector('link[rel="shortcut icon"]')||document.querySelector('link[rel="apple-touch-icon"]');return e?new URL(e.href,window.location.href).href:new URL("/favicon.ico",window.location.origin).href}function o(e,t){if(e.nodeType===Node.TEXT_NODE){const o=e.textContent||"",r=new RegExp(`(${c=t,c.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})`,"gi");if(r.test(o)){var n;const t=document.createElement("span");t.innerHTML=o.replace(r,'<span class="ai-workspace-highlight">$1</span>'),null===(n=e.parentNode)||void 0===n||n.replaceChild(t,e)}}else if(e.nodeType===Node.ELEMENT_NODE){const n=e;["SCRIPT","STYLE","NOSCRIPT"].includes(n.tagName)||Array.from(n.childNodes).forEach(e=>{o(e,t)})}var c}function c(){const e=[];return document.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(t=>{var n;const o=parseInt(t.tagName.substring(1)),c=(null===(n=t.textContent)||void 0===n?void 0:n.trim())||"";c&&e.push({level:o,text:c})}),e}function r(){const e=[];return document.querySelectorAll("a[href]").forEach(t=>{var n;const o=(null===(n=t.textContent)||void 0===n?void 0:n.trim())||"",c=t.href;o&&c&&e.push({text:o,url:c})}),e.slice(0,50)}function i(){const e=[];return document.querySelectorAll("img[src]").forEach(t=>{const n=t.alt||"",o=t.src;o&&e.push({alt:n,src:o})}),e.slice(0,20)}function s(){var e;const t=["main","article",".content",".main-content","#content","#main"];for(const e of t){const t=document.querySelector(e);var n;if(t)return(null===(n=t.textContent)||void 0===n?void 0:n.trim().substring(0,1e3))||""}return(null===(e=document.body.textContent)||void 0===e?void 0:e.trim().substring(0,1e3))||""}function a(){console.log("AI工作台 Content Script 已加载"),function(){const e=window.location.hostname.toLowerCase();["chat.openai.com","gemini.google.com","claude.ai","perplexity.ai","deepask.cc"].some(t=>e.includes(t))&&(console.log("检测到AI工具网站:",e),document.body.setAttribute("data-ai-workspace-site","true"))}()}chrome.runtime.onMessage.addListener((a,l,u)=>{switch(console.log("Content script收到消息:",a),a.type){case"GET_PAGE_INFO":return function(o){try{o({success:!0,pageInfo:{title:document.title,url:window.location.href,description:e(),keywords:t(),favicon:n(),timestamp:(new Date).toISOString()}})}catch(e){console.error("获取页面信息失败:",e),o({success:!1,error:e.message})}}(u),!0;case"HIGHLIGHT_SEARCH":!function(e){if(e){if(document.querySelectorAll(".ai-workspace-highlight").forEach(e=>{const t=e.parentNode;t&&(t.replaceChild(document.createTextNode(e.textContent||""),e),t.normalize())}),!document.getElementById("ai-workspace-highlight-style")){const e=document.createElement("style");e.id="ai-workspace-highlight-style",e.textContent="\n      .ai-workspace-highlight {\n        background-color: #fef3c7 !important;\n        color: #92400e !important;\n        padding: 1px 2px !important;\n        border-radius: 2px !important;\n      }\n    ",document.head.appendChild(e)}o(document.body,e)}}(a.query);break;case"EXTRACT_CONTENT":return function(e){try{e({success:!0,content:{title:document.title,headings:c(),links:r(),images:i(),text:s()}})}catch(t){console.error("提取页面内容失败:",t),e({success:!1,error:t.message})}}(u),!0}}),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",a):a()})();