(()=>{"use strict";var e,t={507:(e,t,s)=>{s.d(t,{StorageManager:()=>a});class a{static STORAGE_KEYS={WORKSPACES:"workspaces",ACTIVE_WORKSPACE:"activeWorkspace",USER_SETTINGS:"userSettings",SYNC_DATA:"syncData",AUTH_TOKEN:"authToken"};static async getWorkspaces(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.WORKSPACES]))[this.STORAGE_KEYS.WORKSPACES]||[]}catch(e){return console.error("Failed to get workspaces:",e),[]}}static async getWorkspace(e){try{return(await this.getWorkspaces()).find(t=>t.id===e)||null}catch(e){return console.error("Failed to get workspace:",e),null}}static async saveWorkspace(e){try{const t=await this.getWorkspaces(),s=t.findIndex(t=>t.id===e.id);return s>=0?t[s]=e:t.push(e),await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:t}),!0}catch(e){return console.error("Failed to save workspace:",e),!1}}static async deleteWorkspace(e){try{const t=(await this.getWorkspaces()).filter(t=>t.id!==e);return await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:t}),!0}catch(e){return console.error("Failed to delete workspace:",e),!1}}static async getActiveWorkspace(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.ACTIVE_WORKSPACE]))[this.STORAGE_KEYS.ACTIVE_WORKSPACE]||null}catch(e){return console.error("Failed to get active workspace:",e),null}}static async setActiveWorkspace(e){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.ACTIVE_WORKSPACE]:e}),!0}catch(e){return console.error("Failed to set active workspace:",e),!1}}static async saveTabsToWorkspace(e,t){try{const s=await this.getWorkspace(t);if(!s)throw new Error("Workspace not found");const a={id:`group_${Date.now()}`,name:`保存的标签页 ${(new Date).toLocaleString()}`,color:"#3B82F6",icon:"bookmark",tabs:e,sortOrder:s.groups.length};return s.groups.push(a),s.updatedAt=(new Date).toISOString(),await this.saveWorkspace(s)}catch(e){return console.error("Failed to save tabs to workspace:",e),!1}}static async getUserSettings(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.USER_SETTINGS]))[this.STORAGE_KEYS.USER_SETTINGS]||{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}catch(e){return console.error("Failed to get user settings:",e),{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}}static async saveUserSettings(e){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.USER_SETTINGS]:e}),!0}catch(e){return console.error("Failed to save user settings:",e),!1}}static async getAuthToken(){try{return(await chrome.storage.local.get([this.STORAGE_KEYS.AUTH_TOKEN]))[this.STORAGE_KEYS.AUTH_TOKEN]||null}catch(e){return console.error("Failed to get auth token:",e),null}}static async saveAuthToken(e){try{return await chrome.storage.local.set({[this.STORAGE_KEYS.AUTH_TOKEN]:e}),!0}catch(e){return console.error("Failed to save auth token:",e),!1}}static async clearAuthToken(){try{return await chrome.storage.local.remove([this.STORAGE_KEYS.AUTH_TOKEN]),!0}catch(e){return console.error("Failed to clear auth token:",e),!1}}static async clearAllData(){try{return await chrome.storage.sync.clear(),await chrome.storage.local.clear(),!0}catch(e){return console.error("Failed to clear all data:",e),!1}}}},738:(e,t,s)=>{var a=s(540),r=s(338);class o{static async getCurrentTabs(){try{return(await chrome.tabs.query({currentWindow:!0})).map(e=>({id:e.id,title:e.title||"",url:e.url||"",favIconUrl:e.favIconUrl,active:e.active,pinned:e.pinned,groupId:e.groupId,windowId:e.windowId,index:e.index}))}catch(e){return console.error("Failed to get current tabs:",e),[]}}static async getCurrentActiveTab(){try{const e=await chrome.tabs.query({active:!0,currentWindow:!0});if(0===e.length)return null;const t=e[0];return{id:t.id,title:t.title||"",url:t.url||"",favIconUrl:t.favIconUrl,active:t.active,pinned:t.pinned,groupId:t.groupId,windowId:t.windowId,index:t.index}}catch(e){return console.error("Failed to get current active tab:",e),null}}static async getAllTabs(){try{return(await chrome.tabs.query({})).map(e=>({id:e.id,title:e.title||"",url:e.url||"",favIconUrl:e.favIconUrl,active:e.active,pinned:e.pinned,groupId:e.groupId,windowId:e.windowId,index:e.index}))}catch(e){return console.error("Failed to get all tabs:",e),[]}}static async createTab(e,t=!1){try{const s=await chrome.tabs.create({url:e,active:t});return{id:s.id,title:s.title||"",url:s.url||"",favIconUrl:s.favIconUrl,active:s.active,pinned:s.pinned,groupId:s.groupId,windowId:s.windowId,index:s.index}}catch(e){return console.error("Failed to create tab:",e),null}}static async closeTab(e){try{return await chrome.tabs.remove(e),!0}catch(e){return console.error("Failed to close tab:",e),!1}}static async updateTab(e,t){try{const s=await chrome.tabs.update(e,t);return{id:s.id,title:s.title||"",url:s.url||"",favIconUrl:s.favIconUrl,active:s.active,pinned:s.pinned,groupId:s.groupId,windowId:s.windowId,index:s.index}}catch(e){throw console.error("Failed to update tab:",e),e}}static async activateTab(e){try{return await chrome.tabs.update(e,{active:!0}),!0}catch(e){return console.error("Failed to activate tab:",e),!1}}static async switchToTab(e){return this.activateTab(e)}static async reloadTab(e){try{return await chrome.tabs.reload(e),!0}catch(e){return console.error("Failed to reload tab:",e),!1}}static async duplicateTab(e){try{const t=await chrome.tabs.query({id:e});if(0===t.length)throw new Error("Tab not found");const s=t[0],a=await chrome.tabs.create({url:s.url,index:s.index+1,active:!1});return{id:a.id,title:a.title||"",url:a.url||"",favIconUrl:a.favIconUrl,active:a.active,pinned:a.pinned,groupId:a.groupId,windowId:a.windowId,index:a.index}}catch(e){throw console.error("Failed to duplicate tab:",e),e}}static async pinTab(e){try{return await chrome.tabs.update(e,{pinned:!0}),!0}catch(e){return console.error("Failed to pin tab:",e),!1}}static async unpinTab(e){try{return await chrome.tabs.update(e,{pinned:!1}),!0}catch(e){return console.error("Failed to unpin tab:",e),!1}}static async openTabs(e){const t=[];for(const s of e){const e=await this.createTab(s,!1);e&&t.push(e)}return t}static async saveTabsToWorkspace(e,t){try{const{StorageManager:a}=await Promise.resolve().then(s.bind(s,507));return await a.saveTabsToWorkspace(e,t)}catch(e){return console.error("Failed to save tabs to workspace:",e),!1}}static async openWorkspace(e){try{const{StorageManager:t}=await Promise.resolve().then(s.bind(s,507)),a=await t.getWorkspace(e);if(!a)throw new Error("Workspace not found");const r=[];return a.groups.forEach(e=>{e.tabs.forEach(e=>{e.url&&r.push(e.url)})}),await this.openTabs(r),!0}catch(e){return console.error("Failed to open workspace:",e),!1}}static async createTabGroup(e,t){try{const s=await chrome.tabs.group({tabIds:e});return await chrome.tabGroups.update(s,{title:t}),s}catch(e){return console.error("Failed to create tab group:",e),null}}static async moveTabsToGroup(e,t){try{return await chrome.tabs.group({tabIds:e,groupId:t}),!0}catch(e){return console.error("Failed to move tabs to group:",e),!1}}static async searchTabs(e){try{const t=await this.getAllTabs(),s=e.toLowerCase();return t.filter(e=>e.title.toLowerCase().includes(s)||e.url.toLowerCase().includes(s))}catch(e){return console.error("Failed to search tabs:",e),[]}}static async getDuplicateTabs(){try{const e=await this.getAllTabs(),t=new Map;return e.forEach(e=>{const s=e.url;t.has(s)||t.set(s,[]),t.get(s).push(e)}),Array.from(t.values()).filter(e=>e.length>1)}catch(e){return console.error("Failed to get duplicate tabs:",e),[]}}}var i=s(507),n=s(848);const c=({onSaveCurrentTabs:e,activeWorkspace:t})=>(0,n.jsxs)("div",{className:"header",children:[(0,n.jsxs)("div",{className:"header-title",children:[(0,n.jsx)("h1",{children:"🤖 AI工作台"}),(0,n.jsx)("span",{className:"version",children:"v1.0.0"})]}),(0,n.jsxs)("div",{className:"header-actions",children:[(0,n.jsx)("button",{className:"btn btn-primary",onClick:e,disabled:!t,title:t?"保存当前标签页到工作空间":"请先选择工作空间",children:"💾 保存标签页"}),(0,n.jsx)("button",{className:"btn btn-secondary",onClick:()=>chrome.runtime.openOptionsPage(),title:"打开设置页面",children:"⚙️"})]})]}),l=({workspaces:e,activeWorkspace:t,onWorkspaceChange:s,onOpenWorkspace:a})=>0===e.length?(0,n.jsx)("div",{className:"workspace-list",children:(0,n.jsxs)("div",{className:"empty-state",children:[(0,n.jsx)("p",{children:"暂无工作空间"}),(0,n.jsx)("button",{className:"btn btn-primary",onClick:()=>chrome.runtime.openOptionsPage(),children:"创建工作空间"})]})}):(0,n.jsxs)("div",{className:"workspace-list",children:[(0,n.jsx)("h3",{children:"工作空间"}),(0,n.jsx)("div",{className:"workspace-items",children:e.map(e=>(0,n.jsxs)("div",{className:"workspace-item "+(t===e.id?"active":""),children:[(0,n.jsxs)("div",{className:"workspace-info",onClick:()=>s(e.id),children:[(0,n.jsx)("span",{className:"workspace-icon",style:{color:e.color},children:e.icon}),(0,n.jsxs)("div",{className:"workspace-details",children:[(0,n.jsx)("span",{className:"workspace-name",children:e.name}),(0,n.jsxs)("span",{className:"workspace-count",children:[e.groups.reduce((e,t)=>e+t.tabs.length,0)," 个标签页"]})]})]}),(0,n.jsx)("div",{className:"workspace-actions",children:(0,n.jsx)("button",{className:"btn btn-small",onClick:()=>a(e.id),title:"打开工作空间",children:"🚀"})})]},e.id))})]}),d=({tabs:e,searchQuery:t})=>{const s=e=>{if(e.favIconUrl)return e.favIconUrl;try{return`https://www.google.com/s2/favicons?domain=${new URL(e.url).hostname}&sz=16`}catch{return"🌐"}},a=(e,t)=>{if(!t)return e;const s=new RegExp(`(${t})`,"gi");return e.split(s).map((e,t)=>s.test(e)?(0,n.jsx)("mark",{className:"highlight",children:e},t):e)};return 0===e.length?(0,n.jsx)("div",{className:"tab-list",children:(0,n.jsx)("div",{className:"empty-state",children:(0,n.jsx)("p",{children:t?"未找到匹配的标签页":"当前窗口没有标签页"})})}):(0,n.jsxs)("div",{className:"tab-list",children:[(0,n.jsxs)("h3",{children:["当前标签页 (",e.length,")"]}),(0,n.jsx)("div",{className:"tab-items",children:e.map(e=>(0,n.jsxs)("div",{className:`tab-item ${e.active?"active":""} ${e.pinned?"pinned":""}`,onClick:()=>(async e=>{e.id&&(await o.activateTab(e.id),window.close())})(e),children:[(0,n.jsxs)("div",{className:"tab-favicon",children:["string"==typeof s(e)?(0,n.jsx)("img",{src:s(e),alt:"",onError:e=>{e.target.style.display="none",e.target.nextElementSibling.textContent="🌐"}}):(0,n.jsx)("span",{children:s(e)}),(0,n.jsx)("span",{style:{display:"none"},children:"🌐"})]}),(0,n.jsxs)("div",{className:"tab-info",children:[(0,n.jsx)("div",{className:"tab-title",children:a(e.title,t)}),(0,n.jsx)("div",{className:"tab-url",children:a(e.url,t)})]}),(0,n.jsxs)("div",{className:"tab-actions",children:[e.pinned&&(0,n.jsx)("span",{className:"pin-indicator",title:"已固定",children:"📌"}),(0,n.jsx)("button",{className:"close-button",onClick:t=>(async(e,t)=>{t.stopPropagation(),e.id&&await o.closeTab(e.id)})(e,t),title:"关闭标签页",children:"✕"})]})]},e.id||e.url))})]})},h=({value:e,onChange:t,placeholder:s="搜索..."})=>(0,n.jsx)("div",{className:"search-bar",children:(0,n.jsxs)("div",{className:"search-input-wrapper",children:[(0,n.jsx)("span",{className:"search-icon",children:"🔍"}),(0,n.jsx)("input",{type:"text",className:"search-input",value:e,onChange:e=>t(e.target.value),placeholder:s}),e&&(0,n.jsx)("button",{className:"clear-button",onClick:()=>t(""),title:"清除搜索",children:"✕"})]})});class u{static async createTabGroup(e,t="#3B82F6",s="📁"){try{const s=(await chrome.tabs.query({currentWindow:!0})).map(e=>e.id).filter(e=>void 0!==e);if(0===s.length)throw new Error("No tabs to group");const a=await chrome.tabs.group({tabIds:s});return await chrome.tabGroups.update(a,{title:e,color:t}),console.log(`Created tab group: ${e} with ${s.length} tabs`),a}catch(e){return console.error("Failed to create tab group:",e),null}}static async getAllTabGroups(){try{return await chrome.tabGroups.query({})}catch(e){return console.error("Failed to get tab groups:",e),[]}}static async getTabsInGroup(e){try{return(await chrome.tabs.query({groupId:e})).map(e=>({id:e.id,title:e.title||"",url:e.url||"",favIconUrl:e.favIconUrl,active:e.active,pinned:e.pinned,groupId:e.groupId,windowId:e.windowId,index:e.index}))}catch(e){return console.error("Failed to get tabs in group:",e),[]}}static async addTabsToGroup(e,t){try{return await chrome.tabs.group({tabIds:e,groupId:t}),console.log(`Added ${e.length} tabs to group ${t}`),!0}catch(e){return console.error("Failed to add tabs to group:",e),!1}}static async removeTabsFromGroup(e){try{return await chrome.tabs.ungroup(e),console.log(`Removed ${e.length} tabs from group`),!0}catch(e){return console.error("Failed to remove tabs from group:",e),!1}}static async updateTabGroup(e,t){try{const s={};return t.title&&(s.title=t.title),t.color&&(s.color=t.color),void 0!==t.collapsed&&(s.collapsed=t.collapsed),await chrome.tabGroups.update(e,s),console.log(`Updated tab group ${e}:`,t),!0}catch(e){return console.error("Failed to update tab group:",e),!1}}static async deleteTabGroup(e,t=!1){try{if(t){const t=(await this.getTabsInGroup(e)).map(e=>e.id).filter(e=>void 0!==e);t.length>0&&await chrome.tabs.remove(t)}else{const t=(await this.getTabsInGroup(e)).map(e=>e.id).filter(e=>void 0!==e);t.length>0&&await chrome.tabs.ungroup(t)}return console.log(`Deleted tab group ${e}, closeTabsToo: ${t}`),!0}catch(e){return console.error("Failed to delete tab group:",e),!1}}static async toggleGroupCollapse(e){try{const t=await chrome.tabGroups.get(e);return await chrome.tabGroups.update(e,{collapsed:!t.collapsed}),console.log(`Toggled group ${e} collapse to ${!t.collapsed}`),!0}catch(e){return console.error("Failed to toggle group collapse:",e),!1}}static async autoGroupByDomain(){try{const e=await chrome.tabs.query({currentWindow:!0}),t=new Map;e.forEach(e=>{if(e.url)try{const s=new URL(e.url).hostname;t.has(s)||t.set(s,[]),t.get(s).push(e)}catch(e){}});for(const[e,s]of t)if(s.length>1){const t=s.map(e=>e.id).filter(e=>void 0!==e);if(t.length>1){const s=await chrome.tabs.group({tabIds:t});await chrome.tabGroups.update(s,{title:e,color:this.getColorForDomain(e)})}}return console.log(`Auto-grouped tabs by domain: ${t.size} domains`),!0}catch(e){return console.error("Failed to auto-group by domain:",e),!1}}static getColorForDomain(e){const t=["blue","red","yellow","green","pink","purple","cyan","orange"];let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)&4294967295;return t[Math.abs(s)%t.length]}static async autoGroupByType(){try{const e=await chrome.tabs.query({currentWindow:!0}),t=new Map,s={AI工具:["chat.openai.com","gemini.google.com","claude.ai","perplexity.ai"],开发工具:["github.com","stackoverflow.com","developer.mozilla.org"],社交媒体:["twitter.com","facebook.com","linkedin.com","instagram.com"],视频平台:["youtube.com","bilibili.com","netflix.com"],购物网站:["amazon.com","taobao.com","jd.com"],新闻资讯:["news.google.com","bbc.com","cnn.com"]};e.forEach(e=>{if(e.url)try{const a=new URL(e.url).hostname;let r=!1;for(const[o,i]of Object.entries(s))if(i.some(e=>a.includes(e))){t.has(o)||t.set(o,[]),t.get(o).push(e),r=!0;break}r||(t.has("其他")||t.set("其他",[]),t.get("其他").push(e))}catch(e){}});for(const[e,s]of t)if(s.length>1){const t=s.map(e=>e.id).filter(e=>void 0!==e);if(t.length>1){const s=await chrome.tabs.group({tabIds:t});await chrome.tabGroups.update(s,{title:e,color:this.getColorForType(e)})}}return console.log(`Auto-grouped tabs by type: ${t.size} types`),!0}catch(e){return console.error("Failed to auto-group by type:",e),!1}}static getColorForType(e){return{AI工具:"purple",开发工具:"blue",社交媒体:"pink",视频平台:"red",购物网站:"orange",新闻资讯:"green",其他:"grey"}[e]||"grey"}static async saveGroupToWorkspace(e,t){try{const s=await this.getTabsInGroup(e),a=await chrome.tabGroups.get(e),r={id:`group_${e}_${Date.now()}`,name:a.title||"未命名分组",color:a.color,icon:"📁",tabs:s,sortOrder:0},o=await i.StorageManager.getWorkspace(t);return!!o&&(o.groups.push(r),o.updatedAt=(new Date).toISOString(),await i.StorageManager.saveWorkspace(o),console.log(`Saved group ${e} to workspace ${t}`),!0)}catch(e){return console.error("Failed to save group to workspace:",e),!1}}}class p{static operations=new Map;static listeners=new Set;static addProgressListener(e){this.listeners.add(e)}static removeProgressListener(e){this.listeners.delete(e)}static notifyProgress(e){this.listeners.forEach(t=>t(e))}static async closeTabs(e,t="批量关闭标签页"){const s=`close_${Date.now()}`,a={id:s,name:t,description:`关闭 ${e.length} 个标签页`,progress:0,total:e.length,status:"pending",results:[],errors:[],startTime:new Date};this.operations.set(s,a),this.notifyProgress(a);try{a.status="running",this.notifyProgress(a);for(let t=0;t<e.length;t++){const s=e[t];try{s.id&&(await o.closeTab(s.id),a.results.push({tabId:s.id,success:!0}))}catch(e){a.errors.push(`Failed to close tab ${s.title}: ${e}`),a.results.push({tabId:s.id,success:!1,error:e})}a.progress=t+1,this.notifyProgress(a),await new Promise(e=>setTimeout(e,50))}return a.status="completed",a.endTime=new Date,this.notifyProgress(a),console.log(`Batch close completed: ${a.results.filter(e=>e.success).length}/${e.length} successful`),a}catch(e){throw a.status="failed",a.errors.push(`Batch operation failed: ${e}`),a.endTime=new Date,this.notifyProgress(a),e}}static async openTabs(e,t="批量打开标签页"){const s=`open_${Date.now()}`,a={id:s,name:t,description:`打开 ${e.length} 个标签页`,progress:0,total:e.length,status:"pending",results:[],errors:[],startTime:new Date};this.operations.set(s,a),this.notifyProgress(a);try{a.status="running",this.notifyProgress(a);for(let t=0;t<e.length;t++){const s=e[t];try{const e=await o.createTab(s,!1);a.results.push({url:s,tab:e,success:!0})}catch(e){a.errors.push(`Failed to open tab ${s}: ${e}`),a.results.push({url:s,success:!1,error:e})}a.progress=t+1,this.notifyProgress(a),await new Promise(e=>setTimeout(e,100))}return a.status="completed",a.endTime=new Date,this.notifyProgress(a),console.log(`Batch open completed: ${a.results.filter(e=>e.success).length}/${e.length} successful`),a}catch(e){throw a.status="failed",a.errors.push(`Batch operation failed: ${e}`),a.endTime=new Date,this.notifyProgress(a),e}}static async moveTabsToGroup(e,t,s="批量移动到分组"){const a=`move_${Date.now()}`,r={id:a,name:s,description:`移动 ${e.length} 个标签页到分组`,progress:0,total:e.length,status:"pending",results:[],errors:[],startTime:new Date};this.operations.set(a,r),this.notifyProgress(r);try{r.status="running",this.notifyProgress(r);for(let s=0;s<e.length;s++){const a=e[s];try{await chrome.tabs.group({tabIds:[a],groupId:t}),r.results.push({tabId:a,success:!0})}catch(e){r.errors.push(`Failed to move tab ${a}: ${e}`),r.results.push({tabId:a,success:!1,error:e})}r.progress=s+1,this.notifyProgress(r),await new Promise(e=>setTimeout(e,50))}return r.status="completed",r.endTime=new Date,this.notifyProgress(r),console.log(`Batch move completed: ${r.results.filter(e=>e.success).length}/${e.length} successful`),r}catch(e){throw r.status="failed",r.errors.push(`Batch operation failed: ${e}`),r.endTime=new Date,this.notifyProgress(r),e}}static async pinTabs(e,t,s){const a=`pin_${Date.now()}`,r={id:a,name:s||(t?"批量固定标签页":"批量取消固定标签页"),description:`${t?"固定":"取消固定"} ${e.length} 个标签页`,progress:0,total:e.length,status:"pending",results:[],errors:[],startTime:new Date};this.operations.set(a,r),this.notifyProgress(r);try{r.status="running",this.notifyProgress(r);for(let s=0;s<e.length;s++){const a=e[s];try{await chrome.tabs.update(a,{pinned:t}),r.results.push({tabId:a,success:!0})}catch(e){r.errors.push(`Failed to ${t?"pin":"unpin"} tab ${a}: ${e}`),r.results.push({tabId:a,success:!1,error:e})}r.progress=s+1,this.notifyProgress(r),await new Promise(e=>setTimeout(e,50))}return r.status="completed",r.endTime=new Date,this.notifyProgress(r),console.log(`Batch ${t?"pin":"unpin"} completed: ${r.results.filter(e=>e.success).length}/${e.length} successful`),r}catch(e){throw r.status="failed",r.errors.push(`Batch operation failed: ${e}`),r.endTime=new Date,this.notifyProgress(r),e}}static async refreshTabs(e,t="批量刷新标签页"){const s=`refresh_${Date.now()}`,a={id:s,name:t,description:`刷新 ${e.length} 个标签页`,progress:0,total:e.length,status:"pending",results:[],errors:[],startTime:new Date};this.operations.set(s,a),this.notifyProgress(a);try{a.status="running",this.notifyProgress(a);for(let t=0;t<e.length;t++){const s=e[t];try{await chrome.tabs.reload(s),a.results.push({tabId:s,success:!0})}catch(e){a.errors.push(`Failed to refresh tab ${s}: ${e}`),a.results.push({tabId:s,success:!1,error:e})}a.progress=t+1,this.notifyProgress(a),await new Promise(e=>setTimeout(e,200))}return a.status="completed",a.endTime=new Date,this.notifyProgress(a),console.log(`Batch refresh completed: ${a.results.filter(e=>e.success).length}/${e.length} successful`),a}catch(e){throw a.status="failed",a.errors.push(`Batch operation failed: ${e}`),a.endTime=new Date,this.notifyProgress(a),e}}static getOperation(e){return this.operations.get(e)}static getAllOperations(){return Array.from(this.operations.values())}static cleanupCompletedOperations(){const e=new Date,t=new Date(e.getTime()-36e5);for(const[e,s]of this.operations)"completed"===s.status&&s.endTime&&s.endTime<t&&this.operations.delete(e)}static cancelOperation(e){const t=this.operations.get(e);return!(!t||"running"!==t.status||(t.status="failed",t.errors.push("Operation cancelled by user"),t.endTime=new Date,this.notifyProgress(t),0))}}class m{static DEFAULT_RULES=[{id:"ai_primary",name:"AI工作主力",description:"ChatGPT、Gemini、Claude等主力AI工具",color:"#EF4444",icon:"🤖",rules:{domains:["chat.openai.com","gemini.google.com","claude.ai","perplexity.ai","chat-preview.lobehub.com","grok.x.ai","aistudio.google.com"]},priority:10},{id:"ai_secondary",name:"AI次选",description:"DeepAsk、GPTFun等次选AI工具",color:"#F97316",icon:"🔧",rules:{domains:["deepask.cc","fun4ai.khthink.cn","new.clivia.fun","aabao.eu.cc","work.haomo.de","demo.fuclaude.com"]},priority:9},{id:"ai_tools",name:"AI其他工具",description:"Dify、提示词优化等辅助工具",color:"#8B5CF6",icon:"🛠️",rules:{domains:["dify.ai","promptpilot.volcengine.com"],keywords:["prompt","ai tool","automation"]},priority:8},{id:"tech_forums",name:"技术论坛",description:"Linux.do、NodeLoc等技术社区",color:"#06B6D4",icon:"💬",rules:{domains:["linux.do","nodeloc.cc","nodeseek.com","meta.appinn.net","app.follow.is"]},priority:7},{id:"collaboration",name:"协作工具",description:"语雀、飞书等协作平台",color:"#EC4899",icon:"👥",rules:{domains:["yuque.com","feishu.cn","notion.so","slack.com","teams.microsoft.com"]},priority:6},{id:"development",name:"开发工具",description:"GitHub、Stack Overflow等开发相关",color:"#10B981",icon:"💻",rules:{domains:["github.com","stackoverflow.com","developer.mozilla.org","npmjs.com","codepen.io"],keywords:["code","programming","development","api"]},priority:5},{id:"social_media",name:"社交媒体",description:"Twitter、LinkedIn等社交平台",color:"#3B82F6",icon:"📱",rules:{domains:["twitter.com","x.com","linkedin.com","facebook.com","instagram.com","weibo.com"]},priority:4},{id:"entertainment",name:"娱乐媒体",description:"YouTube、Netflix等娱乐平台",color:"#F59E0B",icon:"🎬",rules:{domains:["youtube.com","netflix.com","bilibili.com","twitch.tv","spotify.com"]},priority:3},{id:"shopping",name:"购物网站",description:"Amazon、淘宝等购物平台",color:"#84CC16",icon:"🛒",rules:{domains:["amazon.com","taobao.com","jd.com","tmall.com","ebay.com"]},priority:2},{id:"news",name:"新闻资讯",description:"BBC、CNN等新闻网站",color:"#6366F1",icon:"📰",rules:{domains:["bbc.com","cnn.com","reuters.com","news.google.com","techcrunch.com"]},priority:1}];static getAllRules(){return[...this.DEFAULT_RULES]}static categorizeTab(e){if(!e.url)return null;try{const t=new URL(e.url),s=t.hostname.toLowerCase(),a=t.pathname.toLowerCase(),r=e.title.toLowerCase(),o=this.DEFAULT_RULES.sort((e,t)=>t.priority-e.priority);for(const t of o){if(t.rules.domains)for(const e of t.rules.domains)if(s.includes(e.toLowerCase()))return t;if(t.rules.urlPatterns)for(const s of t.rules.urlPatterns)if(s.test(e.url))return t;if(t.rules.titlePatterns)for(const e of t.rules.titlePatterns)if(e.test(r))return t;if(t.rules.keywords)for(const e of t.rules.keywords)if(r.includes(e.toLowerCase())||a.includes(e.toLowerCase()))return t}return null}catch(e){return console.error("Error categorizing tab:",e),null}}static categorizeTabs(e){const t=new Map,s=[];for(const a of e){const e=this.categorizeTab(a);e?(t.has(e.id)||t.set(e.id,[]),t.get(e.id).push(a)):s.push(a)}return s.length>0&&t.set("uncategorized",s),t}static async createSmartGroups(){try{const e=(await chrome.tabs.query({currentWindow:!0})).map(e=>({id:e.id,title:e.title||"",url:e.url||"",favIconUrl:e.favIconUrl,active:e.active,pinned:e.pinned,groupId:e.groupId,windowId:e.windowId,index:e.index})),t=this.categorizeTabs(e);for(const[e,s]of t)if(s.length>1){const t=this.DEFAULT_RULES.find(t=>t.id===e),a=t?t.name:"未分类",r=t?t.color:"#6B7280",o=s.map(e=>e.id).filter(e=>void 0!==e);if(o.length>1)try{const e=await chrome.tabs.group({tabIds:o});await chrome.tabGroups.update(e,{title:a,color:this.getChromColorFromHex(r)}),console.log(`Created smart group: ${a} with ${o.length} tabs`)}catch(e){console.error(`Failed to create group for ${a}:`,e)}}return console.log(`Smart grouping completed: ${t.size} categories processed`),!0}catch(e){return console.error("Failed to create smart groups:",e),!1}}static getChromColorFromHex(e){return{"#EF4444":"red","#F97316":"orange","#8B5CF6":"purple","#06B6D4":"cyan","#EC4899":"pink","#10B981":"green","#3B82F6":"blue","#F59E0B":"yellow","#84CC16":"green","#6366F1":"blue"}[e]||"grey"}static getCategoryStats(e){const t=this.categorizeTabs(e),s=[];for(const e of this.DEFAULT_RULES){const a=t.get(e.id)||[];a.length>0&&s.push({category:e,count:a.length,tabs:a})}const a=t.get("uncategorized")||[];return a.length>0&&s.push({category:{id:"uncategorized",name:"未分类",description:"无法自动分类的标签页",color:"#6B7280",icon:"❓",rules:{},priority:0},count:a.length,tabs:a}),s.sort((e,t)=>t.count-e.count)}static suggestGroupingActions(e){const t=this.categorizeTabs(e),s=[];for(const[e,a]of t)if(a.length>1){const t=this.DEFAULT_RULES.find(t=>t.id===e);t&&s.push({action:"create_group",description:`将 ${a.length} 个${t.name}标签页创建为分组`,category:t,tabs:a,priority:t.priority})}return s.sort((e,t)=>t.priority-e.priority)}static findDuplicateTabs(e){const t=new Map;for(const s of e)if(s.url){const e=this.normalizeUrl(s.url);t.has(e)||t.set(e,[]),t.get(e).push(s)}const s=[];for(const[e,a]of t)a.length>1&&s.push({url:e,tabs:a,suggestion:`发现 ${a.length} 个相同的标签页，建议保留最新的一个`});return s.sort((e,t)=>t.tabs.length-e.tabs.length)}static normalizeUrl(e){try{const t=new URL(e);return["utm_source","utm_medium","utm_campaign","fbclid","gclid"].forEach(e=>t.searchParams.delete(e)),`${t.protocol}//${t.host}${t.pathname}${t.search}`}catch(t){return e}}}const g=({tabs:e,onTabsChange:t})=>{const[s,r]=(0,a.useState)(new Set),[o,i]=(0,a.useState)(null),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)([]);(0,a.useEffect)(()=>{const s=m.getCategoryStats(e);h(s);const a=e=>{i(e),"completed"!==e.status&&"failed"!==e.status||setTimeout(()=>{i(null),t()},2e3)};return p.addProgressListener(a),()=>p.removeProgressListener(a)},[e,t]);const g=async e=>{if(0===s.size)return;const t=Array.from(s);await p.pinTabs(t,e),r(new Set)},y=m.findDuplicateTabs(e),v=m.suggestGroupingActions(e);return(0,n.jsxs)("div",{className:"tab-management-panel",children:[o&&(0,n.jsxs)("div",{className:"operation-progress",children:[(0,n.jsxs)("div",{className:"progress-header",children:[(0,n.jsx)("span",{children:o.name}),(0,n.jsxs)("span",{children:[o.progress,"/",o.total]})]}),(0,n.jsx)("div",{className:"progress-bar",children:(0,n.jsx)("div",{className:"progress-fill",style:{width:o.progress/o.total*100+"%"}})}),"completed"===o.status&&(0,n.jsx)("div",{className:"progress-success",children:"✅ 操作完成"}),"failed"===o.status&&(0,n.jsx)("div",{className:"progress-error",children:"❌ 操作失败"})]}),(0,n.jsxs)("div",{className:"batch-toolbar",children:[(0,n.jsx)("div",{className:"selection-info",children:(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:s.size===e.length&&e.length>0,onChange:()=>{s.size===e.length?r(new Set):r(new Set(e.map(e=>e.id).filter(e=>void 0!==e)))}}),"已选择 ",s.size," / ",e.length," 个标签页"]})}),(0,n.jsxs)("div",{className:"batch-actions",children:[(0,n.jsx)("button",{onClick:async()=>{if(0===s.size)return;const t=e.filter(e=>e.id&&s.has(e.id));confirm(`确定要关闭 ${t.length} 个标签页吗？`)&&(await p.closeTabs(t),r(new Set))},disabled:0===s.size,className:"btn btn-danger",children:"🗑️ 批量关闭"}),(0,n.jsx)("button",{onClick:()=>g(!0),disabled:0===s.size,className:"btn btn-secondary",children:"📌 批量固定"}),(0,n.jsx)("button",{onClick:()=>g(!1),disabled:0===s.size,className:"btn btn-secondary",children:"📌 取消固定"}),(0,n.jsx)("button",{onClick:async()=>{if(0===s.size)return;const e=Array.from(s);await p.refreshTabs(e)},disabled:0===s.size,className:"btn btn-secondary",children:"🔄 批量刷新"}),(0,n.jsx)("button",{onClick:async()=>{if(s.size<2)return void alert("请至少选择2个标签页来创建分组");const e=prompt("请输入分组名称:");if(e)try{const a=Array.from(s),o=await chrome.tabs.group({tabIds:a});await chrome.tabGroups.update(o,{title:e}),r(new Set),t(),alert(`成功创建分组 "${e}"`)}catch(e){console.error("Failed to create group:",e),alert("创建分组失败")}},disabled:s.size<2,className:"btn btn-primary",children:"📁 创建分组"})]})]}),(0,n.jsxs)("div",{className:"smart-tools",children:[(0,n.jsx)("h4",{children:"🤖 智能工具"}),(0,n.jsxs)("div",{className:"smart-actions",children:[(0,n.jsx)("button",{onClick:async()=>{confirm("确定要根据网站类型自动创建智能分组吗？")&&(await m.createSmartGroups()?(t(),alert("智能分组创建完成！")):alert("智能分组创建失败"))},className:"btn btn-primary",children:"✨ 智能分组"}),(0,n.jsx)("button",{onClick:async()=>{confirm("确定要根据域名自动分组吗？")&&(await u.autoGroupByDomain()?(t(),alert("域名分组创建完成！")):alert("域名分组创建失败"))},className:"btn btn-secondary",children:"🌐 域名分组"}),(0,n.jsx)("button",{onClick:()=>l(!c),className:"btn btn-secondary",children:"💡 查看建议"})]})]}),c&&(0,n.jsxs)("div",{className:"smart-suggestions",children:[(0,n.jsx)("h5",{children:"💡 智能建议"}),d.length>0&&(0,n.jsxs)("div",{className:"category-stats",children:[(0,n.jsx)("h6",{children:"📊 标签页分类统计"}),d.map(e=>(0,n.jsxs)("div",{className:"category-stat",children:[(0,n.jsx)("span",{className:"category-icon",children:e.category.icon}),(0,n.jsx)("span",{className:"category-name",children:e.category.name}),(0,n.jsxs)("span",{className:"category-count",children:[e.count," 个"]})]},e.category.id))]}),v.length>0&&(0,n.jsxs)("div",{className:"grouping-suggestions",children:[(0,n.jsx)("h6",{children:"📁 分组建议"}),v.slice(0,3).map((e,s)=>(0,n.jsxs)("div",{className:"suggestion-item",children:[(0,n.jsx)("span",{className:"suggestion-icon",children:e.category.icon}),(0,n.jsx)("span",{className:"suggestion-text",children:e.description}),(0,n.jsx)("button",{className:"btn btn-small btn-primary",onClick:async()=>{const s=e.tabs.map(e=>e.id).filter(e=>void 0!==e);if(s.length>1){const a=await chrome.tabs.group({tabIds:s});await chrome.tabGroups.update(a,{title:e.category.name,color:m.getChromColorFromHex(e.category.color)}),t()}},children:"创建"})]},s))]}),y.length>0&&(0,n.jsxs)("div",{className:"duplicate-detection",children:[(0,n.jsx)("h6",{children:"🔍 重复标签页检测"}),y.slice(0,3).map((e,t)=>(0,n.jsxs)("div",{className:"duplicate-item",children:[(0,n.jsxs)("span",{className:"duplicate-count",children:[e.tabs.length," 个重复"]}),(0,n.jsx)("span",{className:"duplicate-url",children:new URL(e.url).hostname}),(0,n.jsx)("button",{className:"btn btn-small btn-danger",onClick:async()=>{const t=e.tabs.slice(1);confirm(`确定要关闭 ${t.length} 个重复标签页吗？`)&&await p.closeTabs(t)},children:"清理"})]},t))]})]}),(0,n.jsx)("div",{className:"tab-list-with-selection",children:e.map(e=>(0,n.jsxs)("div",{className:"tab-item-selectable",children:[(0,n.jsx)("label",{className:"tab-checkbox",children:(0,n.jsx)("input",{type:"checkbox",checked:!!e.id&&s.has(e.id),onChange:t=>e.id&&((e,t)=>{const a=new Set(s);t?a.add(e):a.delete(e),r(a)})(e.id,t.target.checked)})}),(0,n.jsxs)("div",{className:"tab-info",children:[(0,n.jsx)("div",{className:"tab-favicon",children:e.favIconUrl?(0,n.jsx)("img",{src:e.favIconUrl,alt:""}):(0,n.jsx)("span",{children:"🌐"})}),(0,n.jsxs)("div",{className:"tab-details",children:[(0,n.jsx)("div",{className:"tab-title",children:e.title}),(0,n.jsx)("div",{className:"tab-url",children:e.url})]})]}),(0,n.jsxs)("div",{className:"tab-badges",children:[e.pinned&&(0,n.jsx)("span",{className:"badge badge-pin",children:"📌"}),e.active&&(0,n.jsx)("span",{className:"badge badge-active",children:"●"}),e.groupId&&(0,n.jsx)("span",{className:"badge badge-grouped",children:"📁"})]})]},e.id||e.url))})]})},y=[{id:"ai_primary",name:"AI工作主力",description:"ChatGPT、Gemini、LobeHub等主力AI工具",color:"#EF4444",icon:"🤖",type:"ai_primary",websites:[{name:"ChatGPT",url:"https://chat.openai.com/",description:"OpenAI的ChatGPT对话AI，最强大的通用AI助手",tags:["ai","chat","openai","gpt"],favicon:"https://chat.openai.com/favicon.ico"},{name:"Gemini",url:"https://gemini.google.com/",description:"Google的Gemini AI助手，支持多模态交互",tags:["ai","google","gemini","multimodal"],favicon:"https://www.google.com/favicon.ico"},{name:"LobeHub",url:"https://chat-preview.lobehub.com/discover",description:"LobeHub AI聊天平台，开源AI助手社区",tags:["ai","chat","lobehub","opensource"],favicon:"https://chat-preview.lobehub.com/favicon.ico"},{name:"Perplexity",url:"https://www.perplexity.ai/",description:"Perplexity AI搜索引擎，AI驱动的搜索体验",tags:["ai","search","perplexity","research"],favicon:"https://www.perplexity.ai/favicon.ico"},{name:"Grok",url:"https://grok.x.ai/",description:"xAI的Grok AI助手，马斯克团队开发",tags:["ai","grok","xai","elon"],favicon:"https://grok.x.ai/favicon.ico"},{name:"AI Studio",url:"https://aistudio.google.com/",description:"Google AI Studio开发平台，AI应用开发工具",tags:["ai","google","development","studio"],favicon:"https://aistudio.google.com/favicon.ico"}]},{id:"ai_secondary",name:"AI次选",description:"DeepAsk、GPTFun、Claude等次选AI工具",color:"#F97316",icon:"🔧",type:"ai_secondary",websites:[{name:"DeepAsk",url:"https://deepask.cc/",description:"DeepAsk AI问答平台，深度AI对话体验",tags:["ai","qa","deepask","chinese"],favicon:"https://deepask.cc/favicon.ico"},{name:"GPTFun",url:"https://fun4ai.khthink.cn/login",description:"GPTFun AI娱乐平台，有趣的AI互动体验",tags:["ai","fun","gpt","entertainment"],favicon:"https://fun4ai.khthink.cn/favicon.ico"},{name:"C佬",url:"https://new.clivia.fun/",description:"C佬AI助手，专业的AI对话工具",tags:["ai","assistant","chinese"],favicon:"https://new.clivia.fun/favicon.ico"},{name:"A佬",url:"https://aabao.eu.cc/",description:"A佬AI工具，实用的AI助手平台",tags:["ai","tools","chinese"],favicon:"https://aabao.eu.cc/favicon.ico"},{name:"H佬",url:"https://work.haomo.de/",description:"H佬工作助手，专注工作效率的AI工具",tags:["ai","work","productivity"],favicon:"https://work.haomo.de/favicon.ico"},{name:"Claude",url:"https://demo.fuclaude.com/",description:"Anthropic Claude AI，安全可靠的AI助手",tags:["ai","claude","anthropic","safety"],favicon:"https://demo.fuclaude.com/favicon.ico"}]},{id:"ai_tools",name:"AI其他工具",description:"Dify、提示词优化等辅助工具",color:"#8B5CF6",icon:"🛠️",type:"ai_tools",websites:[{name:"Dify",url:"https://dify.ai/",description:"Dify AI应用开发平台，快速构建AI应用",tags:["ai","development","dify","platform"],favicon:"https://dify.ai/favicon.ico"},{name:"提示词优化",url:"https://promptpilot.volcengine.com/home",description:"火山引擎提示词优化工具，提升AI对话效果",tags:["ai","prompt","optimization","volcengine"],favicon:"https://promptpilot.volcengine.com/favicon.ico"}]},{id:"tech_forums",name:"技术论坛",description:"Linux.do、NodeLoc等技术社区",color:"#06B6D4",icon:"💬",type:"tech_forums",websites:[{name:"Linux.do",url:"https://linux.do/",description:"Linux技术社区，开源技术讨论平台",tags:["linux","community","tech","opensource"],favicon:"https://linux.do/favicon.ico"},{name:"NodeLoc",url:"https://nodeloc.cc/",description:"NodeLoc技术论坛，服务器和网络技术交流",tags:["tech","forum","node","server"],favicon:"https://nodeloc.cc/favicon.ico"},{name:"NodeSeek",url:"https://www.nodeseek.com/",description:"NodeSeek技术分享，专业的技术讨论社区",tags:["tech","sharing","node","community"],favicon:"https://www.nodeseek.com/favicon.ico"},{name:"小众软件",url:"https://meta.appinn.net/latest",description:"小众软件分享社区，发现有趣的软件工具",tags:["software","tools","community","apps"],favicon:"https://meta.appinn.net/favicon.ico"},{name:"Follow",url:"https://app.follow.is/",description:"Follow信息聚合工具，RSS和信息流管理",tags:["rss","news","aggregation","follow"],favicon:"https://app.follow.is/favicon.ico"}]},{id:"collaboration",name:"协作工具",description:"语雀、飞书等协作平台",color:"#EC4899",icon:"👥",type:"collaboration",websites:[{name:"语雀",url:"https://www.yuque.com/",description:"阿里巴巴语雀知识库，专业的文档协作平台",tags:["docs","knowledge","collaboration","alibaba"],favicon:"https://www.yuque.com/favicon.ico"},{name:"飞书",url:"https://p1b9rnchwd.feishu.cn/drive/home/",description:"字节跳动飞书协作平台，一站式办公解决方案",tags:["collaboration","office","feishu","bytedance"],favicon:"https://www.feishu.cn/favicon.ico"}]}],v=e=>y.find(t=>t.type===e),w=e=>y.flatMap(e=>e.websites).find(t=>{try{const s=new URL(t.url).hostname,a=new URL(e).hostname;return a.includes(s)||s.includes(a)}catch{return!1}});class f{static async initializePresetGroups(e){try{const t=await i.StorageManager.getWorkspace(e);if(!t)return console.error("Workspace not found:",e),!1;if(t.groups.some(e=>e.isPreset))return console.log("Preset groups already initialized for workspace:",e),!0;const s=y.map((e,t)=>({id:`preset_${e.id}`,name:e.name,color:e.color,icon:e.icon,tabs:[],isPreset:!0,presetType:e.type,sortOrder:t}));return t.groups.push(...s),t.updatedAt=(new Date).toISOString(),await i.StorageManager.saveWorkspace(t),console.log(`Initialized ${s.length} preset groups for workspace:`,e),!0}catch(e){return console.error("Failed to initialize preset groups:",e),!1}}static async addPresetWebsitesToGroup(e,t){try{const s=await i.StorageManager.getWorkspace(e);if(!s)return console.error("Workspace not found:",e),!1;const a=v(t);if(!a)return console.error("Preset group not found:",t),!1;const r=s.groups.find(e=>e.isPreset&&e.presetType===t);if(!r)return console.error("Target group not found in workspace:",t),!1;const o=a.websites.map(e=>({title:e.name,url:e.url,favIconUrl:e.favicon})),n=new Set(r.tabs.map(e=>e.url)),c=o.filter(e=>!n.has(e.url));return 0===c.length?(console.log("All preset websites already exist in group:",t),!0):(r.tabs.push(...c),s.updatedAt=(new Date).toISOString(),await i.StorageManager.saveWorkspace(s),console.log(`Added ${c.length} preset websites to group:`,t),!0)}catch(e){return console.error("Failed to add preset websites to group:",e),!1}}static async openPresetGroup(e){try{const t=v(e);if(!t)return console.error("Preset group not found:",e),!1;const s=t.websites.map(e=>e.url);for(const e of s)await o.createTab(e,!1),await new Promise(e=>setTimeout(e,100));return console.log(`Opened ${s.length} websites from preset group:`,e),!0}catch(e){return console.error("Failed to open preset group:",e),!1}}static async detectPresetWebsitesInCurrentTabs(){try{const e=await o.getCurrentTabs(),t={};for(const s of e)if(s.url){const e=w(s.url);if(e){const a=y.find(t=>t.websites.some(t=>t.url===e.url));a&&(t[a.type]||(t[a.type]=[]),t[a.type].push(s))}}return t}catch(e){return console.error("Failed to detect preset websites:",e),{}}}static async autoAddDetectedWebsitesToGroups(e){try{const t=await this.detectPresetWebsitesInCurrentTabs();let s=0;for(const[a,r]of Object.entries(t)){const t=await i.StorageManager.getWorkspace(e);if(!t)continue;const o=t.groups.find(e=>e.isPreset&&e.presetType===a);if(o){const e=new Set(o.tabs.map(e=>e.url)),t=r.filter(t=>!e.has(t.url));t.length>0&&(o.tabs.push(...t),s+=t.length)}s>0&&(t.updatedAt=(new Date).toISOString(),await i.StorageManager.saveWorkspace(t))}return console.log(`Auto-added ${s} detected websites to preset groups`),s>0}catch(e){return console.error("Failed to auto-add detected websites:",e),!1}}static async getPresetGroupUsageStats(e){try{const t=await i.StorageManager.getWorkspace(e);if(!t)return{};const s={};for(const e of y){const a=t.groups.find(t=>t.isPreset&&t.presetType===e.type),r=e.websites.length,o=a?a.tabs.length:0,i=r>0?o/r*100:0;s[e.type]={totalWebsites:r,addedWebsites:o,usagePercentage:i}}return s}catch(e){return console.error("Failed to get preset group usage stats:",e),{}}}static async resetPresetGroup(e,t){try{const s=await i.StorageManager.getWorkspace(e);if(!s)return console.error("Workspace not found:",e),!1;const a=s.groups.find(e=>e.isPreset&&e.presetType===t);return a?(a.tabs=[],s.updatedAt=(new Date).toISOString(),await i.StorageManager.saveWorkspace(s),console.log("Reset preset group:",t),!0):(console.error("Target group not found in workspace:",t),!1)}catch(e){return console.error("Failed to reset preset group:",e),!1}}static async getRecommendedPresetWebsites(){try{const e=Date.now()-6048e5,t=await chrome.history.search({text:"",startTime:e,maxResults:1e3}),s={};for(const e of t)if(e.url){const t=w(e.url);if(t){const e=y.find(e=>e.websites.some(e=>e.url===t.url));e&&(s[e.type]||(s[e.type]=[]),s[e.type].includes(t.url)||s[e.type].push(t.url))}}return s}catch(e){return console.error("Failed to get recommended preset websites:",e),{}}}static exportPresetGroupsConfig(){return JSON.stringify(y,null,2)}static getAllPresetGroups(){return y}}const b=({activeWorkspace:e,onWorkspaceChange:t})=>{const[s,r]=(0,a.useState)({}),[o,i]=(0,a.useState)({}),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)({});(0,a.useEffect)(()=>{e&&u()},[e]);const u=async()=>{if(e)try{l(!0);const t=await f.detectPresetWebsitesInCurrentTabs();r(t);const s=await f.getPresetGroupUsageStats(e);i(s);const a=await f.getRecommendedPresetWebsites();h(a)}catch(e){console.error("Failed to load preset groups data:",e)}finally{l(!1)}},p=async s=>{if(e)try{l(!0),await f.addPresetWebsitesToGroup(e,s)?(alert("预设网站添加成功！"),t(),await u()):alert("预设网站添加失败")}catch(e){console.error("Failed to add preset websites:",e),alert("预设网站添加失败")}finally{l(!1)}else alert("请先选择一个工作空间")};return c?(0,n.jsx)("div",{className:"preset-groups-panel",children:(0,n.jsxs)("div",{className:"loading-state",children:[(0,n.jsx)("div",{className:"spinner"}),(0,n.jsx)("p",{children:"加载中..."})]})}):(0,n.jsxs)("div",{className:"preset-groups-panel",children:[(0,n.jsxs)("div",{className:"panel-header",children:[(0,n.jsx)("h3",{children:"🚀 预设AI工具分组"}),(0,n.jsx)("p",{children:"快速访问和管理26个精选AI工具网站"})]}),(0,n.jsxs)("div",{className:"quick-actions",children:[(0,n.jsx)("button",{onClick:async()=>{if(e)try{l(!0),await f.initializePresetGroups(e)?(alert("预设分组初始化成功！"),t(),await u()):alert("预设分组初始化失败")}catch(e){console.error("Failed to initialize preset groups:",e),alert("预设分组初始化失败")}finally{l(!1)}else alert("请先选择一个工作空间")},className:"btn btn-primary",disabled:!e,children:"🎯 初始化预设分组"}),Object.keys(s).length>0&&(0,n.jsxs)("button",{onClick:async()=>{if(e)try{l(!0),await f.autoAddDetectedWebsitesToGroups(e)?(alert("检测到的网站已自动添加到对应分组！"),t(),await u()):alert("没有检测到新的预设网站")}catch(e){console.error("Failed to auto-add detected websites:",e),alert("自动添加失败")}finally{l(!1)}else alert("请先选择一个工作空间")},className:"btn btn-success",children:["✨ 自动添加检测到的网站 (",Object.values(s).flat().length,")"]})]}),Object.keys(s).length>0&&(0,n.jsxs)("div",{className:"detected-websites",children:[(0,n.jsx)("h4",{children:"🔍 检测到的预设网站"}),Object.entries(s).map(([e,t])=>{const s=y.find(t=>t.type===e);return(0,n.jsxs)("div",{className:"detected-group",children:[(0,n.jsx)("span",{className:"group-icon",children:null==s?void 0:s.icon}),(0,n.jsx)("span",{className:"group-name",children:null==s?void 0:s.name}),(0,n.jsxs)("span",{className:"detected-count",children:[t.length," 个"]}),(0,n.jsx)("button",{onClick:()=>p(e),className:"btn btn-small btn-primary",children:"添加到分组"})]},e)})]}),(0,n.jsxs)("div",{className:"preset-groups-list",children:[(0,n.jsx)("h4",{children:"📁 预设分组管理"}),y.map(s=>{var a;const r=o[s.type],i=(null===(a=d[s.type])||void 0===a?void 0:a.length)>0;return(0,n.jsxs)("div",{className:"preset-group-item",children:[(0,n.jsxs)("div",{className:"group-header",children:[(0,n.jsxs)("div",{className:"group-info",children:[(0,n.jsx)("span",{className:"group-icon",style:{color:s.color},children:s.icon}),(0,n.jsxs)("div",{className:"group-details",children:[(0,n.jsx)("h5",{children:s.name}),(0,n.jsx)("p",{children:s.description}),(0,n.jsxs)("div",{className:"group-stats",children:[(0,n.jsxs)("span",{children:["总计: ",s.websites.length," 个网站"]}),r&&(0,n.jsxs)("span",{children:["已添加: ",r.addedWebsites," 个 (",r.usagePercentage.toFixed(0),"%)"]})]})]})]}),(0,n.jsxs)("div",{className:"group-actions",children:[(0,n.jsx)("button",{onClick:()=>p(s.type),className:"btn btn-small btn-primary",disabled:!e,title:"添加所有预设网站到工作空间",children:"➕ 添加"}),(0,n.jsx)("button",{onClick:()=>(async e=>{try{l(!0),await f.openPresetGroup(e)?alert("预设分组已打开！"):alert("打开预设分组失败")}catch(e){console.error("Failed to open preset group:",e),alert("打开预设分组失败")}finally{l(!1)}})(s.type),className:"btn btn-small btn-secondary",title:"在新标签页中打开所有网站",children:"🚀 打开"}),(0,n.jsx)("button",{onClick:()=>(async s=>{var a;if(!e)return;const r=null===(a=y.find(e=>e.type===s))||void 0===a?void 0:a.name;if(confirm(`确定要重置 "${r}" 分组吗？这将清空该分组的所有标签页。`))try{l(!0),await f.resetPresetGroup(e,s)?(alert("分组已重置！"),t(),await u()):alert("重置分组失败")}catch(e){console.error("Failed to reset preset group:",e),alert("重置分组失败")}finally{l(!1)}})(s.type),className:"btn btn-small btn-danger",disabled:!e,title:"重置分组（清空所有标签页）",children:"🔄 重置"})]})]}),(0,n.jsxs)("div",{className:"websites-preview",children:[s.websites.slice(0,3).map(e=>(0,n.jsxs)("div",{className:"website-item",children:[(0,n.jsx)("img",{src:e.favicon,alt:e.name,className:"website-favicon",onError:e=>{e.target.style.display="none"}}),(0,n.jsx)("span",{className:"website-name",children:e.name})]},e.url)),s.websites.length>3&&(0,n.jsxs)("span",{className:"more-websites",children:["+",s.websites.length-3," 更多..."]})]}),i&&(0,n.jsx)("div",{className:"recommendation-badge",children:"💡 基于您的浏览历史推荐"})]},s.id)})]}),(0,n.jsxs)("div",{className:"usage-tips",children:[(0,n.jsx)("h4",{children:"💡 使用提示"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:'首次使用请点击"初始化预设分组"创建分组结构'}),(0,n.jsx)("li",{children:'点击"添加"将预设网站添加到当前工作空间'}),(0,n.jsx)("li",{children:'点击"打开"在新标签页中批量打开所有网站'}),(0,n.jsx)("li",{children:"系统会自动检测您当前打开的预设网站"}),(0,n.jsx)("li",{children:"可以随时重置分组来清空不需要的标签页"})]})]})]})};class x{static STORAGE_KEY="favorites";static QUICK_ACCESS_KEY="quickAccessFavorites";static async addFavorite(e,t="默认",s=!1){try{const a=await this.getAllFavorites();if(a.find(t=>t.url===e.url))throw new Error("This page is already in favorites");const r={id:`fav_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,userId:"local",title:e.title,url:e.url,faviconUrl:e.favIconUrl,description:"",tags:[],category:t,isQuickAccess:s,visitCount:0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};return a.push(r),await i.StorageManager.setItem(this.STORAGE_KEY,a),s&&await this.updateQuickAccessList(),console.log("Favorite added successfully:",r.title),r}catch(e){throw console.error("Failed to add favorite:",e),e}}static async removeFavorite(e){try{const t=await this.getAllFavorites(),s=t.findIndex(t=>t.id===e);if(-1===s)throw new Error("Favorite not found");const a=t[s];return t.splice(s,1),await i.StorageManager.setItem(this.STORAGE_KEY,t),a.isQuickAccess&&await this.updateQuickAccessList(),console.log("Favorite removed successfully:",a.title),!0}catch(e){throw console.error("Failed to remove favorite:",e),e}}static async removeFavoriteByUrl(e){try{const t=(await this.getAllFavorites()).find(t=>t.url===e);return!!t&&await this.removeFavorite(t.id)}catch(e){throw console.error("Failed to remove favorite by URL:",e),e}}static async updateFavorite(e,t){try{const s=await this.getAllFavorites(),a=s.findIndex(t=>t.id===e);if(-1===a)throw new Error("Favorite not found");const r={...s[a],...t,updatedAt:(new Date).toISOString()};return s[a]=r,await i.StorageManager.setItem(this.STORAGE_KEY,s),"isQuickAccess"in t&&await this.updateQuickAccessList(),console.log("Favorite updated successfully:",r.title),r}catch(e){throw console.error("Failed to update favorite:",e),e}}static async getAllFavorites(){try{return await i.StorageManager.getItem(this.STORAGE_KEY)||[]}catch(e){return console.error("Failed to get favorites:",e),[]}}static async getFavoritesByCategory(e){try{return(await this.getAllFavorites()).filter(t=>t.category===e)}catch(e){return console.error("Failed to get favorites by category:",e),[]}}static async getQuickAccessFavorites(){try{return await i.StorageManager.getItem(this.QUICK_ACCESS_KEY)||[]}catch(e){return console.error("Failed to get quick access favorites:",e),[]}}static async updateQuickAccessList(){try{const e=(await this.getAllFavorites()).filter(e=>e.isQuickAccess).sort((e,t)=>t.visitCount-e.visitCount).slice(0,10);await i.StorageManager.setItem(this.QUICK_ACCESS_KEY,e)}catch(e){console.error("Failed to update quick access list:",e)}}static async recordVisit(e){try{const t=await this.getAllFavorites(),s=t.findIndex(t=>t.id===e);-1!==s&&(t[s].visitCount+=1,t[s].lastVisitedAt=(new Date).toISOString(),t[s].updatedAt=(new Date).toISOString(),await i.StorageManager.setItem(this.STORAGE_KEY,t),t[s].isQuickAccess&&await this.updateQuickAccessList())}catch(e){console.error("Failed to record visit:",e)}}static async isFavorited(e){try{return(await this.getAllFavorites()).some(t=>t.url===e)}catch(e){return console.error("Failed to check if favorited:",e),!1}}static async getCategories(){try{const e=await this.getAllFavorites(),t=new Set(e.map(e=>e.category));return Array.from(t).sort()}catch(e){return console.error("Failed to get categories:",e),[]}}static async searchFavorites(e){try{const t=await this.getAllFavorites(),s=e.toLowerCase();return t.filter(e=>{var t;return e.title.toLowerCase().includes(s)||e.url.toLowerCase().includes(s)||(null===(t=e.description)||void 0===t?void 0:t.toLowerCase().includes(s))||e.tags.some(e=>e.toLowerCase().includes(s))||e.category.toLowerCase().includes(s)}).sort((e,t)=>{const a=e.title.toLowerCase().includes(s),r=t.title.toLowerCase().includes(s);return a&&!r?-1:!a&&r?1:t.visitCount-e.visitCount})}catch(e){return console.error("Failed to search favorites:",e),[]}}static async getPopularFavorites(e=10){try{return(await this.getAllFavorites()).sort((e,t)=>t.visitCount-e.visitCount).slice(0,e)}catch(e){return console.error("Failed to get popular favorites:",e),[]}}static async getRecentFavorites(e=10){try{return(await this.getAllFavorites()).sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime()).slice(0,e)}catch(e){return console.error("Failed to get recent favorites:",e),[]}}static async exportFavorites(){try{const e=await this.getAllFavorites();return JSON.stringify(e,null,2)}catch(e){throw console.error("Failed to export favorites:",e),e}}static async importFavorites(e,t="merge"){try{const s=JSON.parse(e);if(!Array.isArray(s))throw new Error("Invalid favorites format");let a="replace"===t?[]:await this.getAllFavorites(),r=0;for(const e of s)if(!a.some(t=>t.url===e.url)){const t={...e,id:`fav_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};a.push(t),r++}return await i.StorageManager.setItem(this.STORAGE_KEY,a),await this.updateQuickAccessList(),console.log(`Imported ${r} favorites successfully`),r}catch(e){throw console.error("Failed to import favorites:",e),e}}static async clearAllFavorites(){try{await i.StorageManager.setItem(this.STORAGE_KEY,[]),await i.StorageManager.setItem(this.QUICK_ACCESS_KEY,[]),console.log("All favorites cleared successfully")}catch(e){throw console.error("Failed to clear favorites:",e),e}}}const j=({onFavoritesChange:e})=>{const[t,s]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[c,l]=(0,a.useState)([]),[d,h]=(0,a.useState)("all"),[u,p]=(0,a.useState)(""),[m,g]=(0,a.useState)(!1),[y,v]=(0,a.useState)(!1),[w,f]=(0,a.useState)("");(0,a.useEffect)(()=>{b()},[d,u]);const b=async()=>{try{g(!0);let e=[];e=u?await x.searchFavorites(u):"all"===d?await x.getAllFavorites():await x.getFavoritesByCategory(d),s(e);const t=await x.getQuickAccessFavorites();i(t);const a=await x.getCategories();l(a)}catch(e){console.error("Failed to load favorites:",e)}finally{g(!1)}},j=async e=>{try{await o.createTab(e.url,!0),await x.recordVisit(e.id),await b()}catch(e){console.error("Failed to open favorite:",e)}};return(0,n.jsxs)("div",{className:"favorites-panel",children:[(0,n.jsxs)("div",{className:"panel-header",children:[(0,n.jsx)("h3",{children:"⭐ 收藏管理"}),(0,n.jsxs)("div",{className:"header-actions",children:[(0,n.jsx)("button",{onClick:()=>v(!y),className:"btn btn-primary btn-small",children:"➕ 添加当前页面"}),(0,n.jsx)("button",{onClick:async()=>{try{const e=await x.exportFavorites(),t=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`favorites_${(new Date).toISOString().split("T")[0]}.json`,a.click(),URL.revokeObjectURL(s)}catch(e){console.error("Failed to export favorites:",e),alert("导出收藏失败")}},className:"btn btn-secondary btn-small",children:"📤 导出"}),(0,n.jsxs)("label",{className:"btn btn-secondary btn-small",children:["📥 导入",(0,n.jsx)("input",{type:"file",accept:".json",onChange:async t=>{var s;const a=null===(s=t.target.files)||void 0===s?void 0:s[0];if(a){try{const t=await a.text(),s=await x.importFavorites(t,"merge");await b(),null==e||e(),alert(`成功导入 ${s} 个收藏！`)}catch(e){console.error("Failed to import favorites:",e),alert("导入收藏失败："+e.message)}t.target.value=""}},style:{display:"none"}})]})]})]}),y&&(0,n.jsxs)("div",{className:"add-favorite-form",children:[(0,n.jsx)("input",{type:"text",placeholder:"分类名称（可选）",value:w,onChange:e=>f(e.target.value),className:"category-input"}),(0,n.jsxs)("div",{className:"form-actions",children:[(0,n.jsx)("button",{onClick:async()=>{try{const t=await o.getCurrentActiveTab();if(!t)return void alert("无法获取当前标签页");const s=w||"默认";await x.addFavorite(t,s),v(!1),f(""),await b(),null==e||e(),alert("已添加到收藏！")}catch(e){console.error("Failed to add favorite:",e),alert("添加收藏失败："+e.message)}},className:"btn btn-primary btn-small",children:"确认添加"}),(0,n.jsx)("button",{onClick:()=>v(!1),className:"btn btn-secondary btn-small",children:"取消"})]})]}),r.length>0&&(0,n.jsxs)("div",{className:"quick-access-section",children:[(0,n.jsx)("h4",{children:"🚀 快速访问"}),(0,n.jsx)("div",{className:"quick-access-grid",children:r.map(e=>(0,n.jsxs)("div",{className:"quick-access-item",onClick:()=>j(e),children:[(0,n.jsx)("img",{src:e.faviconUrl,alt:e.title,className:"quick-favicon",onError:e=>{e.target.style.display="none"}}),(0,n.jsx)("span",{className:"quick-title",children:e.title}),(0,n.jsx)("span",{className:"visit-count",children:e.visitCount})]},e.id))})]}),(0,n.jsxs)("div",{className:"search-filter-section",children:[(0,n.jsx)("input",{type:"text",placeholder:"搜索收藏...",value:u,onChange:e=>p(e.target.value),className:"search-input"}),(0,n.jsxs)("select",{value:d,onChange:e=>h(e.target.value),className:"category-filter",children:[(0,n.jsx)("option",{value:"all",children:"所有分类"}),c.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})]}),(0,n.jsx)("div",{className:"favorites-list",children:m?(0,n.jsx)("div",{className:"loading-state",children:"加载中..."}):0===t.length?(0,n.jsx)("div",{className:"empty-state",children:u?"没有找到匹配的收藏":"还没有收藏任何页面"}):t.map(t=>(0,n.jsxs)("div",{className:"favorite-item",children:[(0,n.jsxs)("div",{className:"favorite-info",onClick:()=>j(t),children:[(0,n.jsx)("img",{src:t.faviconUrl,alt:t.title,className:"favorite-favicon",onError:e=>{e.target.style.display="none"}}),(0,n.jsxs)("div",{className:"favorite-details",children:[(0,n.jsx)("div",{className:"favorite-title",children:t.title}),(0,n.jsx)("div",{className:"favorite-url",children:t.url}),(0,n.jsxs)("div",{className:"favorite-meta",children:[(0,n.jsx)("span",{className:"category-tag",children:t.category}),(0,n.jsxs)("span",{className:"visit-count",children:["访问 ",t.visitCount," 次"]}),t.lastVisitedAt&&(0,n.jsxs)("span",{className:"last-visited",children:["最后访问: ",new Date(t.lastVisitedAt).toLocaleDateString()]})]})]})]}),(0,n.jsxs)("div",{className:"favorite-actions",children:[(0,n.jsx)("button",{onClick:()=>(async t=>{try{await x.updateFavorite(t.id,{isQuickAccess:!t.isQuickAccess}),await b(),null==e||e()}catch(e){console.error("Failed to toggle quick access:",e),alert("更新快速访问失败")}})(t),className:"btn btn-small "+(t.isQuickAccess?"btn-primary":"btn-secondary"),title:t.isQuickAccess?"移出快速访问":"添加到快速访问",children:t.isQuickAccess?"⭐":"☆"}),(0,n.jsx)("button",{onClick:()=>(async t=>{if(confirm("确定要移除这个收藏吗？"))try{await x.removeFavorite(t),await b(),null==e||e()}catch(e){console.error("Failed to remove favorite:",e),alert("移除收藏失败")}})(t.id),className:"btn btn-small btn-danger",title:"移除收藏",children:"🗑️"})]})]},t.id))}),(0,n.jsxs)("div",{className:"favorites-stats",children:[(0,n.jsxs)("div",{className:"stat-item",children:[(0,n.jsx)("span",{className:"stat-label",children:"总收藏:"}),(0,n.jsx)("span",{className:"stat-value",children:t.length})]}),(0,n.jsxs)("div",{className:"stat-item",children:[(0,n.jsx)("span",{className:"stat-label",children:"分类:"}),(0,n.jsx)("span",{className:"stat-value",children:c.length})]}),(0,n.jsxs)("div",{className:"stat-item",children:[(0,n.jsx)("span",{className:"stat-label",children:"快速访问:"}),(0,n.jsx)("span",{className:"stat-value",children:r.length})]})]})]})};class S{static SEARCH_HISTORY_KEY="searchHistory";static MAX_HISTORY_SIZE=50;static async globalSearch(e){try{const t=[],{query:s,filters:a,sortBy:r,sortOrder:o,limit:i,offset:n}=e;if(!a.type||"all"===a.type||"tab"===a.type){const e=await this.searchTabs(s,a);t.push(...e)}if(!a.type||"all"===a.type||"favorite"===a.type){const e=await this.searchFavorites(s,a);t.push(...e)}if(!a.type||"all"===a.type||"workspace"===a.type){const e=await this.searchWorkspaces(s,a);t.push(...e)}if(!a.type||"all"===a.type||"group"===a.type){const e=await this.searchGroups(s,a);t.push(...e)}const c=this.sortResults(t,r,o).slice(n,n+i);return await this.addToSearchHistory(s),c}catch(e){return console.error("Failed to perform global search:",e),[]}}static async searchTabs(e,t){try{const t=await o.getCurrentTabs(),s=e.toLowerCase(),a=[];for(const e of t){const t=this.calculateTabRelevance(e,s);if(t>0){const r=this.getMatchedFields(e,s);a.push({id:`tab_${e.id||e.url}`,type:"tab",title:e.title,url:e.url,faviconUrl:e.favIconUrl,relevanceScore:t,matchedFields:r})}}return a}catch(e){return console.error("Failed to search tabs:",e),[]}}static async searchFavorites(e,t){try{const s=await x.searchFavorites(e),a=[];for(const r of s){if(t.category&&r.category!==t.category)continue;if(t.tags&&t.tags.length>0&&!t.tags.some(e=>r.tags.some(t=>t.toLowerCase().includes(e.toLowerCase()))))continue;const s=this.calculateFavoriteRelevance(r,e.toLowerCase()),o=this.getMatchedFieldsForFavorite(r,e.toLowerCase());a.push({id:`favorite_${r.id}`,type:"favorite",title:r.title,url:r.url,description:r.description,faviconUrl:r.faviconUrl,relevanceScore:s,matchedFields:o})}return a}catch(e){return console.error("Failed to search favorites:",e),[]}}static async searchWorkspaces(e,t){try{const t=await i.StorageManager.getAllWorkspaces(),s=e.toLowerCase(),a=[];for(const e of t){const t=this.calculateWorkspaceRelevance(e,s);if(t>0){const r=this.getMatchedFieldsForWorkspace(e,s);a.push({id:`workspace_${e.id}`,type:"workspace",title:e.name,description:e.description,relevanceScore:t,matchedFields:r})}}return a}catch(e){return console.error("Failed to search workspaces:",e),[]}}static async searchGroups(e,t){try{const s=await i.StorageManager.getAllWorkspaces(),a=e.toLowerCase(),r=[];for(const e of s)if(!t.workspace||e.id===t.workspace)for(const t of e.groups){const s=this.calculateGroupRelevance(t,a);if(s>0){const o=this.getMatchedFieldsForGroup(t,a);r.push({id:`group_${t.id}`,type:"group",title:t.name,description:`${e.name} > ${t.name}`,workspaceName:e.name,groupName:t.name,relevanceScore:s,matchedFields:o})}}return r}catch(e){return console.error("Failed to search groups:",e),[]}}static calculateTabRelevance(e,t){let s=0;return e.title.toLowerCase().includes(t)&&(s+=10,e.title.toLowerCase().startsWith(t)&&(s+=5)),e.url.toLowerCase().includes(t)&&(s+=5),s}static calculateFavoriteRelevance(e,t){var s;let a=0;return e.title.toLowerCase().includes(t)&&(a+=10,e.title.toLowerCase().startsWith(t)&&(a+=5)),e.url.toLowerCase().includes(t)&&(a+=5),null!==(s=e.description)&&void 0!==s&&s.toLowerCase().includes(t)&&(a+=3),e.tags.some(e=>e.toLowerCase().includes(t))&&(a+=4),e.category.toLowerCase().includes(t)&&(a+=2),a+=Math.min(.1*e.visitCount,5),a}static calculateWorkspaceRelevance(e,t){var s;let a=0;return e.name.toLowerCase().includes(t)&&(a+=10,e.name.toLowerCase().startsWith(t)&&(a+=5)),null!==(s=e.description)&&void 0!==s&&s.toLowerCase().includes(t)&&(a+=5),a}static calculateGroupRelevance(e,t){let s=0;return e.name.toLowerCase().includes(t)&&(s+=10,e.name.toLowerCase().startsWith(t)&&(s+=5)),s}static getMatchedFields(e,t){const s=[];return e.title.toLowerCase().includes(t)&&s.push("title"),e.url.toLowerCase().includes(t)&&s.push("url"),s}static getMatchedFieldsForFavorite(e,t){var s;const a=[];return e.title.toLowerCase().includes(t)&&a.push("title"),e.url.toLowerCase().includes(t)&&a.push("url"),null!==(s=e.description)&&void 0!==s&&s.toLowerCase().includes(t)&&a.push("description"),e.tags.some(e=>e.toLowerCase().includes(t))&&a.push("tags"),e.category.toLowerCase().includes(t)&&a.push("category"),a}static getMatchedFieldsForWorkspace(e,t){var s;const a=[];return e.name.toLowerCase().includes(t)&&a.push("name"),null!==(s=e.description)&&void 0!==s&&s.toLowerCase().includes(t)&&a.push("description"),a}static getMatchedFieldsForGroup(e,t){const s=[];return e.name.toLowerCase().includes(t)&&s.push("name"),s}static sortResults(e,t,s){return e.sort((e,a)=>{let r=0;switch(t){case"relevance":case"date":default:r=a.relevanceScore-e.relevanceScore;break;case"alphabetical":r=e.title.localeCompare(a.title)}return"desc"===s?r:-r})}static async addToSearchHistory(e){try{if(!e.trim())return;const t=(await this.getSearchHistory()).filter(t=>t!==e);t.unshift(e);const s=t.slice(0,this.MAX_HISTORY_SIZE);await i.StorageManager.setItem(this.SEARCH_HISTORY_KEY,s)}catch(e){console.error("Failed to add to search history:",e)}}static async getSearchHistory(){try{return await i.StorageManager.getItem(this.SEARCH_HISTORY_KEY)||[]}catch(e){return console.error("Failed to get search history:",e),[]}}static async clearSearchHistory(){try{await i.StorageManager.setItem(this.SEARCH_HISTORY_KEY,[])}catch(e){console.error("Failed to clear search history:",e)}}static async getSearchSuggestions(e,t=5){try{const s=await this.getSearchHistory(),a=e.toLowerCase();return s.filter(e=>e.toLowerCase().includes(a)).slice(0,t)}catch(e){return console.error("Failed to get search suggestions:",e),[]}}}const N=({onResultSelect:e})=>{const[t,s]=(0,a.useState)(""),[r,i]=(0,a.useState)([]),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)([]),[u,p]=(0,a.useState)([]),[m,g]=(0,a.useState)(!1),[y,v]=(0,a.useState)({type:"all"}),[w,f]=(0,a.useState)("relevance"),[b,j]=(0,a.useState)("desc"),N=(0,a.useRef)(null),k=(0,a.useRef)();(0,a.useEffect)(()=>{F()},[]),(0,a.useEffect)(()=>(k.current&&clearTimeout(k.current),t.trim()?k.current=setTimeout(()=>{T()},300):(i([]),g(!1)),()=>{k.current&&clearTimeout(k.current)}),[t,y,w,b]),(0,a.useEffect)(()=>{t.length>0?C():(p([]),g(!1))},[t]);const F=async()=>{try{const e=await S.getSearchHistory();h(e)}catch(e){console.error("Failed to load search history:",e)}},C=async()=>{try{const e=await S.getSearchSuggestions(t,5);p(e),g(e.length>0)}catch(e){console.error("Failed to get suggestions:",e)}},T=async()=>{if(t.trim())try{l(!0);const e={query:t.trim(),filters:y,sortBy:w,sortOrder:b,limit:50,offset:0},s=await S.globalSearch(e);i(s),g(!1)}catch(e){console.error("Failed to perform search:",e),i([])}finally{l(!1)}},A=e=>{s(e)},I=e=>{switch(e){case"tab":return"📄";case"favorite":return"⭐";case"workspace":return"📁";case"group":return"📂";default:return"🔍"}},E=e=>{switch(e){case"tab":return"标签页";case"favorite":return"收藏";case"workspace":return"工作空间";case"group":return"分组";default:return"未知"}};return(0,n.jsxs)("div",{className:"search-panel",children:[(0,n.jsx)("div",{className:"search-header",children:(0,n.jsx)("h3",{children:"🔍 全局搜索"})}),(0,n.jsxs)("div",{className:"search-input-container",children:[(0,n.jsx)("input",{ref:N,type:"text",placeholder:"搜索标签页、收藏、工作空间...",value:t,onChange:e=>A(e.target.value),className:"search-input",onFocus:()=>g(u.length>0)}),m&&(0,n.jsx)("div",{className:"search-suggestions",children:u.map((e,t)=>(0,n.jsxs)("div",{className:"suggestion-item",onClick:()=>(e=>{var t;s(e),g(!1),null===(t=N.current)||void 0===t||t.focus()})(e),children:[(0,n.jsx)("span",{className:"suggestion-icon",children:"🕐"}),(0,n.jsx)("span",{className:"suggestion-text",children:e})]},t))})]}),(0,n.jsxs)("div",{className:"search-filters",children:[(0,n.jsxs)("select",{value:y.type||"all",onChange:e=>v({...y,type:e.target.value}),className:"filter-select",children:[(0,n.jsx)("option",{value:"all",children:"所有类型"}),(0,n.jsx)("option",{value:"tab",children:"标签页"}),(0,n.jsx)("option",{value:"favorite",children:"收藏"}),(0,n.jsx)("option",{value:"workspace",children:"工作空间"}),(0,n.jsx)("option",{value:"group",children:"分组"})]}),(0,n.jsxs)("select",{value:w,onChange:e=>f(e.target.value),className:"sort-select",children:[(0,n.jsx)("option",{value:"relevance",children:"相关性"}),(0,n.jsx)("option",{value:"alphabetical",children:"字母顺序"}),(0,n.jsx)("option",{value:"date",children:"日期"}),(0,n.jsx)("option",{value:"visits",children:"访问次数"})]}),(0,n.jsx)("button",{onClick:()=>j("asc"===b?"desc":"asc"),className:"sort-order-btn",title:"当前: "+("asc"===b?"升序":"降序"),children:"asc"===b?"⬆️":"⬇️"})]}),(0,n.jsx)("div",{className:"search-results",children:c?(0,n.jsxs)("div",{className:"loading-state",children:[(0,n.jsx)("div",{className:"spinner"}),(0,n.jsx)("p",{children:"搜索中..."})]}):t&&0===r.length?(0,n.jsxs)("div",{className:"empty-state",children:[(0,n.jsx)("p",{children:"没有找到匹配的结果"}),(0,n.jsx)("p",{className:"empty-hint",children:"尝试使用不同的关键词或调整过滤条件"})]}):r.map(t=>(0,n.jsxs)("div",{className:"search-result-item",onClick:()=>(async t=>{try{if(t.url&&(await o.createTab(t.url,!0),"favorite"===t.type)){const e=t.id.replace("favorite_","");await x.recordVisit(e)}null==e||e(t)}catch(e){console.error("Failed to handle result click:",e)}})(t),children:[(0,n.jsx)("div",{className:"result-icon",children:t.faviconUrl?(0,n.jsx)("img",{src:t.faviconUrl,alt:"",className:"result-favicon",onError:e=>{e.target.style.display="none"}}):(0,n.jsx)("span",{className:"result-type-icon",children:I(t.type)})}),(0,n.jsxs)("div",{className:"result-content",children:[(0,n.jsx)("div",{className:"result-title",children:t.title}),t.url&&(0,n.jsx)("div",{className:"result-url",children:t.url}),t.description&&(0,n.jsx)("div",{className:"result-description",children:t.description}),(0,n.jsxs)("div",{className:"result-meta",children:[(0,n.jsx)("span",{className:"result-type",children:E(t.type)}),t.workspaceName&&(0,n.jsxs)("span",{className:"result-workspace",children:["📁 ",t.workspaceName]}),t.groupName&&(0,n.jsxs)("span",{className:"result-group",children:["📂 ",t.groupName]}),(0,n.jsxs)("span",{className:"result-score",children:["相关性: ",t.relevanceScore.toFixed(1)]})]}),t.matchedFields.length>0&&(0,n.jsxs)("div",{className:"matched-fields",children:["匹配字段: ",t.matchedFields.join(", ")]})]})]},t.id))}),!t&&d.length>0&&(0,n.jsxs)("div",{className:"search-history",children:[(0,n.jsxs)("div",{className:"history-header",children:[(0,n.jsx)("h4",{children:"🕐 搜索历史"}),(0,n.jsx)("button",{onClick:async()=>{if(confirm("确定要清空搜索历史吗？"))try{await S.clearSearchHistory(),h([])}catch(e){console.error("Failed to clear search history:",e)}},className:"btn btn-small btn-secondary",children:"清空"})]}),(0,n.jsx)("div",{className:"history-list",children:d.slice(0,10).map((e,t)=>(0,n.jsxs)("div",{className:"history-item",onClick:()=>A(e),children:[(0,n.jsx)("span",{className:"history-icon",children:"🕐"}),(0,n.jsx)("span",{className:"history-text",children:e})]},t))})]}),!t&&(0,n.jsxs)("div",{className:"search-tips",children:[(0,n.jsx)("h4",{children:"💡 搜索提示"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:"输入关键词搜索标签页、收藏、工作空间和分组"}),(0,n.jsx)("li",{children:"使用过滤器缩小搜索范围"}),(0,n.jsx)("li",{children:"支持按相关性、日期、访问次数排序"}),(0,n.jsx)("li",{children:"点击搜索历史快速重复搜索"})]})]})]})};class k{static THEME_KEY="currentTheme";static CUSTOM_THEMES_KEY="customThemes";static DEFAULT_THEMES=[{id:"light",name:"明亮主题",description:"清新明亮的白色主题",colors:{primary:"#3B82F6",secondary:"#6B7280",accent:"#8B5CF6",background:"#FFFFFF",surface:"#F8FAFC",text:"#1F2937",textSecondary:"#6B7280",border:"#E5E7EB",success:"#10B981",warning:"#F59E0B",error:"#EF4444"},fonts:{primary:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',secondary:'Georgia, "Times New Roman", serif',mono:'"SF Mono", Monaco, "Cascadia Code", monospace'},spacing:{xs:"4px",sm:"8px",md:"16px",lg:"24px",xl:"32px"},borderRadius:{sm:"4px",md:"8px",lg:"12px"},shadows:{sm:"0 1px 2px rgba(0, 0, 0, 0.05)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"}},{id:"dark",name:"深色主题",description:"护眼的深色主题",colors:{primary:"#60A5FA",secondary:"#9CA3AF",accent:"#A78BFA",background:"#111827",surface:"#1F2937",text:"#F9FAFB",textSecondary:"#D1D5DB",border:"#374151",success:"#34D399",warning:"#FBBF24",error:"#F87171"},fonts:{primary:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',secondary:'Georgia, "Times New Roman", serif',mono:'"SF Mono", Monaco, "Cascadia Code", monospace'},spacing:{xs:"4px",sm:"8px",md:"16px",lg:"24px",xl:"32px"},borderRadius:{sm:"4px",md:"8px",lg:"12px"},shadows:{sm:"0 1px 2px rgba(0, 0, 0, 0.3)",md:"0 4px 6px rgba(0, 0, 0, 0.3)",lg:"0 10px 15px rgba(0, 0, 0, 0.3)"}},{id:"blue",name:"蓝色主题",description:"专业的蓝色商务主题",colors:{primary:"#1E40AF",secondary:"#64748B",accent:"#0EA5E9",background:"#F8FAFC",surface:"#FFFFFF",text:"#0F172A",textSecondary:"#475569",border:"#CBD5E1",success:"#059669",warning:"#D97706",error:"#DC2626"},fonts:{primary:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',secondary:'Georgia, "Times New Roman", serif',mono:'"SF Mono", Monaco, "Cascadia Code", monospace'},spacing:{xs:"4px",sm:"8px",md:"16px",lg:"24px",xl:"32px"},borderRadius:{sm:"4px",md:"8px",lg:"12px"},shadows:{sm:"0 1px 2px rgba(30, 64, 175, 0.1)",md:"0 4px 6px rgba(30, 64, 175, 0.1)",lg:"0 10px 15px rgba(30, 64, 175, 0.1)"}},{id:"green",name:"绿色主题",description:"清新的绿色自然主题",colors:{primary:"#059669",secondary:"#6B7280",accent:"#10B981",background:"#F0FDF4",surface:"#FFFFFF",text:"#064E3B",textSecondary:"#047857",border:"#BBF7D0",success:"#22C55E",warning:"#EAB308",error:"#EF4444"},fonts:{primary:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',secondary:'Georgia, "Times New Roman", serif',mono:'"SF Mono", Monaco, "Cascadia Code", monospace'},spacing:{xs:"4px",sm:"8px",md:"16px",lg:"24px",xl:"32px"},borderRadius:{sm:"4px",md:"8px",lg:"12px"},shadows:{sm:"0 1px 2px rgba(5, 150, 105, 0.1)",md:"0 4px 6px rgba(5, 150, 105, 0.1)",lg:"0 10px 15px rgba(5, 150, 105, 0.1)"}}];static async getCurrentTheme(){try{const e=await i.StorageManager.getItem(this.THEME_KEY);if(e){const t=await this.getThemeById(e);if(t)return t}return this.DEFAULT_THEMES[0]}catch(e){return console.error("Failed to get current theme:",e),this.DEFAULT_THEMES[0]}}static async setCurrentTheme(e){try{const t=await this.getThemeById(e);if(!t)throw new Error("Theme not found");await i.StorageManager.setItem(this.THEME_KEY,e),await this.applyTheme(t),console.log("Theme applied:",t.name)}catch(e){throw console.error("Failed to set current theme:",e),e}}static async getAllThemes(){try{const e=await i.StorageManager.getItem(this.CUSTOM_THEMES_KEY)||[];return[...this.DEFAULT_THEMES,...e]}catch(e){return console.error("Failed to get all themes:",e),this.DEFAULT_THEMES}}static async getThemeById(e){try{return(await this.getAllThemes()).find(t=>t.id===e)||null}catch(e){return console.error("Failed to get theme by id:",e),null}}static async applyTheme(e){try{const t=document.documentElement;Object.entries(e.colors).forEach(([e,s])=>{t.style.setProperty(`--color-${e}`,s)}),Object.entries(e.fonts).forEach(([e,s])=>{t.style.setProperty(`--font-${e}`,s)}),Object.entries(e.spacing).forEach(([e,s])=>{t.style.setProperty(`--spacing-${e}`,s)}),Object.entries(e.borderRadius).forEach(([e,s])=>{t.style.setProperty(`--radius-${e}`,s)}),Object.entries(e.shadows).forEach(([e,s])=>{t.style.setProperty(`--shadow-${e}`,s)}),document.body.className=document.body.className.replace(/theme-\w+/g,""),document.body.classList.add(`theme-${e.id}`),console.log("Theme variables applied")}catch(e){throw console.error("Failed to apply theme:",e),e}}static async createCustomTheme(e){try{const t=await i.StorageManager.getItem(this.CUSTOM_THEMES_KEY)||[],s={...e,id:`custom_${Date.now()}`};return t.push(s),await i.StorageManager.setItem(this.CUSTOM_THEMES_KEY,t),console.log("Custom theme created:",s.name),s.id}catch(e){throw console.error("Failed to create custom theme:",e),e}}static async deleteCustomTheme(e){try{if(!e.startsWith("custom_"))throw new Error("Cannot delete built-in theme");const t=(await i.StorageManager.getItem(this.CUSTOM_THEMES_KEY)||[]).filter(t=>t.id!==e);await i.StorageManager.setItem(this.CUSTOM_THEMES_KEY,t),await i.StorageManager.getItem(this.THEME_KEY)===e&&await this.setCurrentTheme("light"),console.log("Custom theme deleted:",e)}catch(e){throw console.error("Failed to delete custom theme:",e),e}}static async exportTheme(e){try{const t=await this.getThemeById(e);if(!t)throw new Error("Theme not found");return JSON.stringify(t,null,2)}catch(e){throw console.error("Failed to export theme:",e),e}}static async importTheme(e){try{const t=JSON.parse(e);if(!t.name||!t.colors||!t.fonts)throw new Error("Invalid theme format");const s=await this.createCustomTheme({name:t.name,description:t.description||"",colors:t.colors,fonts:t.fonts,spacing:t.spacing,borderRadius:t.borderRadius,shadows:t.shadows});return console.log("Theme imported successfully:",t.name),s}catch(e){throw console.error("Failed to import theme:",e),e}}static async initialize(){try{const e=await this.getCurrentTheme();await this.applyTheme(e),console.log("Theme system initialized")}catch(e){console.error("Failed to initialize theme system:",e)}}static detectSystemTheme(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}static setupSystemThemeListener(e){if(window.matchMedia){const t=window.matchMedia("(prefers-color-scheme: dark)"),s=t=>{e(t.matches?"dark":"light")};return t.addEventListener("change",s),()=>{t.removeEventListener("change",s)}}}}class F{static SYNC_CONFIG_KEY="syncConfig";static SYNC_STATUS_KEY="syncStatus";static PENDING_CHANGES_KEY="pendingChanges";static syncInterval=null;static async initialize(){try{const e=await this.getSyncConfig();e&&e.autoSync&&await this.startAutoSync(),this.setupNetworkListener(),console.log("Sync manager initialized")}catch(e){console.error("Failed to initialize sync manager:",e)}}static async configurSync(e){try{await i.StorageManager.setItem(this.SYNC_CONFIG_KEY,e),e.autoSync?await this.startAutoSync():this.stopAutoSync(),console.log("Sync configured successfully")}catch(e){throw console.error("Failed to configure sync:",e),e}}static async getSyncConfig(){try{return await i.StorageManager.getItem(this.SYNC_CONFIG_KEY)}catch(e){return console.error("Failed to get sync config:",e),null}}static async getSyncStatus(){try{return await i.StorageManager.getItem(this.SYNC_STATUS_KEY)||{lastSyncTime:"",isOnline:navigator.onLine,isSyncing:!1,hasConflicts:!1,pendingChanges:0}}catch(e){return console.error("Failed to get sync status:",e),{lastSyncTime:"",isOnline:!1,isSyncing:!1,hasConflicts:!1,pendingChanges:0}}}static async updateSyncStatus(e){try{const t={...await this.getSyncStatus(),...e};await i.StorageManager.setItem(this.SYNC_STATUS_KEY,t)}catch(e){console.error("Failed to update sync status:",e)}}static async manualSync(){try{await this.updateSyncStatus({isSyncing:!0});const e=await this.getSyncConfig();if(!e)throw new Error("Sync not configured");return await this.uploadLocalData(e),await this.downloadServerData(e),await this.updateSyncStatus({isSyncing:!1,lastSyncTime:(new Date).toISOString(),pendingChanges:0}),console.log("Manual sync completed successfully"),!0}catch(e){throw console.error("Manual sync failed:",e),await this.updateSyncStatus({isSyncing:!1}),e}}static async uploadLocalData(e){try{const t=await i.StorageManager.getAllWorkspaces();for(const s of t)await this.uploadWorkspace(e,s);const s=await x.getAllFavorites();for(const t of s)await this.uploadFavorite(e,t);console.log("Local data uploaded successfully")}catch(e){throw console.error("Failed to upload local data:",e),e}}static async downloadServerData(e){try{const t=await this.fetchWorkspaces(e);await this.mergeWorkspaces(t);const s=await this.fetchFavorites(e);await this.mergeFavorites(s),console.log("Server data downloaded successfully")}catch(e){throw console.error("Failed to download server data:",e),e}}static async uploadWorkspace(e,t){try{const s=await fetch(`${e.serverUrl}/api/workspaces`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e.apiKey}`},body:JSON.stringify(t)});if(!s.ok)throw new Error(`Failed to upload workspace: ${s.statusText}`)}catch(e){throw console.error("Failed to upload workspace:",e),e}}static async uploadFavorite(e,t){try{const s=await fetch(`${e.serverUrl}/api/favorites`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e.apiKey}`},body:JSON.stringify(t)});if(!s.ok)throw new Error(`Failed to upload favorite: ${s.statusText}`)}catch(e){throw console.error("Failed to upload favorite:",e),e}}static async fetchWorkspaces(e){try{const t=await fetch(`${e.serverUrl}/api/workspaces`,{headers:{Authorization:`Bearer ${e.apiKey}`}});if(!t.ok)throw new Error(`Failed to fetch workspaces: ${t.statusText}`);return(await t.json()).data.workspaces||[]}catch(e){throw console.error("Failed to fetch workspaces:",e),e}}static async fetchFavorites(e){try{const t=await fetch(`${e.serverUrl}/api/favorites`,{headers:{Authorization:`Bearer ${e.apiKey}`}});if(!t.ok)throw new Error(`Failed to fetch favorites: ${t.statusText}`);return(await t.json()).data.favorites||[]}catch(e){throw console.error("Failed to fetch favorites:",e),e}}static async mergeWorkspaces(e){try{const t=await i.StorageManager.getAllWorkspaces(),s=this.mergeDataArrays(t,e,"id");for(const e of s)await i.StorageManager.saveWorkspace(e)}catch(e){throw console.error("Failed to merge workspaces:",e),e}}static async mergeFavorites(e){try{const t=await x.getAllFavorites(),s=this.mergeDataArrays(t,e,"id");await x.clearAllFavorites();for(const e of s)await i.StorageManager.setItem("favorites",s)}catch(e){throw console.error("Failed to merge favorites:",e),e}}static mergeDataArrays(e,t,s){const a=new Map;return e.forEach(e=>{a.set(e[s],e)}),t.forEach(e=>{const t=a.get(e[s]);(!t||new Date(e.updatedAt)>new Date(t.updatedAt))&&a.set(e[s],e)}),Array.from(a.values())}static async startAutoSync(){try{const e=await this.getSyncConfig();if(!e||!e.autoSync)return;this.stopAutoSync(),this.syncInterval=setInterval(async()=>{try{navigator.onLine&&await this.manualSync()}catch(e){console.error("Auto sync failed:",e)}},60*e.syncInterval*1e3),console.log(`Auto sync started with interval: ${e.syncInterval} minutes`)}catch(e){console.error("Failed to start auto sync:",e)}}static stopAutoSync(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null,console.log("Auto sync stopped"))}static setupNetworkListener(){window.addEventListener("online",async()=>{await this.updateSyncStatus({isOnline:!0});const e=await this.getSyncConfig();if(e&&e.autoSync)try{await this.manualSync()}catch(e){console.error("Failed to sync after network recovery:",e)}}),window.addEventListener("offline",async()=>{await this.updateSyncStatus({isOnline:!1})})}static async isSyncConfigured(){const e=await this.getSyncConfig();return!!(e&&e.serverUrl&&e.apiKey)}static async resetSync(){try{this.stopAutoSync(),await i.StorageManager.removeItem(this.SYNC_CONFIG_KEY),await i.StorageManager.removeItem(this.SYNC_STATUS_KEY),await i.StorageManager.removeItem(this.PENDING_CHANGES_KEY),console.log("Sync configuration reset")}catch(e){throw console.error("Failed to reset sync:",e),e}}}const C=({onSyncConfigChange:e})=>{const[t,s]=(0,a.useState)({serverUrl:"http://localhost:3001",apiKey:"",userId:"",autoSync:!1,syncInterval:30}),[r,o]=(0,a.useState)({lastSyncTime:"",isOnline:navigator.onLine,isSyncing:!1,hasConflicts:!1,pendingChanges:0}),[i,c]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1);(0,a.useEffect)(()=>{p()},[]);const p=async()=>{try{const e=await F.getSyncConfig();e&&s(e);const t=await F.getSyncStatus();o(t);const a=await F.isSyncConfigured();c(a)}catch(e){console.error("Failed to load sync data:",e)}};return(0,n.jsxs)("div",{className:"sync-settings-panel",children:[(0,n.jsxs)("div",{className:"panel-header",children:[(0,n.jsx)("h3",{children:"☁️ 数据同步设置"}),(0,n.jsxs)("div",{className:"sync-status",children:[(0,n.jsx)("span",{className:"status-indicator "+(r.isOnline?"online":"offline"),children:r.isOnline?"🟢 在线":"🔴 离线"}),r.isSyncing&&(0,n.jsx)("span",{className:"syncing",children:"🔄 同步中..."})]})]}),(0,n.jsxs)("div",{className:"sync-status-card",children:[(0,n.jsxs)("div",{className:"status-item",children:[(0,n.jsx)("span",{className:"status-label",children:"配置状态:"}),(0,n.jsx)("span",{className:"status-value "+(i?"configured":"not-configured"),children:i?"✅ 已配置":"❌ 未配置"})]}),(0,n.jsxs)("div",{className:"status-item",children:[(0,n.jsx)("span",{className:"status-label",children:"最后同步:"}),(0,n.jsx)("span",{className:"status-value",children:(e=>{if(!e)return"从未同步";const t=new Date(e),s=(new Date).getTime()-t.getTime(),a=Math.floor(s/6e4);return a<1?"刚刚":a<60?`${a} 分钟前`:a<1440?`${Math.floor(a/60)} 小时前`:t.toLocaleDateString()})(r.lastSyncTime)})]}),(0,n.jsxs)("div",{className:"status-item",children:[(0,n.jsx)("span",{className:"status-label",children:"待同步更改:"}),(0,n.jsxs)("span",{className:"status-value",children:[r.pendingChanges," 项"]})]}),r.hasConflicts&&(0,n.jsxs)("div",{className:"status-item conflict",children:[(0,n.jsx)("span",{className:"status-label",children:"⚠️ 冲突:"}),(0,n.jsx)("span",{className:"status-value",children:"需要手动解决"})]})]}),(0,n.jsxs)("div",{className:"quick-actions",children:[(0,n.jsx)("button",{onClick:async()=>{try{d(!0),await F.manualSync(),await p(),alert("同步完成！")}catch(e){console.error("Manual sync failed:",e),alert("同步失败："+e.message)}finally{d(!1)}},disabled:!i||l||r.isSyncing,className:"btn btn-primary",children:r.isSyncing?"🔄 同步中...":"🔄 立即同步"}),(0,n.jsxs)("button",{onClick:()=>u(!h),className:"btn btn-secondary",children:["⚙️ ",h?"隐藏":"显示","高级设置"]})]}),h&&(0,n.jsxs)("div",{className:"sync-config",children:[(0,n.jsx)("h4",{children:"🔧 同步配置"}),(0,n.jsxs)("div",{className:"config-form",children:[(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{children:"服务器地址:"}),(0,n.jsx)("input",{type:"text",value:t.serverUrl,onChange:e=>s({...t,serverUrl:e.target.value}),placeholder:"http://localhost:3001",className:"config-input"})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{children:"API密钥:"}),(0,n.jsx)("input",{type:"password",value:t.apiKey,onChange:e=>s({...t,apiKey:e.target.value}),placeholder:"输入您的API密钥",className:"config-input"})]}),(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{children:"用户ID:"}),(0,n.jsx)("input",{type:"text",value:t.userId,onChange:e=>s({...t,userId:e.target.value}),placeholder:"用户唯一标识",className:"config-input"})]}),(0,n.jsx)("div",{className:"form-group checkbox-group",children:(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:t.autoSync,onChange:e=>s({...t,autoSync:e.target.checked})}),"启用自动同步"]})}),t.autoSync&&(0,n.jsxs)("div",{className:"form-group",children:[(0,n.jsx)("label",{children:"同步间隔 (分钟):"}),(0,n.jsxs)("select",{value:t.syncInterval,onChange:e=>s({...t,syncInterval:parseInt(e.target.value)}),className:"config-select",children:[(0,n.jsx)("option",{value:5,children:"5 分钟"}),(0,n.jsx)("option",{value:15,children:"15 分钟"}),(0,n.jsx)("option",{value:30,children:"30 分钟"}),(0,n.jsx)("option",{value:60,children:"1 小时"}),(0,n.jsx)("option",{value:180,children:"3 小时"})]})]})]}),(0,n.jsxs)("div",{className:"config-actions",children:[(0,n.jsx)("button",{onClick:async()=>{try{if(d(!0),!t.serverUrl||!t.apiKey)return void alert("请填写服务器地址和API密钥");await F.configurSync(t),c(!0),null==e||e(),alert("同步配置保存成功！")}catch(e){console.error("Failed to save sync config:",e),alert("保存配置失败："+e.message)}finally{d(!1)}},disabled:l,className:"btn btn-primary",children:"💾 保存配置"}),(0,n.jsx)("button",{onClick:async()=>{if(confirm("确定要重置同步配置吗？这将清除所有同步设置。"))try{d(!0),await F.resetSync(),s({serverUrl:"http://localhost:3001",apiKey:"",userId:"",autoSync:!1,syncInterval:30}),c(!1),null==e||e(),alert("同步配置已重置")}catch(e){console.error("Failed to reset sync:",e),alert("重置失败："+e.message)}finally{d(!1)}},disabled:l,className:"btn btn-danger",children:"🗑️ 重置配置"})]})]}),(0,n.jsxs)("div",{className:"sync-info",children:[(0,n.jsx)("h4",{children:"💡 同步说明"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:"数据同步可以在多个设备间保持工作空间和收藏的一致性"}),(0,n.jsx)("li",{children:"首次配置需要有效的服务器地址和API密钥"}),(0,n.jsx)("li",{children:"自动同步会在后台定期同步数据"}),(0,n.jsx)("li",{children:"离线时的更改会在网络恢复后自动同步"}),(0,n.jsx)("li",{children:"如遇冲突，服务器数据将优先保留"})]})]}),(0,n.jsxs)("div",{className:"data-management",children:[(0,n.jsx)("h4",{children:"📊 数据管理"}),(0,n.jsxs)("div",{className:"data-stats",children:[(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("span",{className:"stat-icon",children:"📁"}),(0,n.jsxs)("div",{className:"stat-info",children:[(0,n.jsx)("span",{className:"stat-label",children:"工作空间"}),(0,n.jsx)("span",{className:"stat-value",children:"-- 个"})]})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("span",{className:"stat-icon",children:"⭐"}),(0,n.jsxs)("div",{className:"stat-info",children:[(0,n.jsx)("span",{className:"stat-label",children:"收藏"}),(0,n.jsx)("span",{className:"stat-value",children:"-- 个"})]})]}),(0,n.jsxs)("div",{className:"stat-card",children:[(0,n.jsx)("span",{className:"stat-icon",children:"📂"}),(0,n.jsxs)("div",{className:"stat-info",children:[(0,n.jsx)("span",{className:"stat-label",children:"分组"}),(0,n.jsx)("span",{className:"stat-value",children:"-- 个"})]})]})]})]}),(0,n.jsxs)("div",{className:"troubleshooting",children:[(0,n.jsx)("h4",{children:"🔧 故障排除"}),(0,n.jsxs)("details",{children:[(0,n.jsx)("summary",{children:"常见问题解决方案"}),(0,n.jsxs)("div",{className:"troubleshooting-content",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"同步失败:"})," 检查网络连接和服务器地址"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"API密钥错误:"})," 确认密钥有效且权限正确"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"数据冲突:"})," 手动同步会以服务器数据为准"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"自动同步不工作:"})," 检查浏览器是否允许后台运行"]})]})]})]})]})},T=({onSettingsChange:e})=>{const[t,s]=(0,a.useState)("general"),[r,o]=(0,a.useState)([]),[i,c]=(0,a.useState)("light"),[l,d]=(0,a.useState)({autoSaveWorkspace:!0,showNotifications:!0,compactMode:!1,animationsEnabled:!0,defaultView:"tabs",maxRecentTabs:10,searchHistoryLimit:50});(0,a.useEffect)(()=>{h(),u()},[]);const h=async()=>{},u=async()=>{try{const e=await k.getAllThemes();o(e);const t=await k.getCurrentTheme();c(t.id)}catch(e){console.error("Failed to load themes:",e)}},p=async t=>{try{await k.setCurrentTheme(t),c(t),null==e||e()}catch(e){console.error("Failed to change theme:",e),alert("主题切换失败")}},m=(t,s)=>{const a={...l,[t]:s};d(a),null==e||e()};return(0,n.jsxs)("div",{className:"settings-panel",children:[(0,n.jsx)("div",{className:"settings-header",children:(0,n.jsx)("h3",{children:"⚙️ 设置"})}),(0,n.jsxs)("div",{className:"settings-nav",children:[(0,n.jsx)("button",{className:"nav-item "+("general"===t?"active":""),onClick:()=>s("general"),children:"🔧 常规"}),(0,n.jsx)("button",{className:"nav-item "+("theme"===t?"active":""),onClick:()=>s("theme"),children:"🎨 主题"}),(0,n.jsx)("button",{className:"nav-item "+("sync"===t?"active":""),onClick:()=>s("sync"),children:"☁️ 同步"}),(0,n.jsx)("button",{className:"nav-item "+("about"===t?"active":""),onClick:()=>s("about"),children:"ℹ️ 关于"})]}),(0,n.jsxs)("div",{className:"settings-content",children:["general"===t&&(0,n.jsxs)("div",{className:"settings-section",children:[(0,n.jsx)("h4",{children:"🔧 常规设置"}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:l.autoSaveWorkspace,onChange:e=>m("autoSaveWorkspace",e.target.checked)}),"自动保存工作空间"]}),(0,n.jsx)("p",{className:"setting-description",children:"在关闭标签页时自动保存到当前工作空间"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:l.showNotifications,onChange:e=>m("showNotifications",e.target.checked)}),"显示通知"]}),(0,n.jsx)("p",{className:"setting-description",children:"显示操作成功、失败等通知消息"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:l.compactMode,onChange:e=>m("compactMode",e.target.checked)}),"紧凑模式"]}),(0,n.jsx)("p",{className:"setting-description",children:"使用更紧凑的界面布局"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsxs)("label",{children:[(0,n.jsx)("input",{type:"checkbox",checked:l.animationsEnabled,onChange:e=>m("animationsEnabled",e.target.checked)}),"启用动画效果"]}),(0,n.jsx)("p",{className:"setting-description",children:"启用界面动画和过渡效果"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsx)("label",{children:"默认视图:"}),(0,n.jsxs)("select",{value:l.defaultView,onChange:e=>m("defaultView",e.target.value),className:"setting-select",children:[(0,n.jsx)("option",{value:"tabs",children:"标签页"}),(0,n.jsx)("option",{value:"management",children:"管理"}),(0,n.jsx)("option",{value:"workspaces",children:"工作空间"}),(0,n.jsx)("option",{value:"presets",children:"预设"}),(0,n.jsx)("option",{value:"favorites",children:"收藏"}),(0,n.jsx)("option",{value:"search",children:"搜索"})]}),(0,n.jsx)("p",{className:"setting-description",children:"打开扩展时的默认显示页面"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsx)("label",{children:"最近标签页数量:"}),(0,n.jsx)("input",{type:"number",min:"5",max:"50",value:l.maxRecentTabs,onChange:e=>m("maxRecentTabs",parseInt(e.target.value)),className:"setting-input"}),(0,n.jsx)("p",{className:"setting-description",children:"保存的最近访问标签页数量"})]}),(0,n.jsxs)("div",{className:"setting-item",children:[(0,n.jsx)("label",{children:"搜索历史限制:"}),(0,n.jsx)("input",{type:"number",min:"10",max:"100",value:l.searchHistoryLimit,onChange:e=>m("searchHistoryLimit",parseInt(e.target.value)),className:"setting-input"}),(0,n.jsx)("p",{className:"setting-description",children:"保存的搜索历史记录数量"})]})]}),"theme"===t&&(0,n.jsxs)("div",{className:"settings-section",children:[(0,n.jsx)("h4",{children:"🎨 主题设置"}),(0,n.jsx)("div",{className:"theme-grid",children:r.map(e=>(0,n.jsxs)("div",{className:"theme-card "+(i===e.id?"active":""),onClick:()=>p(e.id),children:[(0,n.jsx)("div",{className:"theme-preview",style:{background:`linear-gradient(135deg, ${e.colors.primary}, ${e.colors.accent})`},children:(0,n.jsxs)("div",{className:"theme-preview-content",style:{backgroundColor:e.colors.surface,color:e.colors.text,border:`1px solid ${e.colors.border}`},children:[(0,n.jsxs)("div",{className:"preview-header",style:{backgroundColor:e.colors.background},children:[(0,n.jsx)("div",{className:"preview-dot",style:{backgroundColor:e.colors.primary}}),(0,n.jsx)("div",{className:"preview-dot",style:{backgroundColor:e.colors.accent}}),(0,n.jsx)("div",{className:"preview-dot",style:{backgroundColor:e.colors.secondary}})]}),(0,n.jsx)("div",{className:"preview-body",children:(0,n.jsx)("div",{className:"preview-text",style:{color:e.colors.text},children:"Aa"})})]})}),(0,n.jsxs)("div",{className:"theme-info",children:[(0,n.jsx)("h5",{children:e.name}),(0,n.jsx)("p",{children:e.description}),i===e.id&&(0,n.jsx)("span",{className:"current-badge",children:"当前"})]})]},e.id))}),(0,n.jsxs)("div",{className:"theme-actions",children:[(0,n.jsx)("button",{className:"btn btn-secondary",disabled:!0,children:"🎨 创建自定义主题"}),(0,n.jsx)("button",{className:"btn btn-secondary",disabled:!0,children:"📤 导出主题"}),(0,n.jsxs)("label",{className:"btn btn-secondary",children:["📥 导入主题",(0,n.jsx)("input",{type:"file",accept:".json",style:{display:"none"},disabled:!0})]})]})]}),"sync"===t&&(0,n.jsx)(C,{onSyncConfigChange:e}),"about"===t&&(0,n.jsxs)("div",{className:"settings-section",children:[(0,n.jsx)("h4",{children:"ℹ️ 关于"}),(0,n.jsxs)("div",{className:"about-content",children:[(0,n.jsxs)("div",{className:"app-info",children:[(0,n.jsx)("h5",{children:"🤖 AI工作台 Chrome扩展"}),(0,n.jsx)("p",{children:"版本: 1.0.0"}),(0,n.jsxs)("p",{children:["构建时间: ",(new Date).toLocaleDateString()]})]}),(0,n.jsxs)("div",{className:"features-list",children:[(0,n.jsx)("h6",{children:"✨ 主要功能:"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:"🏷️ 智能标签页管理和分组"}),(0,n.jsx)("li",{children:"📁 多工作空间支持"}),(0,n.jsx)("li",{children:"🚀 26个AI工具预设分组"}),(0,n.jsx)("li",{children:"⭐ 收藏管理和快速访问"}),(0,n.jsx)("li",{children:"🔍 全局搜索和智能推荐"}),(0,n.jsx)("li",{children:"☁️ 数据同步和备份"}),(0,n.jsx)("li",{children:"🎨 多主题支持"})]})]}),(0,n.jsxs)("div",{className:"links",children:[(0,n.jsx)("h6",{children:"🔗 相关链接:"}),(0,n.jsx)("a",{href:"#",className:"link-item",children:"📖 使用文档"}),(0,n.jsx)("a",{href:"#",className:"link-item",children:"🐛 问题反馈"}),(0,n.jsx)("a",{href:"#",className:"link-item",children:"⭐ 项目主页"}),(0,n.jsx)("a",{href:"#",className:"link-item",children:"💬 用户社区"})]}),(0,n.jsxs)("div",{className:"data-actions",children:[(0,n.jsx)("h6",{children:"📊 数据管理:"}),(0,n.jsx)("button",{onClick:()=>{const e={settings:l,currentTheme:i,exportDate:(new Date).toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),s=URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download=`ai-workspace-settings-${(new Date).toISOString().split("T")[0]}.json`,a.click(),URL.revokeObjectURL(s)},className:"btn btn-secondary",children:"📤 导出设置"}),(0,n.jsxs)("label",{className:"btn btn-secondary",children:["📥 导入设置",(0,n.jsx)("input",{type:"file",accept:".json",onChange:async e=>{var t;const s=null===(t=e.target.files)||void 0===t?void 0:t[0];if(s){try{const e=await s.text(),t=JSON.parse(e);t.settings&&d(t.settings),t.currentTheme&&await p(t.currentTheme),alert("设置导入成功！")}catch(e){console.error("Failed to import settings:",e),alert("设置导入失败："+e.message)}e.target.value=""}},style:{display:"none"}})]})]})]})]})]})]})},A=()=>{const[e,t]=(0,a.useState)([]),[s,r]=(0,a.useState)([]),[u,p]=(0,a.useState)(null),[m,y]=(0,a.useState)(""),[v,w]=(0,a.useState)(!0),[f,x]=(0,a.useState)("tabs");(0,a.useEffect)(()=>{S()},[]);const S=async()=>{try{w(!0);const e=await o.getCurrentTabs();t(e);const s=await i.StorageManager.getWorkspaces();r(s);const a=await i.StorageManager.getActiveWorkspace();p(a)}catch(e){console.error("Failed to initialize app:",e)}finally{w(!1)}},k=e.filter(e=>e.title.toLowerCase().includes(m.toLowerCase())||e.url.toLowerCase().includes(m.toLowerCase()));return v?(0,n.jsx)("div",{className:"popup-container",children:(0,n.jsxs)("div",{className:"loading",children:[(0,n.jsx)("div",{className:"spinner"}),(0,n.jsx)("p",{children:"加载中..."})]})}):(0,n.jsxs)("div",{className:"popup-container",children:[(0,n.jsx)(c,{onSaveCurrentTabs:async()=>{try{if(!u)return void alert("请先选择一个工作空间");await o.saveTabsToWorkspace(e,u),alert("标签页已保存到工作空间")}catch(e){console.error("Failed to save tabs:",e),alert("保存失败")}},activeWorkspace:u}),(0,n.jsxs)("div",{className:"tab-navigation",children:[(0,n.jsx)("button",{className:"nav-tab "+("tabs"===f?"active":""),onClick:()=>x("tabs"),children:"📋 标签页"}),(0,n.jsx)("button",{className:"nav-tab "+("management"===f?"active":""),onClick:()=>x("management"),children:"🛠️ 管理"}),(0,n.jsx)("button",{className:"nav-tab "+("workspaces"===f?"active":""),onClick:()=>x("workspaces"),children:"📁 工作空间"}),(0,n.jsx)("button",{className:"nav-tab "+("presets"===f?"active":""),onClick:()=>x("presets"),children:"🚀 预设"}),(0,n.jsx)("button",{className:"nav-tab "+("favorites"===f?"active":""),onClick:()=>x("favorites"),children:"⭐ 收藏"}),(0,n.jsx)("button",{className:"nav-tab "+("search"===f?"active":""),onClick:()=>x("search"),children:"🔍 搜索"}),(0,n.jsx)("button",{className:"nav-tab "+("settings"===f?"active":""),onClick:()=>x("settings"),children:"⚙️ 设置"})]}),"tabs"===f&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h,{value:m,onChange:y,placeholder:"搜索标签页..."}),(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(d,{tabs:k,searchQuery:m})})]}),"management"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(g,{tabs:e,onTabsChange:S})}),"workspaces"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(l,{workspaces:s,activeWorkspace:u,onWorkspaceChange:async e=>{p(e),await i.StorageManager.setActiveWorkspace(e)},onOpenWorkspace:async e=>{try{await o.openWorkspace(e)}catch(e){console.error("Failed to open workspace:",e),alert("打开工作空间失败")}}})}),"presets"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(b,{activeWorkspace:u,onWorkspaceChange:S})}),"favorites"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(j,{onFavoritesChange:S})}),"search"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(N,{onResultSelect:e=>{console.log("Search result selected:",e)}})}),"settings"===f&&(0,n.jsx)("div",{className:"content",children:(0,n.jsx)(T,{onSettingsChange:S})})]})},I=document.getElementById("root");I?(0,r.H)(I).render((0,n.jsx)(A,{})):console.error("Root container not found")}},s={};function a(e){var r=s[e];if(void 0!==r)return r.exports;var o=s[e]={exports:{}};return t[e](o,o.exports,a),o.exports}a.m=t,e=[],a.O=(t,s,r,o)=>{if(!s){var i=1/0;for(d=0;d<e.length;d++){for(var[s,r,o]=e[d],n=!0,c=0;c<s.length;c++)(!1&o||i>=o)&&Object.keys(a.O).every(e=>a.O[e](s[c]))?s.splice(c--,1):(n=!1,o<i&&(i=o));if(n){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}o=o||0;for(var d=e.length;d>0&&e[d-1][2]>o;d--)e[d]=e[d-1];e[d]=[s,r,o]},a.d=(e,t)=>{for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={887:0};a.O.j=t=>0===e[t];var t=(t,s)=>{var r,o,[i,n,c]=s,l=0;if(i.some(t=>0!==e[t])){for(r in n)a.o(n,r)&&(a.m[r]=n[r]);if(c)var d=c(a)}for(t&&t(s);l<i.length;l++)o=i[l],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(d)},s=self.webpackChunkai_workspace_extension=self.webpackChunkai_workspace_extension||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})();var r=a.O(void 0,[96],()=>a(738));r=a.O(r)})();