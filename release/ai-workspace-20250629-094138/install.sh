#!/bin/bash

echo "🚀 开始安装AI工作台..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 安装服务器依赖
echo "📦 安装服务器依赖..."
cd server
npm install

# 创建环境配置文件
if [ ! -f ".env" ]; then
    echo "⚙️ 创建环境配置文件..."
    cp .env.example .env
    echo "请编辑 .env 文件配置数据库连接信息"
fi

echo "✅ 安装完成！"
echo ""
echo "下一步："
echo "1. 配置 server/.env 文件"
echo "2. 初始化数据库（执行 database/init.sql）"
echo "3. 启动服务器：cd server && npm start"
echo "4. 在Chrome中加载扩展：chrome://extensions/"
