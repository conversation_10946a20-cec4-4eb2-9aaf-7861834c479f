const { query } = require('../config/database');
const { AppError } = require('../middleware/errorHandler');

class TabGroup {
  constructor(groupData) {
    this.id = groupData.id;
    this.workspaceId = groupData.workspace_id;
    this.name = groupData.name;
    this.description = groupData.description;
    this.color = groupData.color;
    this.icon = groupData.icon;
    this.isPreset = groupData.is_preset;
    this.presetType = groupData.preset_type;
    this.sortOrder = groupData.sort_order;
    this.settings = groupData.settings;
    this.createdAt = groupData.created_at;
    this.updatedAt = groupData.updated_at;
  }

  // 创建新分组
  static async create({ workspaceId, name, description, color = '#6B7280', icon = '📁', isPreset = false, presetType = null }) {
    try {
      // 获取排序顺序
      const sortOrderResult = await query(
        'SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM tab_groups WHERE workspace_id = ?',
        [workspaceId]
      );
      const sortOrder = sortOrderResult[0].next_order;

      // 插入新分组
      const result = await query(
        `INSERT INTO tab_groups (workspace_id, name, description, color, icon, is_preset, preset_type, sort_order) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [workspaceId, name, description, color, icon, isPreset, presetType, sortOrder]
      );

      // 返回新创建的分组
      const newGroup = await this.findById(result.insertId);
      return newGroup;
    } catch (error) {
      console.error('Failed to create tab group:', error);
      throw new AppError('Failed to create tab group', 500);
    }
  }

  // 根据ID查找分组
  static async findById(id) {
    try {
      const groups = await query(
        'SELECT * FROM tab_groups WHERE id = ?',
        [id]
      );
      
      if (groups.length === 0) {
        return null;
      }
      
      return new TabGroup(groups[0]);
    } catch (error) {
      console.error('Failed to find tab group by id:', error);
      throw new AppError('Failed to find tab group', 500);
    }
  }

  // 根据工作空间ID获取所有分组
  static async findByWorkspaceId(workspaceId) {
    try {
      const groups = await query(
        'SELECT * FROM tab_groups WHERE workspace_id = ? ORDER BY sort_order ASC, created_at ASC',
        [workspaceId]
      );
      
      return groups.map(group => new TabGroup(group));
    } catch (error) {
      console.error('Failed to find tab groups by workspace id:', error);
      throw new AppError('Failed to find tab groups', 500);
    }
  }

  // 获取预设分组
  static async findPresetGroups(workspaceId) {
    try {
      const groups = await query(
        'SELECT * FROM tab_groups WHERE workspace_id = ? AND is_preset = TRUE ORDER BY sort_order ASC',
        [workspaceId]
      );
      
      return groups.map(group => new TabGroup(group));
    } catch (error) {
      console.error('Failed to find preset groups:', error);
      throw new AppError('Failed to find preset groups', 500);
    }
  }

  // 更新分组
  async update(updateData) {
    try {
      const allowedFields = ['name', 'description', 'color', 'icon', 'sort_order', 'settings'];
      const updates = [];
      const values = [];

      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key)) {
          const dbField = key === 'sortOrder' ? 'sort_order' : key;
          updates.push(`${dbField} = ?`);
          values.push(typeof value === 'object' ? JSON.stringify(value) : value);
        }
      }

      if (updates.length === 0) {
        throw new AppError('No valid fields to update', 400);
      }

      values.push(this.id);
      await query(
        `UPDATE tab_groups SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      // 重新获取更新后的分组数据
      const updatedGroup = await TabGroup.findById(this.id);
      Object.assign(this, updatedGroup);
    } catch (error) {
      console.error('Failed to update tab group:', error);
      throw new AppError('Failed to update tab group', 500);
    }
  }

  // 删除分组
  async delete() {
    try {
      // 删除分组（级联删除相关的标签页）
      await query('DELETE FROM tab_groups WHERE id = ?', [this.id]);
    } catch (error) {
      console.error('Failed to delete tab group:', error);
      throw new AppError('Failed to delete tab group', 500);
    }
  }

  // 获取分组的标签页
  async getTabs() {
    try {
      const tabs = await query(
        'SELECT * FROM saved_tabs WHERE group_id = ? ORDER BY created_at ASC',
        [this.id]
      );
      
      return tabs;
    } catch (error) {
      console.error('Failed to get group tabs:', error);
      throw new AppError('Failed to get group tabs', 500);
    }
  }

  // 获取分组的统计信息
  async getStats() {
    try {
      const stats = await query(
        `SELECT 
          COUNT(st.id) as tab_count,
          COUNT(CASE WHEN st.is_pinned = TRUE THEN 1 END) as pinned_count,
          MAX(st.last_visited_at) as last_visited
         FROM tab_groups tg
         LEFT JOIN saved_tabs st ON tg.id = st.group_id
         WHERE tg.id = ?`,
        [this.id]
      );
      
      return stats[0];
    } catch (error) {
      console.error('Failed to get group stats:', error);
      throw new AppError('Failed to get group stats', 500);
    }
  }

  // 移动分组到另一个工作空间
  async moveToWorkspace(newWorkspaceId) {
    try {
      await query(
        'UPDATE tab_groups SET workspace_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newWorkspaceId, this.id]
      );
      
      this.workspaceId = newWorkspaceId;
    } catch (error) {
      console.error('Failed to move group to workspace:', error);
      throw new AppError('Failed to move group to workspace', 500);
    }
  }

  // 复制分组
  async duplicate(newName, targetWorkspaceId = null) {
    try {
      const workspaceId = targetWorkspaceId || this.workspaceId;
      
      // 创建新分组
      const newGroup = await TabGroup.create({
        workspaceId,
        name: newName || `${this.name} (副本)`,
        description: this.description,
        color: this.color,
        icon: this.icon,
        isPreset: false, // 复制的分组不是预设分组
        presetType: null
      });

      // 复制标签页
      const tabs = await this.getTabs();
      for (const tab of tabs) {
        await query(
          `INSERT INTO saved_tabs (user_id, group_id, title, url, favicon_url, description, tags, is_pinned)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [tab.user_id, newGroup.id, tab.title, tab.url, tab.favicon_url,
           tab.description, tab.tags, tab.is_pinned]
        );
      }

      return newGroup;
    } catch (error) {
      console.error('Failed to duplicate group:', error);
      throw new AppError('Failed to duplicate group', 500);
    }
  }

  // 更新排序顺序
  static async updateSortOrder(workspaceId, groupOrders) {
    try {
      for (const { id, sortOrder } of groupOrders) {
        await query(
          'UPDATE tab_groups SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND workspace_id = ?',
          [sortOrder, id, workspaceId]
        );
      }
    } catch (error) {
      console.error('Failed to update group sort order:', error);
      throw new AppError('Failed to update group sort order', 500);
    }
  }

  // 搜索分组
  static async search(workspaceId, searchQuery) {
    try {
      const groups = await query(
        `SELECT * FROM tab_groups 
         WHERE workspace_id = ? AND (name LIKE ? OR description LIKE ?)
         ORDER BY sort_order ASC, created_at ASC`,
        [workspaceId, `%${searchQuery}%`, `%${searchQuery}%`]
      );
      
      return groups.map(group => new TabGroup(group));
    } catch (error) {
      console.error('Failed to search groups:', error);
      throw new AppError('Failed to search groups', 500);
    }
  }

  // 批量创建预设分组
  static async createPresetGroups(workspaceId, presetGroupsData) {
    try {
      const createdGroups = [];
      
      for (const presetData of presetGroupsData) {
        const group = await this.create({
          workspaceId,
          name: presetData.name,
          description: presetData.description,
          color: presetData.color,
          icon: presetData.icon,
          isPreset: true,
          presetType: presetData.type
        });
        
        createdGroups.push(group);
      }
      
      return createdGroups;
    } catch (error) {
      console.error('Failed to create preset groups:', error);
      throw new AppError('Failed to create preset groups', 500);
    }
  }

  // 转换为安全的JSON格式
  toJSON() {
    return {
      id: this.id,
      workspaceId: this.workspaceId,
      name: this.name,
      description: this.description,
      color: this.color,
      icon: this.icon,
      isPreset: this.isPreset,
      presetType: this.presetType,
      sortOrder: this.sortOrder,
      settings: this.settings,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = TabGroup;
