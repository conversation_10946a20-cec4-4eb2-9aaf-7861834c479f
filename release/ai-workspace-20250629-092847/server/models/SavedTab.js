const { query } = require('../config/database');
const { AppError } = require('../middleware/errorHandler');

class SavedTab {
  constructor(tabData) {
    this.id = tabData.id;
    this.userId = tabData.user_id;
    this.groupId = tabData.group_id;
    this.title = tabData.title;
    this.url = tabData.url;
    this.faviconUrl = tabData.favicon_url;
    this.description = tabData.description;
    this.tags = tabData.tags;
    this.isPinned = tabData.is_pinned;
    this.visitCount = tabData.visit_count;
    this.lastVisitedAt = tabData.last_visited_at;
    this.createdAt = tabData.created_at;
    this.updatedAt = tabData.updated_at;
  }

  // 创建新标签页
  static async create({ userId, groupId, title, url, faviconUrl, description, tags = [], isPinned = false }) {
    try {
      // 检查是否已存在相同URL的标签页
      const existingTab = await query(
        'SELECT * FROM saved_tabs WHERE user_id = ? AND url = ?',
        [userId, url]
      );

      if (existingTab.length > 0) {
        throw new AppError('Tab with this URL already exists', 409);
      }

      // 插入新标签页
      const result = await query(
        `INSERT INTO saved_tabs (user_id, group_id, title, url, favicon_url, description, tags, is_pinned) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [userId, groupId, title, url, faviconUrl, description, JSON.stringify(tags), isPinned]
      );

      // 返回新创建的标签页
      const newTab = await this.findById(result.insertId);
      return newTab;
    } catch (error) {
      console.error('Failed to create saved tab:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to create saved tab', 500);
    }
  }

  // 根据ID查找标签页
  static async findById(id) {
    try {
      const tabs = await query(
        'SELECT * FROM saved_tabs WHERE id = ?',
        [id]
      );
      
      if (tabs.length === 0) {
        return null;
      }
      
      return new SavedTab(tabs[0]);
    } catch (error) {
      console.error('Failed to find saved tab by id:', error);
      throw new AppError('Failed to find saved tab', 500);
    }
  }

  // 根据用户ID获取所有标签页
  static async findByUserId(userId, options = {}) {
    try {
      let sql = 'SELECT * FROM saved_tabs WHERE user_id = ?';
      const params = [userId];

      // 添加分组过滤
      if (options.groupId) {
        sql += ' AND group_id = ?';
        params.push(options.groupId);
      }

      // 添加固定状态过滤
      if (options.isPinned !== undefined) {
        sql += ' AND is_pinned = ?';
        params.push(options.isPinned);
      }

      // 添加搜索
      if (options.search) {
        sql += ' AND (title LIKE ? OR url LIKE ? OR description LIKE ?)';
        const searchTerm = `%${options.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // 添加排序
      const orderBy = options.orderBy || 'created_at';
      const orderDirection = options.orderDirection || 'DESC';
      sql += ` ORDER BY ${orderBy} ${orderDirection}`;

      // 添加分页
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
        
        if (options.offset) {
          sql += ' OFFSET ?';
          params.push(options.offset);
        }
      }

      const tabs = await query(sql, params);
      return tabs.map(tab => new SavedTab(tab));
    } catch (error) {
      console.error('Failed to find saved tabs by user id:', error);
      throw new AppError('Failed to find saved tabs', 500);
    }
  }

  // 根据分组ID获取标签页
  static async findByGroupId(groupId) {
    try {
      const tabs = await query(
        'SELECT * FROM saved_tabs WHERE group_id = ? ORDER BY created_at ASC',
        [groupId]
      );
      
      return tabs.map(tab => new SavedTab(tab));
    } catch (error) {
      console.error('Failed to find saved tabs by group id:', error);
      throw new AppError('Failed to find saved tabs', 500);
    }
  }

  // 更新标签页
  async update(updateData) {
    try {
      const allowedFields = ['title', 'url', 'favicon_url', 'description', 'tags', 'is_pinned', 'group_id'];
      const updates = [];
      const values = [];

      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key)) {
          updates.push(`${key} = ?`);
          if (key === 'tags') {
            values.push(JSON.stringify(value));
          } else {
            values.push(value);
          }
        }
      }

      if (updates.length === 0) {
        throw new AppError('No valid fields to update', 400);
      }

      values.push(this.id);
      await query(
        `UPDATE saved_tabs SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      // 重新获取更新后的标签页数据
      const updatedTab = await SavedTab.findById(this.id);
      Object.assign(this, updatedTab);
    } catch (error) {
      console.error('Failed to update saved tab:', error);
      throw new AppError('Failed to update saved tab', 500);
    }
  }

  // 删除标签页
  async delete() {
    try {
      await query('DELETE FROM saved_tabs WHERE id = ?', [this.id]);
    } catch (error) {
      console.error('Failed to delete saved tab:', error);
      throw new AppError('Failed to delete saved tab', 500);
    }
  }

  // 记录访问
  async recordVisit() {
    try {
      await query(
        'UPDATE saved_tabs SET visit_count = visit_count + 1, last_visited_at = CURRENT_TIMESTAMP WHERE id = ?',
        [this.id]
      );
      
      this.visitCount += 1;
      this.lastVisitedAt = new Date();
    } catch (error) {
      console.error('Failed to record visit:', error);
      throw new AppError('Failed to record visit', 500);
    }
  }

  // 切换固定状态
  async togglePin() {
    try {
      const newPinnedState = !this.isPinned;
      await query(
        'UPDATE saved_tabs SET is_pinned = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newPinnedState, this.id]
      );
      
      this.isPinned = newPinnedState;
      return newPinnedState;
    } catch (error) {
      console.error('Failed to toggle pin:', error);
      throw new AppError('Failed to toggle pin', 500);
    }
  }

  // 移动到另一个分组
  async moveToGroup(newGroupId) {
    try {
      await query(
        'UPDATE saved_tabs SET group_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newGroupId, this.id]
      );
      
      this.groupId = newGroupId;
    } catch (error) {
      console.error('Failed to move tab to group:', error);
      throw new AppError('Failed to move tab to group', 500);
    }
  }

  // 批量创建标签页
  static async createBatch(userId, groupId, tabsData) {
    try {
      const createdTabs = [];
      
      for (const tabData of tabsData) {
        try {
          const tab = await this.create({
            userId,
            groupId,
            title: tabData.title,
            url: tabData.url,
            faviconUrl: tabData.faviconUrl,
            description: tabData.description,
            tags: tabData.tags || [],
            isPinned: tabData.isPinned || false
          });
          createdTabs.push(tab);
        } catch (error) {
          // 如果是重复URL错误，跳过这个标签页
          if (error.message.includes('already exists')) {
            console.warn(`Skipping duplicate tab: ${tabData.url}`);
            continue;
          }
          throw error;
        }
      }
      
      return createdTabs;
    } catch (error) {
      console.error('Failed to create batch tabs:', error);
      throw new AppError('Failed to create batch tabs', 500);
    }
  }

  // 搜索标签页
  static async search(userId, searchQuery, options = {}) {
    try {
      let sql = `
        SELECT st.*, tg.name as group_name, w.name as workspace_name
        FROM saved_tabs st
        LEFT JOIN tab_groups tg ON st.group_id = tg.id
        LEFT JOIN workspaces w ON tg.workspace_id = w.id
        WHERE st.user_id = ? AND (
          st.title LIKE ? OR 
          st.url LIKE ? OR 
          st.description LIKE ? OR
          JSON_EXTRACT(st.tags, '$[*]') LIKE ?
        )
      `;
      
      const searchTerm = `%${searchQuery}%`;
      const params = [userId, searchTerm, searchTerm, searchTerm, searchTerm];

      // 添加分组过滤
      if (options.groupId) {
        sql += ' AND st.group_id = ?';
        params.push(options.groupId);
      }

      // 添加排序
      sql += ' ORDER BY st.last_visited_at DESC, st.created_at DESC';

      // 添加分页
      if (options.limit) {
        sql += ' LIMIT ?';
        params.push(options.limit);
      }

      const results = await query(sql, params);
      return results.map(result => ({
        ...new SavedTab(result).toJSON(),
        groupName: result.group_name,
        workspaceName: result.workspace_name
      }));
    } catch (error) {
      console.error('Failed to search saved tabs:', error);
      throw new AppError('Failed to search saved tabs', 500);
    }
  }

  // 获取热门标签页
  static async getPopular(userId, limit = 10) {
    try {
      const tabs = await query(
        'SELECT * FROM saved_tabs WHERE user_id = ? ORDER BY visit_count DESC, last_visited_at DESC LIMIT ?',
        [userId, limit]
      );
      
      return tabs.map(tab => new SavedTab(tab));
    } catch (error) {
      console.error('Failed to get popular tabs:', error);
      throw new AppError('Failed to get popular tabs', 500);
    }
  }

  // 获取最近访问的标签页
  static async getRecent(userId, limit = 10) {
    try {
      const tabs = await query(
        'SELECT * FROM saved_tabs WHERE user_id = ? AND last_visited_at IS NOT NULL ORDER BY last_visited_at DESC LIMIT ?',
        [userId, limit]
      );
      
      return tabs.map(tab => new SavedTab(tab));
    } catch (error) {
      console.error('Failed to get recent tabs:', error);
      throw new AppError('Failed to get recent tabs', 500);
    }
  }

  // 转换为安全的JSON格式
  toJSON() {
    return {
      id: this.id,
      groupId: this.groupId,
      title: this.title,
      url: this.url,
      faviconUrl: this.faviconUrl,
      description: this.description,
      tags: typeof this.tags === 'string' ? JSON.parse(this.tags) : this.tags,
      isPinned: this.isPinned,
      visitCount: this.visitCount,
      lastVisitedAt: this.lastVisitedAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = SavedTab;
