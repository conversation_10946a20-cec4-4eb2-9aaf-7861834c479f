const { query } = require('../config/database');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { AppError } = require('../middleware/errorHandler');

class User {
  constructor(userData) {
    this.id = userData.id;
    this.username = userData.username;
    this.email = userData.email;
    this.displayName = userData.display_name;
    this.avatarUrl = userData.avatar_url;
    this.settings = userData.settings;
    this.createdAt = userData.created_at;
    this.updatedAt = userData.updated_at;
    this.lastLoginAt = userData.last_login_at;
    this.isActive = userData.is_active;
  }

  // 创建新用户
  static async create({ username, email, password, displayName }) {
    // 检查用户名和邮箱是否已存在
    const existingUser = await this.findByUsernameOrEmail(username, email);
    if (existingUser) {
      throw new AppError('Username or email already exists', 409);
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 插入新用户
    const result = await query(
      `INSERT INTO users (username, email, password_hash, display_name) 
       VALUES (?, ?, ?, ?)`,
      [username, email, passwordHash, displayName || username]
    );

    // 返回新创建的用户（不包含密码）
    const newUser = await this.findById(result.insertId);
    return newUser;
  }

  // 根据ID查找用户
  static async findById(id) {
    const users = await query(
      'SELECT * FROM users WHERE id = ? AND is_active = TRUE',
      [id]
    );
    
    if (users.length === 0) {
      return null;
    }
    
    return new User(users[0]);
  }  // 根据用户名查找用户
  static async findByUsername(username) {
    const users = await query(
      'SELECT * FROM users WHERE username = ? AND is_active = TRUE',
      [username]
    );
    
    if (users.length === 0) {
      return null;
    }
    
    return new User(users[0]);
  }

  // 根据邮箱查找用户
  static async findByEmail(email) {
    const users = await query(
      'SELECT * FROM users WHERE email = ? AND is_active = TRUE',
      [email]
    );
    
    if (users.length === 0) {
      return null;
    }
    
    return new User(users[0]);
  }

  // 根据用户名或邮箱查找用户
  static async findByUsernameOrEmail(username, email) {
    const users = await query(
      'SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = TRUE',
      [username, email]
    );
    
    if (users.length === 0) {
      return null;
    }
    
    return new User(users[0]);
  }

  // 验证密码
  static async validatePassword(user, password) {
    const userData = await query(
      'SELECT password_hash FROM users WHERE id = ?',
      [user.id]
    );
    
    if (userData.length === 0) {
      return false;
    }
    
    return await bcrypt.compare(password, userData[0].password_hash);
  }  // 更新最后登录时间
  async updateLastLogin() {
    await query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?',
      [this.id]
    );
    this.lastLoginAt = new Date();
  }

  // 更新用户信息
  async update(updateData) {
    const allowedFields = ['display_name', 'avatar_url', 'settings'];
    const updates = [];
    const values = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        values.push(typeof value === 'object' ? JSON.stringify(value) : value);
      }
    }

    if (updates.length === 0) {
      throw new AppError('No valid fields to update', 400);
    }

    values.push(this.id);
    await query(
      `UPDATE users SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      values
    );

    // 重新获取更新后的用户数据
    const updatedUser = await User.findById(this.id);
    Object.assign(this, updatedUser);
  }

  // 生成JWT token
  generateToken() {
    const payload = {
      id: this.id,
      username: this.username,
      email: this.email
    };

    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });
  }

  // 验证JWT token
  static verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new AppError('Invalid or expired token', 401);
    }
  }

  // 转换为安全的JSON格式（不包含敏感信息）
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      displayName: this.displayName,
      avatarUrl: this.avatarUrl,
      settings: this.settings,
      createdAt: this.createdAt,
      lastLoginAt: this.lastLoginAt
    };
  }
}

module.exports = User;