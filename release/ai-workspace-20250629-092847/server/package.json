{"name": "ai-workspace-server", "version": "1.0.0", "description": "AI工作台后端API服务", "main": "app.js", "scripts": {"dev": "nodemon app.js", "start": "node app.js", "build": "echo 'No build step needed for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.9.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.4.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.3", "jest": "^29.7.0", "supertest": "^6.3.4", "eslint": "^8.57.0"}, "engines": {"node": ">=16.0.0"}}