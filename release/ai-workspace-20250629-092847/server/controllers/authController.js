const User = require('../models/User');
const { query } = require('../config/database');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const bcrypt = require('bcryptjs');// 用户注册
const register = async (req, res, next) => {
  try {
    const { username, email, password, displayName } = req.body;
    
    logger.info('User registration attempt', { username, email });
    
    // 创建新用户
    const user = await User.create({
      username,
      email,
      password,
      displayName
    });
    
    // 生成JWT token
    const token = user.generateToken();
    
    // 更新最后登录时间
    await user.updateLastLogin();
    
    logger.info('User registered successfully', { 
      userId: user.id, 
      username: user.username 
    });
    
    res.status(201).json({
      status: 'success',
      message: 'User registered successfully',
      data: {
        user: user.toJSON(),
        token
      }
    });
  } catch (error) {
    logger.error('Registration failed', { 
      error: error.message,
      username: req.body.username 
    });
    next(error);
  }
};

// 用户登录
const login = async (req, res, next) => {
  try {
    const { username, password } = req.body;
    
    logger.info('User login attempt', { username });
    
    // 查找用户（支持用户名或邮箱登录）
    let user = await User.findByUsername(username);
    if (!user) {
      user = await User.findByEmail(username);
    }
    
    if (!user) {
      logger.warn('Login failed - user not found', { username });
      return next(new AppError('Invalid username or password', 401));
    }
    
    // 验证密码
    const isPasswordValid = await User.validatePassword(user, password);
    if (!isPasswordValid) {
      logger.warn('Login failed - invalid password', { 
        userId: user.id, 
        username: user.username 
      });
      return next(new AppError('Invalid username or password', 401));
    }
    
    // 生成JWT token
    const token = user.generateToken();
    
    // 更新最后登录时间
    await user.updateLastLogin();
    
    logger.info('User logged in successfully', { 
      userId: user.id, 
      username: user.username 
    });
    
    res.json({
      status: 'success',
      message: 'Login successful',
      data: {
        user: user.toJSON(),
        token
      }
    });
  } catch (error) {
    logger.error('Login failed', { 
      error: error.message,
      username: req.body.username 
    });
    next(error);
  }
};// 获取当前用户信息
const getProfile = async (req, res, next) => {
  try {
    const user = req.user;
    
    res.json({
      status: 'success',
      data: {
        user: user.toJSON()
      }
    });
  } catch (error) {
    logger.error('Get profile failed', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

// 更新用户信息
const updateProfile = async (req, res, next) => {
  try {
    const user = req.user;
    const updateData = req.body;
    
    logger.info('User profile update attempt', { 
      userId: user.id,
      updateFields: Object.keys(updateData)
    });
    
    await user.update(updateData);
    
    logger.info('User profile updated successfully', { 
      userId: user.id 
    });
    
    res.json({
      status: 'success',
      message: 'Profile updated successfully',
      data: {
        user: user.toJSON()
      }
    });
  } catch (error) {
    logger.error('Profile update failed', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

// 修改密码
const changePassword = async (req, res, next) => {
  try {
    const user = req.user;
    const { currentPassword, newPassword } = req.body;
    
    logger.info('Password change attempt', { userId: user.id });
    
    // 验证当前密码
    const isCurrentPasswordValid = await User.validatePassword(user, currentPassword);
    if (!isCurrentPasswordValid) {
      logger.warn('Password change failed - invalid current password', { 
        userId: user.id 
      });
      return next(new AppError('Current password is incorrect', 400));
    }
    
    // 加密新密码
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
    
    // 更新密码
    await query(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newPasswordHash, user.id]
    );
    
    logger.info('Password changed successfully', { userId: user.id });
    
    res.json({
      status: 'success',
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Password change failed', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};// 登出
const logout = async (req, res, next) => {
  try {
    const user = req.user;
    
    logger.info('User logout', { userId: user.id });
    
    res.json({
      status: 'success',
      message: 'Logout successful'
    });
  } catch (error) {
    logger.error('Logout failed', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

// 验证token有效性
const verifyToken = async (req, res, next) => {
  try {
    const user = req.user;
    
    res.json({
      status: 'success',
      message: 'Token is valid',
      data: {
        user: user.toJSON()
      }
    });
  } catch (error) {
    logger.error('Token verification failed', { 
      error: error.message 
    });
    next(error);
  }
};

// 刷新token
const refreshToken = async (req, res, next) => {
  try {
    const user = req.user;
    
    // 生成新的token
    const newToken = user.generateToken();
    
    logger.info('Token refreshed', { userId: user.id });
    
    res.json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        user: user.toJSON(),
        token: newToken
      }
    });
  } catch (error) {
    logger.error('Token refresh failed', { 
      error: error.message,
      userId: req.user?.id 
    });
    next(error);
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  logout,
  verifyToken,
  refreshToken
};