const TabGroup = require('../models/TabGroup');
const Workspace = require('../models/Workspace');
const SavedTab = require('../models/SavedTab');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// 获取工作空间的所有分组
const getTabGroups = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.workspaceId;
    
    logger.info('Getting tab groups', { userId, workspaceId });
    
    // 验证工作空间权限
    const workspace = await Workspace.findById(workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Workspace not found or access denied', 404));
    }
    
    const groups = await TabGroup.findByWorkspaceId(workspaceId);
    
    // 为每个分组获取标签页和统计信息
    const groupsWithDetails = await Promise.all(
      groups.map(async (group) => {
        const tabs = await group.getTabs();
        const stats = await group.getStats();
        return {
          ...group.toJSON(),
          tabs: tabs.map(tab => new SavedTab(tab).toJSON()),
          stats
        };
      })
    );
    
    res.json({
      status: 'success',
      data: {
        groups: groupsWithDetails
      }
    });
  } catch (error) {
    logger.error('Failed to get tab groups', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.workspaceId
    });
    next(error);
  }
};

// 获取单个分组详情
const getTabGroup = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const groupId = req.params.id;
    
    logger.info('Getting tab group details', { userId, groupId });
    
    const group = await TabGroup.findById(groupId);
    
    if (!group) {
      return next(new AppError('Tab group not found', 404));
    }
    
    // 验证权限
    const workspace = await Workspace.findById(group.workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    const tabs = await group.getTabs();
    const stats = await group.getStats();
    
    res.json({
      status: 'success',
      data: {
        group: {
          ...group.toJSON(),
          tabs: tabs.map(tab => new SavedTab(tab).toJSON()),
          stats
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get tab group', { 
      error: error.message,
      userId: req.user?.id,
      groupId: req.params.id
    });
    next(error);
  }
};

// 创建新分组
const createTabGroup = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.workspaceId;
    const { name, description, color, icon, isPreset, presetType } = req.body;
    
    logger.info('Creating tab group', { userId, workspaceId, name });
    
    // 验证工作空间权限
    const workspace = await Workspace.findById(workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Workspace not found or access denied', 404));
    }
    
    const group = await TabGroup.create({
      workspaceId,
      name,
      description,
      color,
      icon,
      isPreset,
      presetType
    });
    
    logger.info('Tab group created successfully', { 
      userId, 
      workspaceId,
      groupId: group.id,
      name: group.name
    });
    
    res.status(201).json({
      status: 'success',
      message: 'Tab group created successfully',
      data: {
        group: group.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to create tab group', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.workspaceId
    });
    next(error);
  }
};

// 更新分组
const updateTabGroup = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const groupId = req.params.id;
    const updateData = req.body;
    
    logger.info('Updating tab group', { userId, groupId, updateData });
    
    const group = await TabGroup.findById(groupId);
    
    if (!group) {
      return next(new AppError('Tab group not found', 404));
    }
    
    // 验证权限
    const workspace = await Workspace.findById(group.workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    await group.update(updateData);
    
    logger.info('Tab group updated successfully', { 
      userId, 
      groupId: group.id 
    });
    
    res.json({
      status: 'success',
      message: 'Tab group updated successfully',
      data: {
        group: group.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to update tab group', { 
      error: error.message,
      userId: req.user?.id,
      groupId: req.params.id
    });
    next(error);
  }
};

// 删除分组
const deleteTabGroup = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const groupId = req.params.id;
    
    logger.info('Deleting tab group', { userId, groupId });
    
    const group = await TabGroup.findById(groupId);
    
    if (!group) {
      return next(new AppError('Tab group not found', 404));
    }
    
    // 验证权限
    const workspace = await Workspace.findById(group.workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    await group.delete();
    
    logger.info('Tab group deleted successfully', { 
      userId, 
      groupId 
    });
    
    res.json({
      status: 'success',
      message: 'Tab group deleted successfully'
    });
  } catch (error) {
    logger.error('Failed to delete tab group', { 
      error: error.message,
      userId: req.user?.id,
      groupId: req.params.id
    });
    next(error);
  }
};

// 复制分组
const duplicateTabGroup = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const groupId = req.params.id;
    const { name, targetWorkspaceId } = req.body;
    
    logger.info('Duplicating tab group', { userId, groupId, newName: name, targetWorkspaceId });
    
    const group = await TabGroup.findById(groupId);
    
    if (!group) {
      return next(new AppError('Tab group not found', 404));
    }
    
    // 验证源工作空间权限
    const sourceWorkspace = await Workspace.findById(group.workspaceId);
    if (!sourceWorkspace || sourceWorkspace.userId !== userId) {
      return next(new AppError('Access denied', 403));
    }
    
    // 如果指定了目标工作空间，验证权限
    if (targetWorkspaceId) {
      const targetWorkspace = await Workspace.findById(targetWorkspaceId);
      if (!targetWorkspace || targetWorkspace.userId !== userId) {
        return next(new AppError('Target workspace not found or access denied', 404));
      }
    }
    
    const newGroup = await group.duplicate(name, targetWorkspaceId);
    
    logger.info('Tab group duplicated successfully', { 
      userId, 
      originalGroupId: groupId,
      newGroupId: newGroup.id
    });
    
    res.status(201).json({
      status: 'success',
      message: 'Tab group duplicated successfully',
      data: {
        group: newGroup.toJSON()
      }
    });
  } catch (error) {
    logger.error('Failed to duplicate tab group', { 
      error: error.message,
      userId: req.user?.id,
      groupId: req.params.id
    });
    next(error);
  }
};

// 更新分组排序
const updateTabGroupOrder = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.workspaceId;
    const { groupOrders } = req.body;
    
    logger.info('Updating tab group order', { userId, workspaceId, groupOrders });
    
    // 验证工作空间权限
    const workspace = await Workspace.findById(workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Workspace not found or access denied', 404));
    }
    
    await TabGroup.updateSortOrder(workspaceId, groupOrders);
    
    logger.info('Tab group order updated successfully', { userId, workspaceId });
    
    res.json({
      status: 'success',
      message: 'Tab group order updated successfully'
    });
  } catch (error) {
    logger.error('Failed to update tab group order', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.workspaceId
    });
    next(error);
  }
};

// 创建预设分组
const createPresetGroups = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const workspaceId = req.params.workspaceId;
    const { presetGroups } = req.body;
    
    logger.info('Creating preset groups', { userId, workspaceId, groupCount: presetGroups.length });
    
    // 验证工作空间权限
    const workspace = await Workspace.findById(workspaceId);
    if (!workspace || workspace.userId !== userId) {
      return next(new AppError('Workspace not found or access denied', 404));
    }
    
    const createdGroups = await TabGroup.createPresetGroups(workspaceId, presetGroups);
    
    logger.info('Preset groups created successfully', { 
      userId, 
      workspaceId,
      createdCount: createdGroups.length
    });
    
    res.status(201).json({
      status: 'success',
      message: 'Preset groups created successfully',
      data: {
        groups: createdGroups.map(group => group.toJSON())
      }
    });
  } catch (error) {
    logger.error('Failed to create preset groups', { 
      error: error.message,
      userId: req.user?.id,
      workspaceId: req.params.workspaceId
    });
    next(error);
  }
};

module.exports = {
  getTabGroups,
  getTabGroup,
  createTabGroup,
  updateTabGroup,
  deleteTabGroup,
  duplicateTabGroup,
  updateTabGroupOrder,
  createPresetGroups
};
