const express = require('express');
const router = express.Router();

const workspaceController = require('../controllers/workspaceController');
const tabGroupController = require('../controllers/tabGroupController');
const { authenticate } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticate);

// 工作空间路由
router.get('/', workspaceController.getWorkspaces);
router.get('/default', workspaceController.getDefaultWorkspace);
router.get('/search', workspaceController.searchWorkspaces);
router.post('/', workspaceController.createWorkspace);
router.put('/order', workspaceController.updateWorkspaceOrder);

router.get('/:id', workspaceController.getWorkspace);
router.put('/:id', workspaceController.updateWorkspace);
router.delete('/:id', workspaceController.deleteWorkspace);
router.post('/:id/duplicate', workspaceController.duplicateWorkspace);

// 标签页分组路由
router.get('/:workspaceId/groups', tabGroupController.getTabGroups);
router.post('/:workspaceId/groups', tabGroupController.createTabGroup);
router.post('/:workspaceId/groups/preset', tabGroupController.createPresetGroups);
router.put('/:workspaceId/groups/order', tabGroupController.updateTabGroupOrder);

router.get('/groups/:id', tabGroupController.getTabGroup);
router.put('/groups/:id', tabGroupController.updateTabGroup);
router.delete('/groups/:id', tabGroupController.deleteTabGroup);
router.post('/groups/:id/duplicate', tabGroupController.duplicateTabGroup);

module.exports = router;