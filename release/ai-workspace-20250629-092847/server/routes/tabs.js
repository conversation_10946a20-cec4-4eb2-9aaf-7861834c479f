const express = require('express');
const router = express.Router();

const SavedTab = require('../models/SavedTab');
const TabGroup = require('../models/TabGroup');
const Workspace = require('../models/Workspace');
const { AppError } = require('../middleware/errorHandler');
const { authenticate } = require('../middleware/auth');
const logger = require('../utils/logger');

// 所有路由都需要认证
router.use(authenticate);

// 获取用户的所有标签页
router.get('/', async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { groupId, isPinned, search, orderBy, orderDirection, limit, offset } = req.query;

    const options = {
      groupId: groupId ? parseInt(groupId) : undefined,
      isPinned: isPinned !== undefined ? isPinned === 'true' : undefined,
      search,
      orderBy,
      orderDirection,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined
    };

    const tabs = await SavedTab.findByUserId(userId, options);

    res.json({
      status: 'success',
      data: {
        tabs: tabs.map(tab => tab.toJSON())
      }
    });
  } catch (error) {
    logger.error('Failed to get tabs', { error: error.message, userId: req.user?.id });
    next(error);
  }
});

module.exports = router;