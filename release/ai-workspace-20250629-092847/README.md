# AI工作台部署包

## 包含内容

- `chrome-extension/` - Chrome扩展文件
- `server/` - 后端服务文件
- `database/` - 数据库初始化文件

## 部署步骤

### 1. 部署Chrome扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 目录

### 2. 部署后端服务

1. 进入 `server` 目录
2. 复制 `.env.example` 为 `.env` 并配置环境变量
3. 运行 `npm install` 安装依赖
4. 运行 `npm start` 启动服务

### 3. 初始化数据库

1. 创建MySQL数据库
2. 执行 `database/init.sql` 初始化表结构
3. 执行 `database/seed.sql` 插入初始数据（可选）

## 配置说明

### 环境变量

- `DB_HOST` - 数据库主机地址
- `DB_PORT` - 数据库端口
- `DB_NAME` - 数据库名称
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `JWT_SECRET` - JWT密钥
- `PORT` - 服务端口（默认3001）

### Chrome扩展配置

扩展安装后会自动连接到本地服务器（http://localhost:3001）。
如需连接到其他服务器，请在扩展设置中修改服务器地址。

## 故障排除

1. **扩展无法加载**: 检查manifest.json格式是否正确
2. **服务器启动失败**: 检查端口是否被占用，环境变量是否配置正确
3. **数据库连接失败**: 检查数据库服务是否启动，连接参数是否正确

## 技术支持

如遇问题，请查看日志文件或联系技术支持。
