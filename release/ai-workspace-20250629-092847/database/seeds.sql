-- AI工作台预设数据
USE ai_workspace;

-- 创建默认用户（用于演示）
INSERT INTO users (username, email, password_hash, display_name) VALUES
('demo_user', '<EMAIL>', '$2a$10$example_hash_here', '演示用户');

-- 获取演示用户ID
SET @demo_user_id = LAST_INSERT_ID();

-- 创建默认工作空间
INSERT INTO workspaces (user_id, name, description, color, icon, is_default) VALUES
(@demo_user_id, 'AI工作台', '默认的AI工具工作空间', '#3B82F6', 'workspace', TRUE),
(@demo_user_id, '技术学习', '技术论坛和学习资源', '#10B981', 'book', FALSE),
(@demo_user_id, '日常办公', '日常工作和协作工具', '#F59E0B', 'briefcase', FALSE);

-- 获取工作空间ID
SET @ai_workspace_id = (SELECT id FROM workspaces WHERE name = 'AI工作台' AND user_id = @demo_user_id);
SET @tech_workspace_id = (SELECT id FROM workspaces WHERE name = '技术学习' AND user_id = @demo_user_id);
SET @office_workspace_id = (SELECT id FROM workspaces WHERE name = '日常办公' AND user_id = @demo_user_id);

-- 创建预设标签页分组
INSERT INTO tab_groups (workspace_id, name, description, color, icon, is_preset, preset_type, sort_order) VALUES
-- AI工作台分组
(@ai_workspace_id, 'AI工作主力', 'ChatGPT、Gemini、LobeHub等主力AI工具', '#EF4444', 'star', TRUE, 'ai_primary', 1),
(@ai_workspace_id, 'AI次选', 'DeepAsk、GPTFun、Claude等次选AI工具', '#F97316', 'cpu', TRUE, 'ai_secondary', 2),
(@ai_workspace_id, 'AI其他工具', 'Dify、提示词优化等辅助工具', '#8B5CF6', 'tool', TRUE, 'ai_tools', 3),-- 技术学习分组
(@tech_workspace_id, '技术论坛', 'Linux.do、NodeLoc等技术社区', '#06B6D4', 'chat', TRUE, 'tech_forums', 1),
(@tech_workspace_id, '开发资源', '开发文档和资源网站', '#84CC16', 'code', TRUE, 'dev_resources', 2),

-- 日常办公分组
(@office_workspace_id, '协作工具', '语雀、飞书等协作平台', '#EC4899', 'users', TRUE, 'collaboration', 1),
(@office_workspace_id, '效率工具', '其他提升效率的工具', '#6366F1', 'zap', TRUE, 'productivity', 2);

-- 获取分组ID
SET @ai_primary_group = (SELECT id FROM tab_groups WHERE preset_type = 'ai_primary');
SET @ai_secondary_group = (SELECT id FROM tab_groups WHERE preset_type = 'ai_secondary');
SET @ai_tools_group = (SELECT id FROM tab_groups WHERE preset_type = 'ai_tools');
SET @tech_forums_group = (SELECT id FROM tab_groups WHERE preset_type = 'tech_forums');
SET @dev_resources_group = (SELECT id FROM tab_groups WHERE preset_type = 'dev_resources');
SET @collaboration_group = (SELECT id FROM tab_groups WHERE preset_type = 'collaboration');
SET @productivity_group = (SELECT id FROM tab_groups WHERE preset_type = 'productivity');

-- 插入预设网站数据
INSERT INTO saved_tabs (user_id, group_id, title, url, description, tags) VALUES
-- AI工作主力
(@demo_user_id, @ai_primary_group, 'ChatGPT', 'https://chat.openai.com/', 'OpenAI的ChatGPT对话AI', '["ai", "chat", "openai"]'),
(@demo_user_id, @ai_primary_group, 'Gemini', 'https://gemini.google.com/', 'Google的Gemini AI助手', '["ai", "google", "gemini"]'),
(@demo_user_id, @ai_primary_group, 'LobeHub', 'https://chat-preview.lobehub.com/discover', 'LobeHub AI聊天平台', '["ai", "chat", "lobehub"]'),
(@demo_user_id, @ai_primary_group, 'Perplexity', 'https://www.perplexity.ai/', 'Perplexity AI搜索引擎', '["ai", "search", "perplexity"]'),
(@demo_user_id, @ai_primary_group, 'Grok', 'https://grok.x.ai/', 'xAI的Grok AI助手', '["ai", "grok", "xai"]'),
(@demo_user_id, @ai_primary_group, 'AI Studio', 'https://aistudio.google.com/', 'Google AI Studio开发平台', '["ai", "google", "development"]'),-- AI次选
(@demo_user_id, @ai_secondary_group, 'DeepAsk', 'https://deepask.cc/', 'DeepAsk AI问答平台', '["ai", "qa", "deepask"]'),
(@demo_user_id, @ai_secondary_group, 'GPTFun', 'https://fun4ai.khthink.cn/login', 'GPTFun AI娱乐平台', '["ai", "fun", "gpt"]'),
(@demo_user_id, @ai_secondary_group, 'C佬', 'https://new.clivia.fun/', 'C佬AI助手', '["ai", "assistant"]'),
(@demo_user_id, @ai_secondary_group, 'A佬', 'https://aabao.eu.cc/', 'A佬AI工具', '["ai", "tools"]'),
(@demo_user_id, @ai_secondary_group, 'H佬', 'https://work.haomo.de/', 'H佬工作助手', '["ai", "work"]'),
(@demo_user_id, @ai_secondary_group, 'Claude', 'https://demo.fuclaude.com/', 'Anthropic Claude AI', '["ai", "claude", "anthropic"]'),

-- AI其他工具
(@demo_user_id, @ai_tools_group, 'Dify', 'https://dify.ai/', 'Dify AI应用开发平台', '["ai", "development", "dify"]'),
(@demo_user_id, @ai_tools_group, '提示词优化', 'https://promptpilot.volcengine.com/home', '火山引擎提示词优化工具', '["ai", "prompt", "optimization"]'),

-- 技术论坛
(@demo_user_id, @tech_forums_group, 'Linux.do', 'https://linux.do/', 'Linux技术社区', '["linux", "community", "tech"]'),
(@demo_user_id, @tech_forums_group, 'NodeLoc', 'https://nodeloc.cc/', 'NodeLoc技术论坛', '["tech", "forum", "node"]'),
(@demo_user_id, @tech_forums_group, 'NodeSeek', 'https://www.nodeseek.com/', 'NodeSeek技术分享', '["tech", "sharing", "node"]'),
(@demo_user_id, @tech_forums_group, '小众软件', 'https://meta.appinn.net/latest', '小众软件分享社区', '["software", "tools", "community"]'),
(@demo_user_id, @tech_forums_group, 'Follow', 'https://app.follow.is/', 'Follow信息聚合工具', '["rss", "news", "aggregation"]'),

-- 协作工具
(@demo_user_id, @collaboration_group, '语雀', 'https://www.yuque.com/', '阿里巴巴语雀知识库', '["docs", "knowledge", "collaboration"]'),
(@demo_user_id, @collaboration_group, '飞书', 'https://p1b9rnchwd.feishu.cn/drive/home/', '字节跳动飞书协作平台', '["collaboration", "office", "feishu"]');