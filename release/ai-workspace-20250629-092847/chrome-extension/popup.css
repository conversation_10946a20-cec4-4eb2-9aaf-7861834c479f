/* AI工作台 Popup 样式 */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #fff;
}

.popup-container {
  width: 400px;
  min-height: 500px;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-title h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.header-title .version {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
}

.nav-tab {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 2px solid transparent;
}

.nav-tab:hover {
  background: #f1f5f9;
  color: #374151;
}

.nav-tab.active {
  background: white;
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}/* 搜索栏 */
.search-bar {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6b7280;
  font-size: 14px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-button {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.clear-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 内容区域 */
.content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 工作空间列表 */
.workspace-list h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.workspace-items {
  margin-bottom: 24px;
}

.workspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.workspace-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.workspace-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.workspace-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.workspace-icon {
  font-size: 18px;
}

.workspace-details {
  display: flex;
  flex-direction: column;
}

.workspace-name {
  font-weight: 500;
  color: #374151;
}

.workspace-count {
  font-size: 12px;
  color: #6b7280;
}

.workspace-actions {
  display: flex;
  gap: 4px;
}/* 标签页列表 */
.tab-list h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.tab-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.tab-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-item.active {
  border-color: #10b981;
  background: #ecfdf5;
}

.tab-item.pinned {
  border-left: 3px solid #f59e0b;
}

.tab-favicon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tab-favicon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.tab-info {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.tab-url {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.pin-indicator {
  font-size: 12px;
  color: #f59e0b;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: all 0.2s;
}

.tab-item:hover .close-button {
  opacity: 1;
}

.close-button:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 高亮搜索结果 */
.highlight {
  background: #fef3c7;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #6b7280;
}

.empty-state p {
  margin-bottom: 16px;
}

/* 滚动条样式 */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式调整 */
@media (max-height: 600px) {
  .popup-container {
    max-height: 500px;
  }
}

/* 标签页管理面板 */
.tab-management-panel {
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* 操作进度 */
.operation-progress {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e0f2fe;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0ea5e9;
  transition: width 0.3s ease;
}

.progress-success {
  color: #059669;
  font-size: 12px;
  margin-top: 4px;
}

.progress-error {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

/* 批量操作工具栏 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  margin-bottom: 16px;
}

.selection-info label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.btn-danger {
  background: #dc2626;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

/* 智能工具 */
.smart-tools {
  margin-bottom: 16px;
}

.smart-tools h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
}

.smart-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 智能建议 */
.smart-suggestions {
  background: #fefce8;
  border: 1px solid #eab308;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.smart-suggestions h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #92400e;
}

.smart-suggestions h6 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #78716c;
  font-weight: 600;
}

.category-stats, .grouping-suggestions, .duplicate-detection {
  margin-bottom: 12px;
}

.category-stat, .suggestion-item, .duplicate-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid #f3f4f6;
}

.category-stat:last-child, .suggestion-item:last-child, .duplicate-item:last-child {
  border-bottom: none;
}

.category-icon, .suggestion-icon {
  font-size: 16px;
}

.category-name, .suggestion-text {
  flex: 1;
  font-size: 12px;
  color: #374151;
}

.category-count {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.duplicate-count {
  background: #fee2e2;
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.duplicate-url {
  flex: 1;
  font-size: 12px;
  color: #374151;
  font-family: monospace;
}

/* 可选择的标签页列表 */
.tab-list-with-selection {
  max-height: 300px;
  overflow-y: auto;
}

.tab-item-selectable {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 4px;
  transition: all 0.2s;
}

.tab-item-selectable:hover {
  background: #f8fafc;
  border-color: #3b82f6;
}

.tab-checkbox {
  display: flex;
  align-items: center;
}

.tab-checkbox input {
  margin: 0;
}

.tab-badges {
  display: flex;
  gap: 4px;
}

.badge {
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.badge-pin {
  background: #fef3c7;
  color: #92400e;
}

.badge-active {
  background: #dcfce7;
  color: #166534;
}

.badge-grouped {
  background: #dbeafe;
  color: #1d4ed8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 预设分组面板 */
.preset-groups-panel {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.panel-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.panel-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

/* 检测到的网站 */
.detected-websites {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 20px;
}

.detected-websites h4 {
  margin: 0 0 12px 0;
  color: #0c4a6e;
  font-size: 14px;
}

.detected-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #e0f2fe;
}

.detected-group:last-child {
  border-bottom: none;
}

.detected-count {
  background: #0ea5e9;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

/* 预设分组列表 */
.preset-groups-list h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 14px;
}

.preset-group-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s;
}

.preset-group-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background: white;
}

.group-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.group-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.group-details h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #374151;
}

.group-details p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.group-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #9ca3af;
}

.group-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

/* 网站预览 */
.websites-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.website-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 11px;
}

.website-favicon {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.website-name {
  color: #374151;
  font-weight: 500;
}

.more-websites {
  color: #6b7280;
  font-size: 11px;
  font-style: italic;
}

/* 推荐标识 */
.recommendation-badge {
  background: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  font-size: 11px;
  text-align: center;
  border-top: 1px solid #fbbf24;
}

/* 使用提示 */
.usage-tips {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-top: 20px;
}

.usage-tips h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.usage-tips ul {
  margin: 0;
  padding-left: 16px;
  color: #6b7280;
  font-size: 12px;
  line-height: 1.5;
}

.usage-tips li {
  margin-bottom: 4px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

/* 收藏面板 */
.favorites-panel {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.panel-header h3 {
  margin: 0;
  color: #374151;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 添加收藏表单 */
.add-favorite-form {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.category-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 快速访问 */
.quick-access-section {
  margin-bottom: 20px;
}

.quick-access-section h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.quick-access-item:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.quick-favicon {
  width: 16px;
  height: 16px;
  margin-bottom: 4px;
}

.quick-title {
  font-size: 11px;
  color: #374151;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.visit-count {
  font-size: 10px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 1px 4px;
  border-radius: 3px;
  margin-top: 2px;
}

/* 搜索过滤 */
.search-filter-section {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.category-filter {
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

/* 收藏列表 */
.favorites-list {
  max-height: 300px;
  overflow-y: auto;
}

.favorite-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.2s;
}

.favorite-item:hover {
  background: #f8fafc;
  border-color: #3b82f6;
}

.favorite-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.favorite-favicon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.favorite-details {
  flex: 1;
  min-width: 0;
}

.favorite-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.favorite-url {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.favorite-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.category-tag {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.visit-count, .last-visited {
  font-size: 11px;
  color: #9ca3af;
}

.favorite-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

/* 统计信息 */
.favorites-stats {
  display: flex;
  justify-content: space-around;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 2px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

/* 搜索面板 */
.search-panel {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.search-header h3 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

/* 搜索输入容器 */
.search-input-container {
  position: relative;
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.suggestion-item:hover {
  background: #f8fafc;
}

.suggestion-icon {
  color: #6b7280;
  font-size: 12px;
}

.suggestion-text {
  color: #374151;
  font-size: 14px;
}

/* 搜索过滤器 */
.search-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.filter-select, .sort-select {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.sort-order-btn {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
}

.sort-order-btn:hover {
  background: #f8fafc;
}

/* 搜索结果 */
.search-results {
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.search-result-item:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.result-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-favicon {
  width: 16px;
  height: 16px;
}

.result-type-icon {
  font-size: 16px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-url {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 4px;
}

.result-type {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.result-workspace, .result-group {
  font-size: 10px;
  color: #6b7280;
}

.result-score {
  font-size: 10px;
  color: #9ca3af;
}

.matched-fields {
  font-size: 10px;
  color: #059669;
  background: #ecfdf5;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

/* 搜索历史 */
.search-history {
  margin-top: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header h4 {
  margin: 0;
  color: #374151;
  font-size: 14px;
}

.history-list {
  max-height: 150px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s;
}

.history-item:hover {
  background: #f8fafc;
}

.history-icon {
  color: #6b7280;
  font-size: 12px;
}

.history-text {
  color: #374151;
  font-size: 12px;
}

/* 搜索提示 */
.search-tips {
  margin-top: 20px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.search-tips h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 14px;
}

.search-tips ul {
  margin: 0;
  padding-left: 16px;
  color: #6b7280;
  font-size: 12px;
  line-height: 1.5;
}

.search-tips li {
  margin-bottom: 4px;
}

/* 加载和空状态 */
.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
  text-align: center;
}

.empty-hint {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.tab-item, .workspace-item, .tab-item-selectable, .preset-group-item, .favorite-item, .search-result-item {
  animation: fadeIn 0.3s ease-out;
}
