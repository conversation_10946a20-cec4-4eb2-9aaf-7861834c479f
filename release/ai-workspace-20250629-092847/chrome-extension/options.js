(()=>{"use strict";var e,t={639:(e,t,r)=>{var s=r(540),o=r(338);class n{static STORAGE_KEYS={WORKSPACES:"workspaces",ACTIVE_WORKSPACE:"activeWorkspace",USER_SETTINGS:"userSettings",SYNC_DATA:"syncData",AUTH_TOKEN:"authToken"};static async getWorkspaces(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.WORKSPACES]))[this.STORAGE_KEYS.WORKSPACES]||[]}catch(e){return console.error("Failed to get workspaces:",e),[]}}static async getWorkspace(e){try{return(await this.getWorkspaces()).find(t=>t.id===e)||null}catch(e){return console.error("Failed to get workspace:",e),null}}static async saveWorkspace(e){try{const t=await this.getWorkspaces(),r=t.findIndex(t=>t.id===e.id);return r>=0?t[r]=e:t.push(e),await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:t}),!0}catch(e){return console.error("Failed to save workspace:",e),!1}}static async deleteWorkspace(e){try{const t=(await this.getWorkspaces()).filter(t=>t.id!==e);return await chrome.storage.sync.set({[this.STORAGE_KEYS.WORKSPACES]:t}),!0}catch(e){return console.error("Failed to delete workspace:",e),!1}}static async getActiveWorkspace(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.ACTIVE_WORKSPACE]))[this.STORAGE_KEYS.ACTIVE_WORKSPACE]||null}catch(e){return console.error("Failed to get active workspace:",e),null}}static async setActiveWorkspace(e){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.ACTIVE_WORKSPACE]:e}),!0}catch(e){return console.error("Failed to set active workspace:",e),!1}}static async saveTabsToWorkspace(e,t){try{const r=await this.getWorkspace(t);if(!r)throw new Error("Workspace not found");const s={id:`group_${Date.now()}`,name:`保存的标签页 ${(new Date).toLocaleString()}`,color:"#3B82F6",icon:"bookmark",tabs:e,sortOrder:r.groups.length};return r.groups.push(s),r.updatedAt=(new Date).toISOString(),await this.saveWorkspace(r)}catch(e){return console.error("Failed to save tabs to workspace:",e),!1}}static async getUserSettings(){try{return(await chrome.storage.sync.get([this.STORAGE_KEYS.USER_SETTINGS]))[this.STORAGE_KEYS.USER_SETTINGS]||{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}catch(e){return console.error("Failed to get user settings:",e),{theme:"auto",autoSave:!0,syncEnabled:!0,shortcuts:{toggleWorkspace:"Ctrl+Shift+W",quickSearch:"Ctrl+Shift+F"}}}}static async saveUserSettings(e){try{return await chrome.storage.sync.set({[this.STORAGE_KEYS.USER_SETTINGS]:e}),!0}catch(e){return console.error("Failed to save user settings:",e),!1}}static async getAuthToken(){try{return(await chrome.storage.local.get([this.STORAGE_KEYS.AUTH_TOKEN]))[this.STORAGE_KEYS.AUTH_TOKEN]||null}catch(e){return console.error("Failed to get auth token:",e),null}}static async saveAuthToken(e){try{return await chrome.storage.local.set({[this.STORAGE_KEYS.AUTH_TOKEN]:e}),!0}catch(e){return console.error("Failed to save auth token:",e),!1}}static async clearAuthToken(){try{return await chrome.storage.local.remove([this.STORAGE_KEYS.AUTH_TOKEN]),!0}catch(e){return console.error("Failed to clear auth token:",e),!1}}static async clearAllData(){try{return await chrome.storage.sync.clear(),await chrome.storage.local.clear(),!0}catch(e){return console.error("Failed to clear all data:",e),!1}}}var a=r(848);const i=()=>{const[e,t]=(0,s.useState)([]),[r,o]=(0,s.useState)(null),[i,c]=(0,s.useState)("workspaces"),[l,d]=(0,s.useState)(!0);(0,s.useEffect)(()=>{p()},[]);const p=async()=>{try{d(!0);const[e,r]=await Promise.all([n.getWorkspaces(),n.getUserSettings()]);t(e),o(r)}catch(e){console.error("Failed to load data:",e)}finally{d(!1)}};return l?(0,a.jsx)("div",{style:{padding:"48px",textAlign:"center"},children:(0,a.jsx)("div",{children:"加载中..."})}):(0,a.jsxs)("div",{style:{display:"flex",minHeight:"600px"},children:[(0,a.jsx)("div",{style:{width:"240px",background:"#f8fafc",borderRight:"1px solid #e5e7eb",padding:"24px 0"},children:(0,a.jsxs)("nav",{children:[(0,a.jsx)("button",{onClick:()=>c("workspaces"),style:{display:"block",width:"100%",padding:"12px 24px",border:"none",background:"workspaces"===i?"#3b82f6":"transparent",color:"workspaces"===i?"white":"#374151",textAlign:"left",cursor:"pointer"},children:"📁 工作空间管理"}),(0,a.jsx)("button",{onClick:()=>c("presets"),style:{display:"block",width:"100%",padding:"12px 24px",border:"none",background:"presets"===i?"#3b82f6":"transparent",color:"presets"===i?"white":"#374151",textAlign:"left",cursor:"pointer"},children:"🚀 预设分组"}),(0,a.jsx)("button",{onClick:()=>c("settings"),style:{display:"block",width:"100%",padding:"12px 24px",border:"none",background:"settings"===i?"#3b82f6":"transparent",color:"settings"===i?"white":"#374151",textAlign:"left",cursor:"pointer"},children:"⚙️ 设置"})]})}),(0,a.jsxs)("div",{style:{flex:1,padding:"24px"},children:["workspaces"===i&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[(0,a.jsx)("h2",{style:{margin:0},children:"工作空间管理"}),(0,a.jsx)("button",{onClick:async()=>{const e=prompt("请输入工作空间名称:");if(!e)return;const t={id:`workspace_${Date.now()}`,name:e,description:"",color:"#3B82F6",icon:"📁",groups:[],createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};await n.saveWorkspace(t),await p()},style:{padding:"8px 16px",background:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer"},children:"+ 新建工作空间"})]}),(0,a.jsx)("div",{style:{display:"grid",gap:"16px"},children:e.map(e=>(0,a.jsx)("div",{style:{padding:"16px",border:"1px solid #e5e7eb",borderRadius:"8px",background:"white"},children:(0,a.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{style:{margin:"0 0 8px 0"},children:[e.icon," ",e.name]}),(0,a.jsxs)("p",{style:{margin:0,color:"#6b7280",fontSize:"14px"},children:[e.groups.length," 个分组，",e.groups.reduce((e,t)=>e+t.tabs.length,0)," 个标签页"]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{onClick:()=>(async e=>{confirm("确定要删除这个工作空间吗？")&&(await n.deleteWorkspace(e),await p())})(e.id),style:{padding:"4px 8px",background:"#ef4444",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:"删除"})})]})},e.id))})]}),"presets"===i&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{children:"预设分组配置"}),(0,a.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px"},children:"管理26个精选AI工具网站的预设分组配置"}),(0,a.jsxs)("div",{style:{display:"grid",gap:"16px"},children:[(0,a.jsxs)("div",{style:{padding:"16px",background:"#f0f9ff",border:"1px solid #0ea5e9",borderRadius:"8px"},children:[(0,a.jsx)("h3",{style:{margin:"0 0 12px 0",color:"#0c4a6e"},children:"🤖 AI工作主力 (6个网站)"}),(0,a.jsx)("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"},children:"ChatGPT、Gemini、LobeHub、Perplexity、Grok、AI Studio"}),(0,a.jsx)("p",{style:{margin:0,fontSize:"12px",color:"#6b7280"},children:"最强大的通用AI助手和对话平台"})]}),(0,a.jsxs)("div",{style:{padding:"16px",background:"#fef3c7",border:"1px solid #f59e0b",borderRadius:"8px"},children:[(0,a.jsx)("h3",{style:{margin:"0 0 12px 0",color:"#92400e"},children:"🔧 AI次选 (6个网站)"}),(0,a.jsx)("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"},children:"DeepAsk、GPTFun、C佬、A佬、H佬、Claude"}),(0,a.jsx)("p",{style:{margin:0,fontSize:"12px",color:"#6b7280"},children:"专业的AI对话工具和助手平台"})]}),(0,a.jsxs)("div",{style:{padding:"16px",background:"#f3e8ff",border:"1px solid #8b5cf6",borderRadius:"8px"},children:[(0,a.jsx)("h3",{style:{margin:"0 0 12px 0",color:"#6b21a8"},children:"🛠️ AI其他工具 (2个网站)"}),(0,a.jsx)("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"},children:"Dify、提示词优化"}),(0,a.jsx)("p",{style:{margin:0,fontSize:"12px",color:"#6b7280"},children:"AI应用开发和提示词优化工具"})]}),(0,a.jsxs)("div",{style:{padding:"16px",background:"#ecfdf5",border:"1px solid: #10b981",borderRadius:"8px"},children:[(0,a.jsx)("h3",{style:{margin:"0 0 12px 0",color:"#065f46"},children:"💬 技术论坛 + 👥 协作工具 (7个网站)"}),(0,a.jsx)("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"},children:"Linux.do、NodeLoc、NodeSeek、小众软件、Follow、语雀、飞书"}),(0,a.jsx)("p",{style:{margin:0,fontSize:"12px",color:"#6b7280"},children:"技术社区讨论和团队协作平台"})]})]}),(0,a.jsxs)("div",{style:{marginTop:"24px",padding:"16px",background:"#f9fafb",border:"1px solid #e5e7eb",borderRadius:"8px"},children:[(0,a.jsx)("h4",{style:{margin:"0 0 12px 0",color:"#374151"},children:"💡 使用说明"}),(0,a.jsxs)("ul",{style:{margin:0,paddingLeft:"20px",color:"#6b7280",fontSize:"14px"},children:[(0,a.jsx)("li",{children:'在弹窗界面点击"预设"标签页来管理预设分组'}),(0,a.jsx)("li",{children:"首次使用需要初始化预设分组到工作空间"}),(0,a.jsx)("li",{children:"可以一键添加所有预设网站到对应分组"}),(0,a.jsx)("li",{children:"支持自动检测当前打开的预设网站"}),(0,a.jsx)("li",{children:"可以批量打开某个分组的所有网站"})]})]})]}),"settings"===i&&r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{children:"设置"}),(0,a.jsxs)("div",{style:{maxWidth:"600px"},children:[(0,a.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,a.jsx)("label",{style:{display:"block",marginBottom:"8px",fontWeight:"500"},children:"主题"}),(0,a.jsxs)("select",{value:r.theme,onChange:e=>o({...r,theme:e.target.value}),style:{padding:"8px 12px",border:"1px solid #d1d5db",borderRadius:"6px",width:"200px"},children:[(0,a.jsx)("option",{value:"light",children:"浅色"}),(0,a.jsx)("option",{value:"dark",children:"深色"}),(0,a.jsx)("option",{value:"auto",children:"跟随系统"})]})]}),(0,a.jsx)("div",{style:{marginBottom:"24px"},children:(0,a.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,a.jsx)("input",{type:"checkbox",checked:r.autoSave,onChange:e=>o({...r,autoSave:e.target.checked})}),"自动保存标签页"]})}),(0,a.jsx)("div",{style:{marginBottom:"24px"},children:(0,a.jsxs)("label",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,a.jsx)("input",{type:"checkbox",checked:r.syncEnabled,onChange:e=>o({...r,syncEnabled:e.target.checked})}),"启用云同步"]})}),(0,a.jsx)("button",{onClick:()=>n.saveUserSettings(r),style:{padding:"8px 16px",background:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer"},children:"保存设置"})]})]})]})]})},c=document.getElementById("root");c?(0,o.H)(c).render((0,a.jsx)(i,{})):console.error("Root container not found")}},r={};function s(e){var o=r[e];if(void 0!==o)return o.exports;var n=r[e]={exports:{}};return t[e](n,n.exports,s),n.exports}s.m=t,e=[],s.O=(t,r,o,n)=>{if(!r){var a=1/0;for(d=0;d<e.length;d++){for(var[r,o,n]=e[d],i=!0,c=0;c<r.length;c++)(!1&n||a>=n)&&Object.keys(s.O).every(e=>s.O[e](r[c]))?r.splice(c--,1):(i=!1,n<a&&(a=n));if(i){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}n=n||0;for(var d=e.length;d>0&&e[d-1][2]>n;d--)e[d]=e[d-1];e[d]=[r,o,n]},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={575:0};s.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[a,i,c]=r,l=0;if(a.some(t=>0!==e[t])){for(o in i)s.o(i,o)&&(s.m[o]=i[o]);if(c)var d=c(s)}for(t&&t(r);l<a.length;l++)n=a[l],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return s.O(d)},r=self.webpackChunkai_workspace_extension=self.webpackChunkai_workspace_extension||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var o=s.O(void 0,[96],()=>s(639));o=s.O(o)})();